"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { BatchEmployee, availableColumns, CellChangeHandler } from "./types";
import { MemoizedCellRenderer } from "./cell";
import PayslipStatusBadge from "../payslip-status-badge";

// Create dynamic columns based on selected columns
export const createDynamicColumns = (
  selectedColumns: string[],
  handleCellChange: CellChangeHandler,
): ColumnDef<BatchEmployee>[] => {
  // Base columns that are always present
  const baseColumns: ColumnDef<BatchEmployee>[] = [
    {
      id: "name",
      accessorFn: (row) => row.name,
      header: "Employee",
      cell: ({ row }) => (
        <div
          className="cursor-pointer text-sm"
          onClick={() => row.original.onEmployeeSelect?.(row.original.id)}
        >
          {row.getValue("name")}
        </div>
      ),
      footer: () => <div className="font-semibold">Totals</div>,
      meta: {
        className: "sticky left-0 z-10",
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="flex justify-center">
          <PayslipStatusBadge status={row.original.status} />
        </div>
      ),
      footer: () => null,
      enableSorting: false, // Disable sorting on status column
    },
  ];

  // Dynamic columns based on selection
  const dynamicColumns: ColumnDef<BatchEmployee>[] = selectedColumns.map(
    (columnId) => {
      const column = availableColumns.find((c) => c.id === columnId);
      return {
        id: columnId,
        accessorFn: (row) => row[columnId] as number,
        header: column?.name || columnId,
        cell: ({ row }) => {
          const isClosed = row.original.status === "closed";
          const value = row.getValue(columnId) as number;

          return (
            <MemoizedCellRenderer
              value={value}
              employeeId={row.original.id}
              columnId={columnId}
              isClosed={isClosed}
              onChange={handleCellChange}
            />
          );
        },
        footer: ({ table }) => {
          // Calculate total for this column
          const filteredData = table.getFilteredRowModel().rows;
          const total = filteredData.reduce((sum, row) => {
            const value = row.getValue(columnId) as number;
            return sum + (value || 0);
          }, 0);
          return (
            <div className="pl-3 text-left font-medium">{total.toFixed(2)}</div>
          );
        },
        enableSorting: false, // Disable sorting on data columns
      };
    },
  );

  return [...baseColumns, ...dynamicColumns];
};
