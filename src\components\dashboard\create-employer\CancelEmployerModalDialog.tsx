import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CancelEmployerModalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onContinue: () => void;
}

export function CancelEmployerModalDialog({
  open,
  onOpenChange,
  onCancel,
  onContinue,
}: CancelEmployerModalDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Cancel Employer Creation?</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel creating this employer? All progress will be lost.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-between">
          <Button variant="secondary" onClick={onContinue}>
            Continue Editing
          </Button>
          <Button variant="destructive" onClick={onCancel}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
