/**
 * Statutory Payment Metadata
 * 
 * This module provides metadata about UK statutory payment types
 * including their effective dates and descriptions.
 * It also provides utility functions to check eligibility based on dates.
 */

import { StatutoryPaymentType, StatutoryPaymentTypeMetadata } from './types';

/**
 * Metadata for all statutory payment types
 * This includes information about when each payment type became effective
 */
export const STATUTORY_PAYMENT_METADATA: StatutoryPaymentTypeMetadata[] = [
  {
    type: StatutoryPaymentType.SSP,
    displayName: 'Statutory Sick Pay',
    description: 'Paid to employees who are unable to work because of illness',
    effectiveFromDate: '1983-04-06' // Introduced in 1983
  },
  {
    type: StatutoryPaymentType.SMP,
    displayName: 'Statutory Maternity Pay',
    description: 'Paid to female employees who are about to have a baby',
    effectiveFromDate: '1987-04-06' // Introduced in 1987
  },
  {
    type: StatutoryPaymentType.SPP,
    displayName: 'Statutory Paternity Pay',
    description: 'Paid to employees whose partner is having a baby or adopting a child',
    effectiveFromDate: '2003-04-06' // Introduced in 2003
  },
  {
    type: StatutoryPaymentType.SAP,
    displayName: 'Statutory Adoption Pay',
    description: 'Paid to employees who are adopting a child',
    effectiveFromDate: '2003-04-06' // Introduced in 2003
  },
  {
    type: StatutoryPaymentType.SPBP,
    displayName: 'Statutory Parental Bereavement Pay',
    description: 'Paid to employees whose child has died',
    effectiveFromDate: '2020-04-06' // Introduced in 2020
  },
  {
    type: StatutoryPaymentType.SHPP,
    displayName: 'Shared Parental Pay',
    description: 'Paid to employees who are sharing parental leave with their partner',
    effectiveFromDate: '2015-04-05' // Introduced in 2015
  }
  // Neonatal Care Pay will be added here when implemented
  // {
  //   type: StatutoryPaymentType.NCP,
  //   displayName: 'Statutory Neonatal Care Pay',
  //   description: 'Paid to employees whose newborn is receiving neonatal care',
  //   effectiveFromDate: '2025-04-06' // To be introduced in 2025/26 tax year
  // }
];

/**
 * Get metadata for a specific statutory payment type
 * 
 * @param type The statutory payment type
 * @returns The metadata for the specified payment type
 */
export function getStatutoryPaymentMetadata(
  type: StatutoryPaymentType
): StatutoryPaymentTypeMetadata | undefined {
  return STATUTORY_PAYMENT_METADATA.find(metadata => metadata.type === type);
}

/**
 * Check if a statutory payment type is available on a specific date
 * 
 * @param type The statutory payment type
 * @param date Optional date to check (ISO format). If not provided, assumes current date
 * @returns True if the payment type is available on the specified date
 */
export function isStatutoryPaymentTypeAvailable(
  type: StatutoryPaymentType,
  date?: string
): boolean {
  const metadata = getStatutoryPaymentMetadata(type);
  if (!metadata) {
    return false;
  }

  // If no date provided, assume it's available (current date check)
  if (!date) {
    return true;
  }

  const checkDate = new Date(date);
  const effectiveFromDate = new Date(metadata.effectiveFromDate);
  
  // Check if date is after effective from date
  if (checkDate < effectiveFromDate) {
    return false;
  }
  
  // Check if date is before effective to date (if specified)
  if (metadata.effectiveToDate) {
    const effectiveToDate = new Date(metadata.effectiveToDate);
    if (checkDate > effectiveToDate) {
      return false;
    }
  }
  
  return true;
}

/**
 * Get all statutory payment types available on a specific date
 * 
 * @param date The date to check (ISO format)
 * @returns Array of available payment types
 */
export function getAvailableStatutoryPaymentTypes(
  date: string
): StatutoryPaymentType[] {
  return STATUTORY_PAYMENT_METADATA
    .filter(metadata => isStatutoryPaymentTypeAvailable(metadata.type, date))
    .map(metadata => metadata.type);
}

/**
 * Get all statutory payment metadata available on a specific date
 * 
 * @param date The date to check (ISO format)
 * @returns Array of available payment type metadata
 */
export function getAvailableStatutoryPaymentMetadata(
  date: string
): StatutoryPaymentTypeMetadata[] {
  return STATUTORY_PAYMENT_METADATA
    .filter(metadata => isStatutoryPaymentTypeAvailable(metadata.type, date));
}
