import { sqliteTable, text, integer, real } from "drizzle-orm/sqlite-core";

export const employer = sqliteTable("employer", {
  id: text("id").primaryKey().notNull(),
  name: text("name").notNull(),
  tradingName: text("trading_name"),
  address1: text("address1"),
  address2: text("address2"),
  address3: text("address3"),
  address4: text("address4"),
  postcode: text("postcode"),
  country: text("country"),

  // --- Start of added fields ---

  // Registration Details (New)
  officeNumber: text("office_number"),
  payeReference: text("paye_reference"),
  accountsOfficeReference: text("accounts_office_reference"),
  hmrcOffice: text("hmrc_office"),
  smallEmployersRelief: integer("small_employers_relief", {
    mode: "boolean",
  }).default(true), // Default from Zod
  companyRegistrationNumber: text("company_registration_number"),
  uniqueTaxReference: text("unique_tax_reference"),
  corporationTaxReference: text("corporation_tax_reference"),
  bacsSUN: text("bacs_sun"),

  // RTI Submission Information
  senderType: text("sender_type").default("Employer"), // Default from Zod
  senderId: text("sender_id"),
  password: text("password"), // WARNING: Consider encryption/secure storage
  contactTitleRTI: text("contact_title_rti").default("Mr"), // Default from Zod
  contactFirstNameRTI: text("contact_first_name_rti"),
  contactLastNameRTI: text("contact_last_name_rti"),
  contactEmailRTI: text("contact_email_rti"),
  contactPhoneRTI: text("contact_phone_rti"),
  contactFaxRTI: text("contact_fax_rti"),

  // Typical Employee Settings
  typicalPayFrequency: text("typical_pay_frequency").default("Monthly"), // Default from Zod
  typicalPayBasis: text("typical_pay_basis").default("Salaried"), // Default from Zod
  typicalPayMethod: text("typical_pay_method").default("DirectDeposit"), // Default from Zod
  typicalLeaveYearStart: text("typical_leave_year_start").default("January"), // Default from Zod
  typicalLeaveCalculationMethod: text(
    "typical_leave_calculation_method",
  ).default("Days"), // Default from Zod
  typicalLeaveEntitlement: real("typical_leave_entitlement").default(28), // Default from Zod
  typicalWorkingDays: text("typical_working_days", { mode: "json" }), // Store as JSON
  typicalHoursWorked: real("typical_hours_worked"), // From UI, not in Zod
  typicalMinimumWageProfile: text("typical_minimum_wage_profile").default(
    "Standard",
  ), // Name from UI, Default from Zod ('minimumWageProfile')

  // Client Details
  contactName: text("contact_name"),
  contactEmail: text("contact_email"),
  contactPhone: text("contact_phone"),
  altContactName: text("alt_contact_name"),
  altContactEmail: text("alt_contact_email"),
  altContactPhone: text("alt_contact_phone"),
  filingTypes: text("filing_types", { mode: "json" }), // Store as JSON
  clientNotes: text("client_notes"), // Renamed from 'notes'

  // --- End of added fields ---

  // Existing fields (if any, like created_at, updated_at - add if needed)
});
