import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";
import { migrate } from "drizzle-orm/libsql/migrator";
import path from "path";
import { app } from "electron";
import { mkdir } from "fs/promises";
import { existsSync } from "fs";

export async function runMigrationsElectron() {
  const userDataPath = app.getPath("userData");
  if (!existsSync(userDataPath)) {
    await mkdir(userDataPath, { recursive: true });
  }

  const dbPath = path.join(userDataPath, "master.db");
  console.log("Master DB Path:", dbPath);

  const client = createClient({
    url: `file:${dbPath}`,
  });

  const db = drizzle(client);

  try {
    // In production, migrations are in dist-electron/migrations/master
    const migrationsFolder =
      process.env.NODE_ENV === "development"
        ? path.join(process.cwd(), "src", "drizzle", "migrations", "master")
        : path.join(__dirname, "..", "migrations", "master");

    console.log("Migrations folder:", migrationsFolder);

    await migrate(db, { migrationsFolder });
    console.log("Master database migrations completed");
  } finally {
    await client.close();
  }
}

export async function runMigrationsCLI() {
  // Use a development database path for CLI migrations
  const devDbPath = path.join(process.cwd(), "dev-data", "master.sqlite");
  const devDbDir = path.dirname(devDbPath);

  if (!existsSync(devDbDir)) {
    await mkdir(devDbDir, { recursive: true });
  }

  console.log("Development DB Path:", devDbPath);

  const client = createClient({
    url: `file:${devDbPath}`,
  });

  const db = drizzle(client);

  try {
    const migrationsFolder = path.join(
      process.cwd(),
      "src",
      "drizzle",
      "migrations",
      "master",
    );
    console.log("Migrations folder:", migrationsFolder);

    await migrate(db, { migrationsFolder });
    console.log("Master database migrations completed");
  } finally {
    await client.close();
  }
}
