"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { LayoutGrid, Edit, Save } from "lucide-react";

interface ViewNavigationProps {
  activeView: string;
  onSwitchToOverview?: () => void;
  onSwitchToBatch?: () => void;
}

const ViewNavigation: React.FC<ViewNavigationProps> = ({
  activeView,
  onSwitchToOverview,
  onSwitchToBatch,
}) => {
  return (
    <div className="flex gap-2">
      <Button
        variant={activeView === "overview" ? "default" : "outline"}
        size="sm"
        onClick={onSwitchToOverview}
        className="min-w-24"
      >
        <LayoutGrid className="mr-2 h-4 w-4" />
        Overview
      </Button>
      <Button
        variant={activeView === "batch" ? "default" : "outline"}
        size="sm"
        onClick={onSwitchToBatch}
        className="min-w-24"
      >
        <Edit className="mr-2 h-4 w-4" />
        Batch Edit
      </Button>
    </div>
  );
};

export default ViewNavigation;
