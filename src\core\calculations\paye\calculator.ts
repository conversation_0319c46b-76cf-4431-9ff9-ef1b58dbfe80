/**
 * UK PAYE (Pay As You Earn) Calculator
 * 
 * Implementation based on HMRC's PAYErout-v23-0 specification.
 * This calculator handles income tax calculations for UK taxpayers, including:
 * - Standard suffix codes, K codes, and special codes (BR, D0, NT, etc.)
 * - Cumulative and non-cumulative (Week 1/Month 1) calculations
 * - Scottish and Welsh tax rates
 * - Personal allowance reduction for high earners
 * - Rounding according to HMRC rules
 */

import { 
    PayeCalculationInput, 
    PayeCalculationResult, 
    PayPeriodType,
    TaxBandBreakdown,
  } from './types';
  import { parseTaxCode, TaxCodeComponents, calculatePeriodValue, calculateTaxFreeAllowance } from './tax-code-parser';
  
  /**
   * Calculate PAYE tax according to HMRC rules
   * @param input The calculation input parameters
   * @returns The calculation result
   */
  export function calculatePaye(input: PayeCalculationInput): PayeCalculationResult {
    try {
      // Validate input
      validateInput(input);
      
      // Get the period factor for annual conversions
      const periodFactor = getPeriodFactor(input.payPeriod);
      
      // Parse the tax code
      const taxCodeComponents = parseTaxCode(input.taxCode);
      
      // Determine if we're using Week 1/Month 1 basis
      // Emergency codes (X/M) must always be treated as non-cumulative per Section 12
      const isNonCumulative = taxCodeComponents.isEmergencyCode || 
                              taxCodeComponents.isNonCumulative || 
                              input.isNonCumulative === true;
      
      // Handle special tax codes
      const specialCodeResult = handleSpecialTaxCodes(input, taxCodeComponents, periodFactor, isNonCumulative);
      if (specialCodeResult) {
        return specialCodeResult;
      }
      
      // Get cumulative values
      const cumulativeValues = calculateCumulativeValues(input, isNonCumulative);
      
      // Get tax bands based on taxpayer status
      const taxBands = getTaxBands(
        input, 
        taxCodeComponents
      );
      
      // Calculate free pay or additional pay based on tax code
      const freePayOrAdditionalPay = calculateFreePayOrAdditionalPay(
        input, 
        cumulativeValues.cumulativePeriodNumber,
        taxCodeComponents
      );
      
      // Calculate taxable pay
      const taxablePayResults = calculateTaxablePay(
        input, 
        freePayOrAdditionalPay.periodValue,
        cumulativeValues,
        taxCodeComponents
      );
      
      // If no tax is due (taxable pay <= 0 and not a K code)
      if (taxablePayResults.taxablePay <= 0 && !taxCodeComponents.isKCode) {
        return createZeroTaxResult(
          input, 
          freePayOrAdditionalPay.periodValue,
          cumulativeValues,
          taxablePayResults,
          taxCodeComponents
        );
      }
      
      // Calculate tax due
      const taxResults = calculateTaxDue(
        input, 
        taxCodeComponents, 
        periodFactor, 
        isNonCumulative, 
        taxablePayResults.taxablePay, 
        taxablePayResults.cumulativeTaxablePay, 
        cumulativeValues
      );
      
      // Apply the regulatory limit if needed
      const taxAfterRegLimit = applyRegulatoryLimit(
        taxResults.periodTaxDue,
        input.grossPay,
        input.benefitsInKind || 0,
        isNonCumulative,
        cumulativeValues.previousTaxDue,
        taxResults.cumulativeTaxDue
      );
      
      // Create the final result
      return {
        grossPay: input.grossPay,
        taxDue: taxAfterRegLimit.periodTaxDue,
        taxFreeAmount: freePayOrAdditionalPay.periodValue,
        taxablePay: taxablePayResults.taxablePay,
        taxBreakdown: taxResults.taxBreakdown,
        effectiveTaxRate: calculateEffectiveTaxRate(taxAfterRegLimit.periodTaxDue, input.grossPay),
        cumulativeGrossPay: cumulativeValues.cumulativeGrossPay,
        cumulativeTaxablePay: taxablePayResults.cumulativeTaxablePay,
        cumulativeTaxDue: taxAfterRegLimit.cumulativeTaxDue,
        taxCodeUsed: input.taxCode,
        emergencyTaxApplied: taxCodeComponents.isEmergencyCode,
        regulatoryLimitApplied: taxAfterRegLimit.regulatoryLimitApplied,
        personalAllowanceReductionApplied: freePayOrAdditionalPay.personalAllowanceReductionApplied
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`PAYE calculation error: ${error.message}`);
      }
      throw new Error('PAYE calculation error: Unknown error occurred');
    }
  }
  
  /**
   * Validate the input parameters
   */
  function validateInput(input: PayeCalculationInput): void {
    if (!input.taxCode) {
      throw new Error('Tax code is required');
    }
    
    if (!input.taxYearConfig) {
      throw new Error('Tax year configuration is required');
    }
    
    if (input.grossPay < 0) {
      throw new Error('Gross pay cannot be negative');
    }
    
    if (!input.payPeriod) {
      throw new Error('Pay period is required');
    }
  }
  
  /**
   * Get the period factor for annual conversions
   */
  function getPeriodFactor(period: PayPeriodType): number {
    switch (period) {
      case PayPeriodType.WEEKLY:
        return 52;
      case PayPeriodType.TWO_WEEKLY:
        return 26;
      case PayPeriodType.FOUR_WEEKLY:
        return 13;
      case PayPeriodType.MONTHLY:
        return 12;
      case PayPeriodType.QUARTERLY:
        return 4;
      case PayPeriodType.BI_ANNUALLY:
        return 2;
      case PayPeriodType.ANNUALLY:
        return 1;
      default:
        throw new Error(`Invalid pay period: ${period}`);
    }
  }
  
  /**
   * Handle special tax codes (BR, D0, D1, NT)
   */
  function handleSpecialTaxCodes(
    input: PayeCalculationInput,
    taxCodeComponents: TaxCodeComponents,
    periodFactor: number,
    isNonCumulative: boolean
  ): PayeCalculationResult | null {
    const code = taxCodeComponents.rawCode;
    
    // Check for special codes
    if (taxCodeComponents.suffix === 'BR' || 
        taxCodeComponents.suffix === 'D0' || 
        taxCodeComponents.suffix === 'D1' || 
        taxCodeComponents.suffix === 'NT') {
      
      // Get the appropriate tax bands based on taxpayer status
      let taxBands;
      let gPointers;
      
      if (taxCodeComponents.isScottishCode || input.isScottishTaxpayer) {
        taxBands = input.taxYearConfig.paye.scottishRates;
        // Scottish rates use different G pointers 
        // For Scottish rates, use the equivalent rates
        // G = Intermediate Rate (21%), G1 = Higher Rate (42%), G2 = Top Rate (47%)
        gPointers = {
          G: 2,  // Index for scottish-intermediate-rate
          G1: 3, // Index for scottish-higher-rate
          G2: 4  // Index for scottish-top-rate
        };
      } else if (taxCodeComponents.isWelshCode || input.isWelshTaxpayer) {
        taxBands = input.taxYearConfig.paye.welshRates;
        // Welsh rates use same G pointers as UK rates but with different array
        gPointers = {
          G: 0,  // Index for WR1 (Basic Rate)
          G1: 1, // Index for WR2 (Higher Rate)
          G2: 2  // Index for WR3 (Additional Rate)
        };
      } else {
        taxBands = input.taxYearConfig.paye.standardRates;
        // Standard UK rates use G pointers from the config
        gPointers = {
          G: input.taxYearConfig.paye.G,
          G1: input.taxYearConfig.paye.G1,
          G2: input.taxYearConfig.paye.G2
        };
      }
      
      let taxRate: number;
      
      // Use G pointers to identify the correct rate band (as per PAYErout Appendices A/B/C)
      switch (taxCodeComponents.suffix) {
        case 'BR':
          // BR code uses the G pointer (basic rate)
          taxRate = taxBands[gPointers.G]?.rate || 0.2; // Fallback to 20% if undefined
          break;
        case 'D0':
          // D0 code uses the G1 pointer (higher rate)
          taxRate = taxBands[gPointers.G1]?.rate || 0.4; // Fallback to 40% if undefined
          break;
        case 'D1':
          // D1 code uses the G2 pointer (additional rate)
          taxRate = taxBands[gPointers.G2]?.rate || 0.45; // Fallback to 45% if undefined
          break;
        case 'NT':
          // NT code means "No Tax"
          return createZeroTaxResult(
            input,
            0,
            calculateCumulativeValues(input, isNonCumulative),
            { taxablePay: 0, cumulativeTaxablePay: 0 },
            taxCodeComponents
          );
        default:
          return null;
      }
      
      // Calculate cumulative values
      const cumulativeValues = calculateCumulativeValues(input, isNonCumulative);
      
      // Determine which pay to use based on cumulative vs non-cumulative
      let payToTax: number;
      let cumulativePayToTax: number;
      
      if (isNonCumulative) {
        // Non-cumulative mode: tax the current pay in isolation
        // Per PAYErout section 9/10 - deal with each payment in isolation
        payToTax = roundDownToWholePounds(input.grossPay);
        cumulativePayToTax = payToTax;
      } else {
        // Cumulative mode: tax the cumulative pay to date
        // Per PAYErout section 5 - tax the cumulative pay to date
        cumulativePayToTax = roundDownToWholePounds(cumulativeValues.cumulativeGrossPay);
        
        // Calculate the period tax due by differencing
        if (input.previousTaxDue !== undefined && input.previousCumulativeGrossPay !== undefined) {
          // First calculate tax on previous cumulative gross pay
          const previousPayTaxed = roundDownToWholePounds(input.previousCumulativeGrossPay);
          const previousTaxCalculation = roundToFourDecimalPlaces(previousPayTaxed * taxRate);
          const previousPreciseTax = roundDownToTwoDecimalPlaces(previousTaxCalculation);
          
          // Then calculate tax on current cumulative gross pay
          const currentTaxCalculation = roundToFourDecimalPlaces(cumulativePayToTax * taxRate);
          const currentPreciseTax = roundDownToTwoDecimalPlaces(currentTaxCalculation);
          
          // Period tax due is the difference
          payToTax = currentPreciseTax - previousPreciseTax;
        } else {
          // No previous tax data, treat as first period
          payToTax = roundToFourDecimalPlaces(cumulativePayToTax * taxRate);
          payToTax = roundDownToTwoDecimalPlaces(payToTax);
        }
      }
      
      // Apply tax rate with 4 decimal precision for intermediate calculation (Definition 11.2)
      let periodTaxDue: number;
      let cumulativeTaxDue: number;
      
      if (isNonCumulative) {
        // For non-cumulative, calculate tax on the rounded down payment
        const preciseTax = roundToFourDecimalPlaces(payToTax * taxRate);
        
        // Final tax due rounded DOWN to nearest penny (Definition 11.4)
        periodTaxDue = roundDownToTwoDecimalPlaces(preciseTax);
        cumulativeTaxDue = periodTaxDue;
      } else {
        // For cumulative, we've already calculated the period tax due
        periodTaxDue = payToTax; // Already properly calculated and rounded
        
        // Cumulative tax due is the sum of previous tax due and current period tax due
        if (input.previousTaxDue !== undefined) {
          const preciseCumulativeTax = roundToFourDecimalPlaces(input.previousTaxDue + periodTaxDue);
          cumulativeTaxDue = roundDownToTwoDecimalPlaces(preciseCumulativeTax);
        } else {
          cumulativeTaxDue = periodTaxDue;
        }
      }
      
      // Apply the regulatory limit (50% maximum deduction)
      // Section 4.5.2: Regulatory limit should ALSO be applied to special tax codes
      const taxAfterRegLimit = applyRegulatoryLimit(
        periodTaxDue,
        input.grossPay,
        input.benefitsInKind || 0,
        isNonCumulative,
        input.previousTaxDue || 0,
        cumulativeTaxDue
      );
      
      const bandName = getBandNameForSpecialCode(taxCodeComponents.suffix, taxCodeComponents.isScottishCode, taxCodeComponents.isWelshCode);
      
      // For display in the tax breakdown, show the actual amount that was taxed
      const amountTaxed = isNonCumulative ? payToTax : cumulativePayToTax;
      
      return {
        grossPay: input.grossPay,
        taxDue: taxAfterRegLimit.periodTaxDue,
        taxFreeAmount: 0,
        taxablePay: isNonCumulative ? payToTax : input.grossPay,
        taxBreakdown: [{
          bandName,
          amount: isNonCumulative ? payToTax : input.grossPay,
          rate: taxRate * 100,
          taxDue: taxAfterRegLimit.periodTaxDue
        }],
        effectiveTaxRate: calculateEffectiveTaxRate(taxAfterRegLimit.periodTaxDue, input.grossPay),
        cumulativeGrossPay: cumulativeValues.cumulativeGrossPay,
        cumulativeTaxablePay: isNonCumulative ? payToTax : cumulativePayToTax,
        cumulativeTaxDue: taxAfterRegLimit.cumulativeTaxDue,
        taxCodeUsed: input.taxCode,
        emergencyTaxApplied: taxCodeComponents.isEmergencyCode,
        regulatoryLimitApplied: taxAfterRegLimit.regulatoryLimitApplied,
        personalAllowanceReductionApplied: false
      };
    }
    
    return null;
  }
  
  /**
   * Get the display name for the tax band used by a special code
   */
  function getBandNameForSpecialCode(suffix: string, isScottishCode: boolean, isWelshCode: boolean): string {
    const prefix = isScottishCode ? 'Scottish ' : isWelshCode ? 'Welsh ' : '';
    
    switch (suffix) {
      case 'BR':
        return `${prefix}Basic Rate`;
      case 'D0':
        return `${prefix}Higher Rate`;
      case 'D1':
        return `${prefix}Additional Rate`;
      default:
        return 'Special Rate';
    }
  }
  
  /**
   * Find a tax rate by name from a list of tax bands
   */
  function findRateByName(bands: any[], name: string): number | undefined {
    const band = bands.find(b => 
      b.name.toLowerCase().includes(name.toLowerCase())
    );
    return band ? band.rate : undefined;
  }
  
  /**
   * Calculate cumulative values based on input and tax code
   */
  function calculateCumulativeValues(
    input: PayeCalculationInput,
    isNonCumulative: boolean
  ): {
    previousPayToDate: number;
    previousTaxablePay: number;
    previousTaxDue: number;
    cumulativeGrossPay: number;
    cumulativePeriodNumber: number;
  } {
    // Handle Week 53+ scenarios according to Section 14 of PAYErout
    let effectivePeriodNumber = input.periodNumber || 1;
    
    // For Week 53+ payments in non-cumulative mode, adjust period numbers
    // Week 53 → treat as Week 1
    // Week 54 → treat as Week 2
    // Week 56 → treat as Week 4
    if (isNonCumulative && effectivePeriodNumber >= 53) {
      if (effectivePeriodNumber === 53) {
        effectivePeriodNumber = 1;
      } else if (effectivePeriodNumber === 54) {
        effectivePeriodNumber = 2;
      } else if (effectivePeriodNumber === 56) {
        effectivePeriodNumber = 4;
      }
    }
    
    // For non-cumulative calculations, we ignore previous values
    if (isNonCumulative) {
      return {
        previousPayToDate: 0,
        previousTaxablePay: 0,
        previousTaxDue: 0,
        cumulativeGrossPay: input.grossPay,
        cumulativePeriodNumber: effectivePeriodNumber
      };
    }
    
    // For cumulative calculations, use the provided previous values or default to 0
    const previousPayToDate = input.previousPayToDate || 0;
    const previousTaxablePay = input.previousTaxablePay || 0;
    const previousTaxDue = input.previousTaxDue || 0;
    
    return {
      previousPayToDate,
      previousTaxablePay,
      previousTaxDue,
      cumulativeGrossPay: roundToFourDecimalPlaces(previousPayToDate + input.grossPay),
      cumulativePeriodNumber: effectivePeriodNumber
    };
  }
  
  /**
   * Calculate free pay or additional pay based on tax code
   */
  function calculateFreePayOrAdditionalPay(
    input: PayeCalculationInput,
    cumulativePeriodNumber: number,
    taxCodeComponents: TaxCodeComponents
  ): {
    annualValue: number;
    periodValue: number;
    personalAllowanceReductionApplied?: boolean;
  } {
    // Get period factor for annual conversions
    const periodFactor = getPeriodFactor(input.payPeriod);
    
    // Calculate annual value from tax code using the tax-code-parser
    // This ensures we properly handle all specific cases like the +9 adjustment
    let annualValue = calculateTaxFreeAllowance(taxCodeComponents);
    let personalAllowanceReductionApplied = false;
    
    // Apply personal allowance reduction for high earners
    if (annualValue > 0) {
      const threshold = input.taxYearConfig.paye.personalAllowanceReductionThreshold || 100000;
      
      // Annualize pay for high earner test
      const annualizedPayToDate = (input.previousPayToDate || 0) + input.grossPay;
      const estimatedAnnualPay = annualizedPayToDate * (periodFactor / cumulativePeriodNumber);
      
      if (estimatedAnnualPay > threshold) {
        // Reduce allowance by £1 for every £2 over threshold
        const excessIncome = estimatedAnnualPay - threshold;
        // Section 4.3.1: Ensure integer division for allowance reduction (no fractional pounds)
        // £1 for every complete £2 over the threshold
        const reduction = Math.min(annualValue, Math.floor(excessIncome / 2));
        annualValue = Math.max(0, annualValue - reduction);
        personalAllowanceReductionApplied = true;
      }
    }
    
    // Calculate period value using the function from tax-code-parser.ts which handles proper rounding
    // and period adjustments according to HMRC specifications
    const isNonCumulative = taxCodeComponents.isNonCumulative || input.isNonCumulative === true;
    const periodValue = calculatePeriodValue(
      annualValue, 
      input.payPeriod, 
      cumulativePeriodNumber,
      !isNonCumulative // Only apply cumulative multiplier if not non-cumulative
    );
    
    // PAYErout Section 4.4.1 and 5.4: Round down to the nearest pound for tax calculation
    const roundedPeriodValue = Math.floor(periodValue);
    
    return {
      annualValue,
      periodValue: roundedPeriodValue, // Return the rounded value
      personalAllowanceReductionApplied
    };
  }
  
  /**
   * Calculate taxable pay
   */
  function calculateTaxablePay(
    input: PayeCalculationInput,
    freePay: number,
    cumulativeValues: {
      previousPayToDate: number;
      previousTaxablePay: number;
      previousTaxDue: number;
      cumulativeGrossPay: number;
      cumulativePeriodNumber: number;
    },
    taxCodeComponents: TaxCodeComponents
  ): {
    taxablePay: number;
    cumulativeTaxablePay: number;
  } {
    // If K code, subtract negative free pay (i.e., add to taxable pay)
    // Otherwise, subtract free pay from gross to get taxable pay
    let cumulativeTaxablePay: number;
    
    // Normalize taxable pay based on K codes
    // K codes represent additional pay to be taxed, not free pay
    if (taxCodeComponents.isKCode) {
      // For K codes, add abs(freePay) to gross pay
      // But ensure it doesn't exceed 50% of gross pay (regulatory limit)
      const maxAdditionalPay = cumulativeValues.cumulativeGrossPay * 0.5;
      const additionalPay = Math.min(Math.abs(freePay), maxAdditionalPay);
      cumulativeTaxablePay = cumulativeValues.cumulativeGrossPay + additionalPay;
    } else {
      // For standard codes, subtract free pay from gross pay
      cumulativeTaxablePay = Math.max(0, cumulativeValues.cumulativeGrossPay - freePay);
    }
    
    // PAYErout Section 4.4.1: Round down to the nearest pound for tax calculation
    cumulativeTaxablePay = Math.floor(cumulativeTaxablePay);
    
    // Calculate taxable pay for this period
    let taxablePay: number;
    
    // Handle non-cumulative calculations
    const isNonCumulative = taxCodeComponents.isNonCumulative || input.isNonCumulative === true;
    if (isNonCumulative) {
      // For non-cumulative, just calculate directly on this period's gross pay
      if (taxCodeComponents.isKCode) {
        // For K codes, add abs(freePay) to gross pay
        // But ensure it doesn't exceed 50% of gross pay (regulatory limit)
        const maxAdditionalPay = input.grossPay * 0.5;
        const additionalPay = Math.min(Math.abs(freePay), maxAdditionalPay);
        taxablePay = input.grossPay + additionalPay;
      } else {
        // For standard codes, subtract free pay from gross pay
        taxablePay = Math.max(0, input.grossPay - freePay);
      }
      
      // PAYErout Section 4.4.1: Round down to the nearest pound for tax calculation
      taxablePay = Math.floor(taxablePay);
    } else {
      // For cumulative, subtract previous taxable pay from cumulative taxable pay
      taxablePay = Math.max(0, cumulativeTaxablePay - Math.floor(cumulativeValues.previousTaxablePay));
    }
    
    return {
      taxablePay,
      cumulativeTaxablePay
    };
  }
  
  /**
   * Create a zero tax result
   */
  function createZeroTaxResult(
    input: PayeCalculationInput,
    freePay: number,
    cumulativeValues: {
      previousPayToDate: number;
      previousTaxablePay: number;
      previousTaxDue: number;
      cumulativeGrossPay: number;
      cumulativePeriodNumber: number;
    },
    taxablePayResults: {
      taxablePay: number;
      cumulativeTaxablePay: number;
    },
    taxCodeComponents: TaxCodeComponents
  ): PayeCalculationResult {
    return {
      grossPay: input.grossPay,
      taxDue: 0,
      taxFreeAmount: freePay,
      taxablePay: 0,
      taxBreakdown: [],
      effectiveTaxRate: 0,
      cumulativeGrossPay: cumulativeValues.cumulativeGrossPay,
      cumulativeTaxablePay: taxablePayResults.cumulativeTaxablePay,
      cumulativeTaxDue: 0,
      taxCodeUsed: input.taxCode,
      emergencyTaxApplied: taxCodeComponents.isEmergencyCode,
      regulatoryLimitApplied: false,
      personalAllowanceReductionApplied: false
    };
  }
  
  /**
   * Get tax bands based on taxpayer status
   */
  function getTaxBands(
    input: PayeCalculationInput,
    taxCodeComponents: TaxCodeComponents
  ): any[] {
    // Get the appropriate tax bands based on taxpayer status
    let taxBands;
    if (taxCodeComponents.isScottishCode || input.isScottishTaxpayer) {
      taxBands = input.taxYearConfig.paye.scottishRates;
    } else if (taxCodeComponents.isWelshCode || input.isWelshTaxpayer) {
      taxBands = input.taxYearConfig.paye.welshRates;
    } else {
      taxBands = input.taxYearConfig.paye.standardRates;
    }
    
    // The thresholds should remain at their annual values
    // They will be adjusted properly in the calculateTaxDue function
    // according to PAYErout Definition 9 and Definition 10
    return taxBands;
  }
  
  /**
   * Calculate tax due
   */
  function calculateTaxDue(
    input: PayeCalculationInput,
    taxCodeComponents: TaxCodeComponents,
    periodFactor: number,
    isNonCumulative: boolean,
    taxablePay: number,
    cumulativeTaxablePay: number,
    cumulativeValues: {
      previousPayToDate: number;
      previousTaxablePay: number;
      previousTaxDue: number;
      cumulativeGrossPay: number;
      cumulativePeriodNumber: number;
    }
  ): {
    periodTaxDue: number;
    cumulativeTaxDue: number;
    taxBreakdown: TaxBandBreakdown[];
  } {
    // Get the appropriate tax bands based on taxpayer status
    const taxBands = getTaxBands(input, taxCodeComponents);
    
    // Calculate tax on each band
    let cumulativeTaxDue = 0;
    const taxBreakdown: TaxBandBreakdown[] = [];
    
    // Sort bands by threshold in ascending order
    const sortedBands = [...taxBands].sort((a, b) => a.threshold - b.threshold);
    
    // Store the previous band's threshold to calculate the width of each band
    const prevThreshold = 0;
    
    // Process each band
    for (let i = 0; i < sortedBands.length; i++) {
      const band = sortedBands[i];
      const nextBand = sortedBands[i + 1];
      
      // Calculate the annual bandwidth
      let bandWidth: number;
      
      if (nextBand) {
        bandWidth = nextBand.threshold - band.threshold;
      } else {
        // For the highest band, the width is effectively infinite
        bandWidth = Number.MAX_SAFE_INTEGER;
      }
      
      // For cumulative calculations, adjust thresholds based on period number (Definition 9.1a)
      // For non-cumulative, just divide by period factor (Definition 10.1a)
      let adjustedThreshold: number;
      let adjustedWidth: number;
      
      if (!isNonCumulative) {
        // PAYErout Definition 9:
        // Threshold = (Annual bandwidth × period number) ÷ period factor
        // Using 4 decimal places without correction to final place
        // Then for Cvalue (Definition 10), round up to nearest pound
        adjustedThreshold = Math.ceil(
          parseFloat(((band.threshold * cumulativeValues.cumulativePeriodNumber) / periodFactor).toFixed(4))
        );
        
        // Calculate adjusted width: either based on next band's adjusted threshold or unlimited
        if (nextBand) {
          const nextAdjustedThreshold = Math.ceil(
            parseFloat(((nextBand.threshold * cumulativeValues.cumulativePeriodNumber) / periodFactor).toFixed(4))
          );
          adjustedWidth = nextAdjustedThreshold - adjustedThreshold;
        } else {
          adjustedWidth = Number.MAX_SAFE_INTEGER;
        }
      } else {
        // PAYErout Definition 10 for non-cumulative:
        // Cvalue = Annual threshold ÷ period factor, rounded up to nearest £1
        adjustedThreshold = Math.ceil(
          parseFloat((band.threshold / periodFactor).toFixed(4))
        );
        
        // Calculate adjusted width: either based on next band's adjusted threshold or unlimited
        if (nextBand) {
          const nextAdjustedThreshold = Math.ceil(
            parseFloat((nextBand.threshold / periodFactor).toFixed(4))
          );
          adjustedWidth = nextAdjustedThreshold - adjustedThreshold;
        } else {
          adjustedWidth = Number.MAX_SAFE_INTEGER;
        }
      }
      
      // Calculate how much taxable pay falls within this band for cumulative calculation
      const amountInBand = Math.max(0, Math.min(cumulativeTaxablePay - adjustedThreshold, adjustedWidth));
      
      if (amountInBand > 0) {
        // Apply the tax rate and store with 4 decimal precision (Definition 11.2)
        const bandTax = roundToFourDecimalPlaces(amountInBand * band.rate);
        cumulativeTaxDue += bandTax;
        
        // For the breakdown, calculate what portion of the current period's pay falls into this band
        // Only add bands where some of the current pay falls into the band
        if (!isNonCumulative) {
          // For cumulative, need to determine what part of this period's pay is in this band
          let previousAmountInBand = 0;
          if (cumulativeValues.previousTaxablePay > adjustedThreshold) {
            previousAmountInBand = Math.min(
              cumulativeValues.previousTaxablePay - adjustedThreshold,
              adjustedWidth
            );
          }
          
          const periodAmountInBand = amountInBand - previousAmountInBand;
          
          if (periodAmountInBand > 0) {
            taxBreakdown.push({
              bandName: band.name,
              amount: roundToTwoDecimalPlaces(periodAmountInBand),
              rate: band.rate * 100,
              taxDue: roundToTwoDecimalPlaces(periodAmountInBand * band.rate)
            });
          }
        } else {
          // For non-cumulative, the current period amount in the band is just amountInBand
          taxBreakdown.push({
            bandName: band.name,
            amount: roundToTwoDecimalPlaces(amountInBand),
            rate: band.rate * 100,
            taxDue: roundToTwoDecimalPlaces(amountInBand * band.rate)
          });
        }
      }
    }
    
    // Final cumulative tax due with 4 decimal precision (Definition 11.3)
    cumulativeTaxDue = roundToFourDecimalPlaces(cumulativeTaxDue);
    
    // Calculate period tax due
    let periodTaxDue: number;
    
    if (!isNonCumulative && cumulativeValues.previousTaxDue !== undefined) {
      // Cumulative: period tax due = cumulative tax due - previous tax due
      // Final rounding DOWN to nearest penny (Definition 11.4)
      periodTaxDue = roundDownToTwoDecimalPlaces(
        cumulativeTaxDue - cumulativeValues.previousTaxDue
      );
    } else {
      // Non-cumulative: period tax due = cumulative tax due
      // Final rounding DOWN to nearest penny (Definition 11.4)
      periodTaxDue = roundDownToTwoDecimalPlaces(cumulativeTaxDue);
    }
    
    return { periodTaxDue, cumulativeTaxDue, taxBreakdown };
  }
  
  /**
   * Apply the regulatory limit (maximum 50% of pay excluding benefits in kind)
   */
  function applyRegulatoryLimit(
    periodTaxDue: number,
    grossPay: number,
    benefitsInKind: number,
    isNonCumulative: boolean,
    previousTaxDue: number,
    cumulativeTaxDue: number
  ): {
    periodTaxDue: number;
    cumulativeTaxDue: number;
    regulatoryLimitApplied: boolean;
  } {
    // Maximum tax deductible is 50% of pay excluding benefits in kind
    const maxRate = 0.5; // 50%
    
    // Check if grossPay - benefitsInKind is negative and set maxTaxDeductible to 0 to prevent invalid deductions
    const netPayForLimit = grossPay - benefitsInKind;
    const maxTaxDeductible = netPayForLimit <= 0 ? 0 : roundToTwoDecimalPlaces(netPayForLimit * maxRate);
    
    let adjustedPeriodTaxDue = periodTaxDue;
    let adjustedCumulativeTaxDue = cumulativeTaxDue;
    let limitApplied = false;
    
    // Section 4.5.2: Regulatory limit should not be applied to refunds (negative periodTaxDue)
    // Only apply regulatory limit if period tax due is positive and exceeds the limit
    if (periodTaxDue > 0 && periodTaxDue > maxTaxDeductible) {
      adjustedPeriodTaxDue = maxTaxDeductible;
      limitApplied = true;
      
      // Adjust cumulative tax due for cumulative codes
      if (!isNonCumulative) {
        adjustedCumulativeTaxDue = previousTaxDue + maxTaxDeductible;
      }
    }
    
    return {
      periodTaxDue: adjustedPeriodTaxDue,
      cumulativeTaxDue: adjustedCumulativeTaxDue,
      regulatoryLimitApplied: limitApplied
    };
  }
  
  /**
   * Calculate effective tax rate
   */
  function calculateEffectiveTaxRate(taxDue: number, grossPay: number): number {
    if (grossPay <= 0) {
      return 0;
    }
    
    return roundToTwoDecimalPlaces((taxDue / grossPay) * 100);
  }

  /**
   * Round down to two decimal places according to HMRC rules
   * "round down the result if necessary to the nearest multiple of 1p below"
   */
  function roundDownToTwoDecimalPlaces(value: number): number {
    // Handle exact penny values correctly to avoid floating-point imprecision issues
    // This ensures 290.40 stays 290.40 and doesn't become 290.39
    
    // First convert to a string with 6 decimal places to capture any floating point imprecision
    const valueStr = value.toFixed(6);
    
    // Extract the pence part of the value (after the decimal point)
    const decimalPart = valueStr.split('.')[1];
    
    // If the pence are exact (00 after the first 2 digits), keep the value as is
    if (decimalPart && decimalPart.substring(2) === '0000') {
      return parseFloat(valueStr.substring(0, valueStr.indexOf('.') + 3));
    }
    
    // Otherwise round down to nearest penny
    return Math.floor(value * 100) / 100;
  }
  
  /**
   * Round to two decimal places according to HMRC rules
   * Used for most financial calculations
   */
  function roundToTwoDecimalPlaces(value: number): number {
    // Use fixed precision to avoid floating point errors
    return parseFloat(value.toFixed(2));
  }
  
  /**
   * Round to four decimal places
   * Used for intermediate tax calculations
   */
  function roundToFourDecimalPlaces(value: number): number {
    // Use fixed precision to avoid floating point errors
    return parseFloat(value.toFixed(4));
  }
  
  /**
   * Round down to whole pounds
   */
  function roundDownToWholePounds(value: number): number {
    return Math.floor(value);
  }