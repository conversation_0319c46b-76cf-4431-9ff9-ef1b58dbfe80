import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export function PortalsTab() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Portal Settings</h2>
      <Card>
        <CardHeader>
          <CardTitle>Employee Self-Service Portal</CardTitle>
          <CardDescription>
            Configure access and features for the employee self-service portal.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Employee Portal</Label>
              <p className="text-sm text-muted-foreground">
                Allow employees to access their payroll information.
              </p>
            </div>
            <Switch />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Payslip Access</Label>
              <p className="text-sm text-muted-foreground">
                Allow employees to view and download their payslips.
              </p>
            </div>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Personal Information Updates</Label>
              <p className="text-sm text-muted-foreground">
                Allow employees to update their personal information.
              </p>
            </div>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Bank Details Management</Label>
              <p className="text-sm text-muted-foreground">
                Allow employees to update their bank details.
              </p>
            </div>
            <Switch />
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Save Portal Settings</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
