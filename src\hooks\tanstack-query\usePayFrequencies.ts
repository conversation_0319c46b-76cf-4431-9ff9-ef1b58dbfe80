import { useQuery } from '@tanstack/react-query';
import { useEmployerDBContext } from '@/providers/employer-db-provider';
import { getUniquePayFrequencies } from '@/services/employerDbService';

/**
 * TanStack Query hook for fetching unique pay frequencies from the employer DB.
 * Returns an array of frequencies, e.g. ["Weekly", "Monthly"].
 */
export function usePayFrequenciesQuery() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbPath;
  return useQuery<string[]>({
    queryKey: ['payFrequencies', dbPath],
    queryFn: async () => {
      if (!dbPath) return [];
      return await getUniquePayFrequencies(dbPath);
    },
    enabled: !!dbPath,
    staleTime: 5 * 60 * 1000,
  });
}
