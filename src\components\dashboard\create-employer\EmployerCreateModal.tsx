// Requires anime.js: npm install animejs
import React, { useState, useEffect, useRef } from "react";
import path from "path-browserify";
import {
  Dialog,
  DialogContent,
  DialogOverlay,
  DialogTitle,
} from "@/components/ui/dialog";
import { CancelEmployerModalDialog } from "./CancelEmployerModalDialog";
import { ResultEmployerModalDialog } from "./ResultEmployerModalDialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { motion, AnimatePresence } from "framer-motion";
import {
  EMPLOYER_DB_EXTENSION,
  EMPLOYER_DB_LOCALSTORAGE_PREFIX,
} from "@/constants/file";

interface EmployerFormData {
  name: string;
  tradingName?: string;
  filePath: string;
}

interface EmployerCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: EmployerFormData) => void;
  defaultFilePath?: string;
}

const steps = ["Welcome", "Employer Details", "Confirmation"];

function sanitizeFileName(input: string): string {
  return input
    .replace(/[\\/:*?"<>|]/g, "") // Remove invalid chars
    .replace(/^\s+|\s+$/g, "") // Trim leading/trailing spaces
    .replace(/[.]+$/g, ""); // Remove trailing periods
}

const EmployerCreateModal: React.FC<EmployerCreateModalProps> = ({
  isOpen,
  onClose,
  onSave,
  defaultFilePath,
}) => {
  // Define employer directory in a single place for use throughout the component
  const employerDirectory =
    "a:/WEBSITES/A1 Payroll Software V2/uk-payroll/test-employers";
  // Standard modal state
  const [step, setStep] = useState(0);
  const [formData, setFormData] = useState<EmployerFormData>({
    name: "",
    tradingName: "",
    filePath: "",
  });
  const [fileNameSource, setFileNameSource] = useState<"name" | "tradingName">(
    "name",
  );
  const [hasSaved, setHasSaved] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [internalOpen, setInternalOpen] = useState(isOpen);
  // Backend/UX states
  const [loading, setLoading] = useState(false);

  // Save handler for creating employer DB and updating master DB
  const handleSave = async () => {
    if (!formData.name.trim()) {
      setValidationError("Employer name is required.");
      return;
    }
    setLoading(true);
    setValidationError(null);
    try {
      // Compute file path
      // TEMPORARY: Hardcode directory for testing createEmployerDb
      // TODO: Remove this and rely on defaultFilePath prop once master DB settings are implemented
      const filePath = path.join(employerDirectory, computedFileName);
      // Prepare employer data (id will be generated in backend)
      const employerData = {
        name: formData.name.trim(),
        tradingName: formData.tradingName?.trim() || undefined,
      };
      const result = await window.api.createEmployerDb({
        filePath,
        employerData,
      });
      if (result.success) {
        setShowSuccessDialog(true);
        setHasSaved(true);
        onSave({ ...formData, filePath });
      } else {
        setErrorMessage(result.error || "Unknown error creating employer.");
        setShowErrorDialog(true);
      }
    } catch (err: any) {
      setErrorMessage(err?.message || String(err));
      setShowErrorDialog(true);
    } finally {
      setLoading(false);
    }
  };
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const computedFileName = `${sanitizeFileName(
    fileNameSource === "tradingName" && formData.tradingName
      ? formData.tradingName
      : formData.name,
  )}${EMPLOYER_DB_EXTENSION}`;

  // Reset all state and clear persisted state
  const resetAll = () => {
    setStep(0);
    setFormData({ name: "", tradingName: "", filePath: "" });
    setFileNameSource("name");
    setHasSaved(false);
    setValidationError(null);
    // Remove only the employer create modal state key from localStorage
    if (typeof window !== "undefined" && window.localStorage) {
      window.localStorage.removeItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_employer_create_modal_state`,
      );
    }
  };

  // Always reset when modal is opened
  useEffect(() => {
    if (isOpen) {
      resetAll();
      setInternalOpen(true);
    }
  }, [isOpen]);

  // Intercept close
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setShowCancelDialog(true);
    }
  };

  const handleCancelConfirm = () => {
    setShowCancelDialog(false);
    setInternalOpen(false);
    resetAll();
    // Actually close modal for parent
    onClose();
  };

  const handleCancelContinue = () => {
    setShowCancelDialog(false);
    setInternalOpen(true);
  };

  useEffect(() => {
    // Keep internal open state in sync with parent
    if (!isOpen) setInternalOpen(false);
  }, [isOpen]);

  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="relative flex h-full flex-col items-center justify-center overflow-hidden">
            <div className="relative z-10 flex flex-col items-center justify-center">
              <h2 className="mb-4 text-2xl font-bold">Create a New Employer</h2>
              <p className="text-muted-foreground mb-8 max-w-xl text-center text-lg">
                Welcome! You only need to enter the employer name to get
                started. You can add more details later in the employer section.
              </p>
              <Button size="lg" className="w-48" onClick={() => setStep(1)}>
                Get Started
              </Button>
            </div>
          </div>
        );
      case 1:
        return (
          <div className="flex h-full flex-col items-center justify-center">
            <div className="w-full max-w-md space-y-6">
              <div>
                <label
                  htmlFor="employerName"
                  className="mb-1 block font-medium"
                >
                  Employer Name <span className="text-red-600">*</span>
                </label>
                <Input
                  id="employerName"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="Enter employer name"
                  required
                  className="w-full"
                />
              </div>
              <div>
                <label htmlFor="tradingName" className="mb-1 block font-medium">
                  Trading Name{" "}
                  <span className="text-muted-foreground">(optional)</span>
                </label>
                <Input
                  id="tradingName"
                  value={formData.tradingName}
                  onChange={(e) =>
                    setFormData({ ...formData, tradingName: e.target.value })
                  }
                  placeholder="Enter trading name"
                  className="w-full"
                />
              </div>
              <div>
                <label className="mb-1 block font-medium">File Name</label>
                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="fileNameSource"
                      value="name"
                      checked={fileNameSource === "name"}
                      onChange={() => setFileNameSource("name")}
                      disabled={!formData.name}
                    />
                    Employer Name
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="fileNameSource"
                      value="tradingName"
                      checked={fileNameSource === "tradingName"}
                      onChange={() => setFileNameSource("tradingName")}
                      disabled={!formData.tradingName}
                    />
                    Trading Name
                  </label>
                </div>
                <div className="text-muted-foreground mt-2">
                  File will be saved as:{" "}
                  <span className="bg-muted rounded px-2 py-1 font-mono">
                    {computedFileName}
                  </span>
                </div>
              </div>
              {validationError && (
                <div className="text-sm text-red-600">{validationError}</div>
              )}
              <div className="flex justify-end gap-2">
                <Button variant="secondary" onClick={() => setStep(0)}>
                  Back
                </Button>
                <Button
                  onClick={() => {
                    if (!formData.name.trim()) {
                      setValidationError("Employer name is required.");
                      return;
                    }
                    setValidationError(null);
                    setStep(2);
                  }}
                  disabled={!formData.name.trim()}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="flex h-full flex-col items-center justify-center">
            <div className="w-full max-w-md space-y-6">
              <h2 className="mb-2 text-xl font-bold">
                Confirm Employer Details
              </h2>
              <div className="mb-2">
                <b>Employer Name:</b> {formData.name}
              </div>
              {formData.tradingName && (
                <div className="mb-2">
                  <b>Trading Name:</b> {formData.tradingName}
                </div>
              )}
              <div className="mb-2">
                <b>File Location:</b>{" "}
                <span className="bg-muted rounded px-2 py-1 font-mono">
                  {(() => {
                    const filePath = path.join(
                      employerDirectory,
                      computedFileName,
                    );
                    return path.dirname(filePath);
                  })()}
                </span>
              </div>
              <div className="mb-2">
                <b>File Name:</b>{" "}
                <span className="bg-muted rounded px-2 py-1 font-mono">
                  {computedFileName}
                </span>
              </div>
              <div className="mt-8 flex flex-row gap-4">
                <Button variant="secondary" onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button
                  variant="default"
                  onClick={handleSave}
                  disabled={loading}
                  autoFocus
                >
                  {loading ? "Creating..." : "Create Employer"}
                </Button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const modalVariants = {
    initial: {
      scale: 0.8,
      opacity: 0,
      boxShadow: "0 0 0px 0px #00B7FFFF",
    },
    animate: {
      scale: [0.8, 1.05, 0.9, 1],
      opacity: 1,
      boxShadow: [
        "0 0 0px 0px #00B7FFFF",
        "0 0 32px 8px #00B7FFFF",
        "0 0 12px 4px #00B7FFFF",
        "0 0 0px 0px #00B7FFFF",
      ],
      transition: {
        duration: 1.0,
        ease: [0.86, -0.01, 0.21, 1.01],
        boxShadow: { duration: 1.0, ease: "easeInOut" },
        scale: { duration: 1.0, ease: "easeOut" },
      },
    },
    exit: { opacity: 0, scale: 0.85, transition: { duration: 0.4 } },
  };

  const contentVariants = {
    initial: { opacity: 0, y: 24 },
    animate: {
      opacity: 1,
      y: 0,
      transition: { delay: 0.2, duration: 0.5, ease: "easeOut" },
    },
    exit: { opacity: 0, y: 24, transition: { duration: 0.2 } },
  };

  return (
    <>
      <Dialog open={internalOpen} onOpenChange={handleOpenChange}>
        <DialogOverlay className="fixed inset-0 z-40 bg-black/70" />
        <DialogContent
          className="bg-background z-50 mx-auto w-[90vw] max-w-[1100px] overflow-hidden rounded-xl p-0 shadow-2xl"
          style={{
            width: "90vw",
            maxWidth: "1100px",
          }}
        >
          <DialogTitle className="sr-only">{steps[step]}</DialogTitle>
          <AnimatePresence>
            {isOpen && (
              <motion.div
                className="bg-background relative mx-auto flex h-[600px] w-full flex-col justify-center"
                style={{
                  borderRadius: "inherit",
                  minHeight: 600,
                  overflow: "hidden",
                }}
                variants={modalVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
                <motion.div
                  className="flex flex-col justify-center"
                  style={{ position: "relative" }}
                  variants={contentVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                >
                  {renderStep()}
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </DialogContent>
      </Dialog>
      <CancelEmployerModalDialog
        open={showCancelDialog}
        onOpenChange={setShowCancelDialog}
        onCancel={handleCancelConfirm}
        onContinue={handleCancelContinue}
      />
      {/* Error Modal Dialog */}
      <ResultEmployerModalDialog
        open={showErrorDialog}
        type="error"
        message={errorMessage}
        onConfirm={() => setShowErrorDialog(false)}
      />
      {/* Success Modal Dialog */}
      <ResultEmployerModalDialog
        open={showSuccessDialog}
        type="success"
        message={
          "The employer database was created successfully! You can now manage this employer's details and payroll."
        }
        onConfirm={() => {
          setShowSuccessDialog(false);
          setInternalOpen(false);
          resetAll();
          onClose();
        }}
      />
    </>
  );
};

export default EmployerCreateModal;
