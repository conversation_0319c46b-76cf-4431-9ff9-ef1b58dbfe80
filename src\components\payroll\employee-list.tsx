"use client";

import React, { useState, useMemo } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import PayslipStatusBadge from "./payslip-status-badge";

interface Employee {
  id: string;
  name: string;
  status: "open" | "closed";
}

interface EmployeeListProps {
  employees: Employee[];
  periodId: string;
  selectedEmployeeId?: string | null;
  onEmployeeSelect: (employeeId: string) => void;
}

const EmployeeList: React.FC<EmployeeListProps> = ({
  employees,
  periodId,
  selectedEmployeeId,
  onEmployeeSelect,
}) => {
  // State for search term
  const [searchTerm, setSearchTerm] = useState("");

  // Filter employees based on search term
  const filteredEmployees = useMemo(() => {
    if (!searchTerm.trim()) return employees;

    const lowerCaseSearch = searchTerm.toLowerCase();
    return employees.filter((employee) =>
      employee.name.toLowerCase().includes(lowerCaseSearch),
    );
  }, [employees, searchTerm]);

  return (
    <div className="space-y-1">
      <div className="relative mb-2">
        <Search className="text-muted-foreground absolute top-1.5 left-2 h-4 w-4" />
        <Input
          type="search"
          placeholder="Search..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <ScrollArea className="overflow-auto">
        <div className="text-sm">
          {filteredEmployees.length === 0 ? (
            <div className="text-muted-foreground p-2 text-center">
              No employees found
            </div>
          ) : (
            filteredEmployees.map((employee) => (
              <div
                key={employee.id}
                className={`flex cursor-pointer items-center justify-between rounded-sm p-2 ${
                  selectedEmployeeId === employee.id
                    ? "bg-slate-200 dark:bg-zinc-700"
                    : "hover:bg-blue-50 dark:hover:bg-zinc-700"
                }`}
                onClick={() => {
                  onEmployeeSelect(employee.id);
                  setSearchTerm(""); // Clear search box when employee is selected
                }}
              >
                <span className="truncate">{employee.name}</span>
                <PayslipStatusBadge
                  status={employee.status}
                  className="ml-2 text-xs"
                />
              </div>
            ))
          )}
        </div>
      </ScrollArea>
      {searchTerm && (
        <div className="mt-1 text-xs">
          Found {filteredEmployees.length} of {employees.length} employees
        </div>
      )}
    </div>
  );
};

export default EmployeeList;
