import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Employer } from "@/lib/schemas/employer";

interface RegistrationDetailsSectionProps {
  employer: Employer;
  onChange: (field: string, value: any) => void;
}

const RegistrationDetailsSection: React.FC<RegistrationDetailsSectionProps> = ({
  employer,
  onChange,
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleCheckboxChange = (field: string, checked: boolean) => {
    onChange(field, checked);
  };

  return (
    <div className="space-y-2 pt-4 w-full">
      {/* PAYE Reference */}
      <div className="grid grid-cols-20 items-center gap-2 w-full">
        <Label
          htmlFor="employerPayeReference"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Employer PAYE Reference
        </Label>
        <div className="col-span-2">
          <Input
            id="officeNumber"
            name="officeNumber"
            value={employer.officeNumber || ""}
            onChange={handleInputChange}
            placeholder="123"
            className="w-full text-sm"
          />
        </div>
        <span className="text-center font-bold">/</span>
        <div className="col-span-3">
          <Input
            id="payeReference"
            name="payeReference"
            value={employer.payeReference || ""}
            onChange={handleInputChange}
            placeholder="A12345"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Accounts Office Reference */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="accountsOfficeReference"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Accounts Office Reference
        </Label>
        <div className="col-span-6">
          <Input
            id="accountsOfficeReference"
            name="accountsOfficeReference"
            value={employer.accountsOfficeReference || ""}
            onChange={handleInputChange}
            placeholder="123PX00123456"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* HMRC Office */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="hmrcOffice"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          HMRC office name
        </Label>
        <div className="col-span-6">
          <Input
            id="hmrcOffice"
            name="hmrcOffice"
            value={employer.hmrcOffice || ""}
            onChange={handleInputChange}
            placeholder="HMRC office name"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Small Employers Relief */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="smallEmployersRelief"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Small Employers&apos; Relief
        </Label>
        <div className="col-span-6 flex items-center space-x-2">
          <Checkbox
            id="smallEmployersRelief"
            checked={!!employer.smallEmployersRelief}
            onCheckedChange={(checked) =>
              handleCheckboxChange("smallEmployersRelief", checked === true)
            }
          />
          <label
            htmlFor="smallEmployersRelief"
            className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Employer qualifies for Small Employers&apos; Relief
          </label>
        </div>
      </div>

      {/* Company Registration Number */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="companyRegistrationNumber"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Company Reg Number
        </Label>
        <div className="col-span-6">
          <Input
            id="companyRegistrationNumber"
            name="companyRegistrationNumber"
            value={employer.companyRegistrationNumber || ""}
            onChange={handleInputChange}
            placeholder="If applicable"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Unique Tax Reference */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="uniqueTaxReference"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Unique Tax Reference
        </Label>
        <div className="col-span-6">
          <Input
            id="uniqueTaxReference"
            name="uniqueTaxReference"
            value={employer.uniqueTaxReference || ""}
            onChange={handleInputChange}
            placeholder="If applicable"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Corporation Tax Reference */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="corporationTaxReference"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Corporation Tax ref
        </Label>
        <div className="col-span-6">
          <Input
            id="corporationTaxReference"
            name="corporationTaxReference"
            value={employer.corporationTaxReference || ""}
            onChange={handleInputChange}
            placeholder="If applicable"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* BACS SUN */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="bacsSUN"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          BACS SUN
        </Label>
        <div className="col-span-6">
          <Input
            id="bacsSUN"
            name="bacsSUN"
            value={employer.bacsSUN || ""}
            onChange={handleInputChange}
            placeholder="If applicable"
            className="w-full text-sm"
          />
        </div>
      </div>
    </div>
  );
};

export default RegistrationDetailsSection;
