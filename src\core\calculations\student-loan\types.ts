/**
 * Types for Student Loan calculations
 * These types define the structure of inputs and outputs for UK Student Loan calculations
 */

import { TaxYearConfig } from '../tax-years/types';

/**
 * Student loan plan types available in the UK
 */
export enum StudentLoanPlanType {
  PLAN_1 = 'plan1',
  PLAN_2 = 'plan2',
  PLAN_4 = 'plan4',
  PLAN_5 = 'plan5',
  POSTGRADUATE_LOAN = 'postgraduate'
}

/**
 * Pay period types
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  TWO_WEEKLY = 'two_weekly',
  FOUR_WEEKLY = 'four_weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BI_ANNUALLY = 'bi_annually',
  ANNUALLY = 'annually'
}

/**
 * Input data for Student Loan calculation
 */
export interface StudentLoanCalculationInput {
  // Gross pay for the period
  grossPay: number;
  
  // Student loan plan type(s) - an employee can be on multiple plans simultaneously
  planTypes: StudentLoanPlanType[];
  
  // Pay period type
  payPeriod: PayPeriodType;
  
  // Tax year configuration
  taxYearConfig: TaxYearConfig;
  
  // Week or month number in the tax year (1-based)
  periodNumber: number;
  
  // Previous earnings in this tax year (for annual calculations)
  previousEarningsInTaxYear?: number;
  
  // Previous student loan deductions in this tax year by plan type
  previousDeductionsByPlan?: Record<StudentLoanPlanType, number>;
}

/**
 * Result of Student Loan calculation
 */
export interface StudentLoanCalculationResult {
  // Total student loan deduction for the period
  totalDeduction: number;
  
  // Breakdown of deductions by plan type
  deductionsByPlan: Record<StudentLoanPlanType, number>;
  
  // Earnings subject to student loan deductions
  earningsSubjectToDeduction: number;
  
  // Thresholds used for each plan
  thresholdsByPlan: Record<StudentLoanPlanType, number>;
  
  // Rates used for each plan
  ratesByPlan: Record<StudentLoanPlanType, number>;
  
  // Cumulative values
  cumulativeGrossPay: number;
  cumulativeDeduction: number;
  cumulativeDeductionsByPlan: Record<StudentLoanPlanType, number>;
}
