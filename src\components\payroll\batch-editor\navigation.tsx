"use client";

import { useCallback } from "react";

// Custom hook for tab navigation in batch editor
export const useTabNavigation = (columnWiseNavigation: boolean) => {
  return useCallback(
    (event: React.KeyboardEvent<HTMLTableElement>) => {
      // Only handle Tab key presses
      if (event.key !== "Tab") return;

      // Get the active element
      const activeElement = document.activeElement as HTMLElement;
      if (!activeElement || activeElement.tagName !== "INPUT") return;

      // Find the current cell and row
      const currentCell = activeElement.closest("td");
      if (!currentCell) return;

      const currentRow = currentCell.closest("tr");
      if (!currentRow) return;

      // Get all rows in the table body
      const allRows = Array.from(document.querySelectorAll("table tbody tr"));
      const rowIndex = allRows.indexOf(currentRow);
      if (rowIndex === -1) return;

      // Get all cells in the current row
      const cellsInCurrentRow = Array.from(currentRow.querySelectorAll("td"));
      const cellIndex = cellsInCurrentRow.indexOf(currentCell);
      if (cellIndex === -1) return;

      // Prevent default tab behavior
      event.preventDefault();

      // Determine the next cell to focus based on navigation mode
      let nextRow: number = 0;
      let nextCellIndex: number = 0;

      // Function to find the next enabled input in the specified direction
      const findNextEnabledInput = (isShiftTab: boolean) => {
        if (columnWiseNavigation) {
          // Column-wise navigation (default): move horizontally, then to next row
          if (isShiftTab) {
            // Shift+Tab: move left
            if (cellIndex > 0) {
              // Move to previous cell in same row
              nextRow = rowIndex;
              nextCellIndex = cellIndex - 1;
            } else {
              // At leftmost cell, move to rightmost cell of previous row
              if (rowIndex > 0) {
                nextRow = rowIndex - 1;
                const prevRowCells = Array.from(
                  allRows[nextRow].querySelectorAll("td"),
                );
                nextCellIndex = prevRowCells.length - 1;
              } else {
                // At first cell of table, wrap to last cell
                nextRow = allRows.length - 1;
                const lastRowCells = Array.from(
                  allRows[nextRow].querySelectorAll("td"),
                );
                nextCellIndex = lastRowCells.length - 1;
              }
            }
          } else {
            // Tab: move right
            if (cellIndex < cellsInCurrentRow.length - 1) {
              // Move to next cell in same row
              nextRow = rowIndex;
              nextCellIndex = cellIndex + 1;
            } else {
              // At rightmost cell, move to leftmost cell of next row
              if (rowIndex < allRows.length - 1) {
                nextRow = rowIndex + 1;
                nextCellIndex = 0;
              } else {
                // At last cell of table, wrap to first cell
                nextRow = 0;
                nextCellIndex = 0;
              }
            }
          }
        } else {
          // Row-wise navigation: move vertically, then to next column
          if (isShiftTab) {
            // Shift+Tab: move up
            if (rowIndex > 0) {
              // Move to same cell in previous row
              nextRow = rowIndex - 1;
              nextCellIndex = cellIndex;
            } else {
              // At top row, move to bottom row of previous column
              if (cellIndex > 0) {
                nextCellIndex = cellIndex - 1;
                nextRow = allRows.length - 1;
              } else {
                // At first cell of table, wrap to last cell
                nextCellIndex = cellsInCurrentRow.length - 1;
                nextRow = allRows.length - 1;
              }
            }
          } else {
            // Tab: move down
            if (rowIndex < allRows.length - 1) {
              // Move to same cell in next row
              nextRow = rowIndex + 1;
              nextCellIndex = cellIndex;
            } else {
              // At bottom row, move to top row of next column
              if (cellIndex < cellsInCurrentRow.length - 1) {
                nextCellIndex = cellIndex + 1;
                nextRow = 0;
              } else {
                // At last cell of table, wrap to first cell
                nextCellIndex = 0;
                nextRow = 0;
              }
            }
          }
        }
      };

      // Set initial direction based on the Tab or Shift+Tab key
      findNextEnabledInput(event.shiftKey);

      // Get the next row and cell
      const nextRowElement = allRows[nextRow];
      if (!nextRowElement) return;

      const nextCells = Array.from(nextRowElement.querySelectorAll("td"));
      if (nextCellIndex >= nextCells.length) {
        // Adjust if the target cell index is out of bounds
        nextCellIndex = nextCells.length - 1;
      }

      const nextCellElement = nextCells[nextCellIndex];
      if (!nextCellElement) return;

      // Find an enabled input in the next cell or subsequent cells
      const findAndFocusNextInput = () => {
        // Check if the next cell has an enabled input
        const nextInput = nextCellElement.querySelector(
          "input:not([disabled])",
        ) as HTMLInputElement;
        if (nextInput) {
          nextInput.focus();
          nextInput.select();
          return true;
        }

        // If no enabled input in this cell, search for the next cell with an enabled input
        let found = false;

        if (event.shiftKey) {
          // For Shift+Tab (backward navigation)
          if (columnWiseNavigation) {
            // Column-wise backward navigation: search from right to left, bottom to top
            // Start from the current row and move upward
            for (let r = nextRow; r >= 0; r--) {
              const rowCells = Array.from(allRows[r].querySelectorAll("td"));
              // If we're on the starting row, start from the cell to the left of the current cell
              // Otherwise, start from the rightmost cell
              const startIdx =
                r === nextRow ? nextCellIndex - 1 : rowCells.length - 1;
              const endIdx = 0;

              // Search from right to left in this row
              for (let c = startIdx; c >= endIdx; c--) {
                if (c < 0) continue; // Skip invalid indices
                const input = rowCells[c].querySelector(
                  "input:not([disabled])",
                ) as HTMLInputElement;
                if (input) {
                  input.focus();
                  input.select();
                  found = true;
                  break;
                }
              }

              if (found) break;
            }

            // If not found in rows above, wrap around to the bottom
            if (!found) {
              for (let r = allRows.length - 1; r > nextRow; r--) {
                const rowCells = Array.from(allRows[r].querySelectorAll("td"));

                for (let c = rowCells.length - 1; c >= 0; c--) {
                  const input = rowCells[c].querySelector(
                    "input:not([disabled])",
                  ) as HTMLInputElement;
                  if (input) {
                    input.focus();
                    input.select();
                    found = true;
                    break;
                  }
                }

                if (found) break;
              }
            }
          } else {
            // Row-wise backward navigation: search from bottom to top, right to left
            // Start from the current column and move leftward
            for (let c = nextCellIndex; c >= 0; c--) {
              // If we're on the starting column, start from the row above the current row
              // Otherwise, start from the bottom row
              const startRow =
                c === nextCellIndex ? nextRow - 1 : allRows.length - 1;

              // Search from bottom to top in this column
              for (let r = startRow; r >= 0; r--) {
                if (r < 0) continue; // Skip invalid indices
                const rowCells = Array.from(allRows[r].querySelectorAll("td"));
                if (c < rowCells.length) {
                  const input = rowCells[c].querySelector(
                    "input:not([disabled])",
                  ) as HTMLInputElement;
                  if (input) {
                    input.focus();
                    input.select();
                    found = true;
                    break;
                  }
                }
              }

              if (found) break;
            }

            // If not found in columns to the left, wrap around to the rightmost column
            if (!found) {
              for (
                let c = cellsInCurrentRow.length - 1;
                c > nextCellIndex;
                c--
              ) {
                for (let r = allRows.length - 1; r >= 0; r--) {
                  const rowCells = Array.from(
                    allRows[r].querySelectorAll("td"),
                  );
                  if (c < rowCells.length) {
                    const input = rowCells[c].querySelector(
                      "input:not([disabled])",
                    ) as HTMLInputElement;
                    if (input) {
                      input.focus();
                      input.select();
                      found = true;
                      break;
                    }
                  }
                }

                if (found) break;
              }
            }
          }
        } else {
          // For Tab (forward navigation)
          if (columnWiseNavigation) {
            // For column-wise, search row by row
            for (let r = nextRow; r < allRows.length; r++) {
              const rowCells = Array.from(allRows[r].querySelectorAll("td"));
              const startIdx = r === nextRow ? nextCellIndex + 1 : 0; // Start from the next cell

              for (let c = startIdx; c < rowCells.length; c++) {
                const input = rowCells[c].querySelector(
                  "input:not([disabled])",
                ) as HTMLInputElement;
                if (input) {
                  input.focus();
                  input.select();
                  found = true;
                  break;
                }
              }

              if (found) break;
            }

            // If we've reached the end, wrap around to the beginning
            if (!found) {
              for (let r = 0; r <= nextRow; r++) {
                const rowCells = Array.from(allRows[r].querySelectorAll("td"));
                const endIdx = r === nextRow ? nextCellIndex : rowCells.length;

                for (let c = 0; c < endIdx; c++) {
                  const input = rowCells[c].querySelector(
                    "input:not([disabled])",
                  ) as HTMLInputElement;
                  if (input) {
                    input.focus();
                    input.select();
                    found = true;
                    break;
                  }
                }

                if (found) break;
              }
            }
          } else {
            // For row-wise, search column by column
            for (let c = nextCellIndex; c < cellsInCurrentRow.length; c++) {
              const startRow = c === nextCellIndex ? nextRow + 1 : 0; // Start from the next row

              for (let r = startRow; r < allRows.length; r++) {
                const rowCells = Array.from(allRows[r].querySelectorAll("td"));
                if (c < rowCells.length) {
                  const input = rowCells[c].querySelector(
                    "input:not([disabled])",
                  ) as HTMLInputElement;
                  if (input) {
                    input.focus();
                    input.select();
                    found = true;
                    break;
                  }
                }
              }

              if (found) break;

              // If we've reached the bottom of a column, check if we need to wrap to the next column
              if (c === nextCellIndex && !found) {
                // Continue to the next column
                continue;
              }
            }

            // If we've reached the end, wrap around to the beginning
            if (!found) {
              for (let c = 0; c <= nextCellIndex; c++) {
                const endRow = c === nextCellIndex ? nextRow : allRows.length;

                for (let r = 0; r < endRow; r++) {
                  const rowCells = Array.from(
                    allRows[r].querySelectorAll("td"),
                  );
                  if (c < rowCells.length) {
                    const input = rowCells[c].querySelector(
                      "input:not([disabled])",
                    ) as HTMLInputElement;
                    if (input) {
                      input.focus();
                      input.select();
                      found = true;
                      break;
                    }
                  }
                }

                if (found) break;
              }
            }
          }
        }

        return found;
      };

      // Try to find and focus the next enabled input
      if (!findAndFocusNextInput()) {
        // If no enabled input found, let the default tab behavior take over
        return;
      }
    },
    [columnWiseNavigation],
  );
};
