import React from "react";
import { Label } from "@/components/ui/label";
import { NumberInput } from "@/components/ui/number-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Employer } from "@/lib/schemas/employer";

interface TypicalEmployeeSectionProps {
  employer: Employer;
  onChange: (field: string, value: any) => void;
  onNestedChange: (parentField: string, field: string, value: any) => void;
}

const TypicalEmployeeSection: React.FC<TypicalEmployeeSectionProps> = ({
  employer,
  onChange,
  onNestedChange,
}) => {
  // Defensive: fallback if typicalWorkingDays is null/undefined
  const typicalWorkingDays = employer.typicalWorkingDays ?? {
    monday: false,
    tuesday: false,
    wednesday: false,
    thursday: false,
    friday: false,
    saturday: false,
    sunday: false,
  };

  const handleSelectChange = (field: string, value: string) => {
    onChange(field, value);
  };

  const handleCheckboxChange = (field: string, checked: boolean) => {
    onChange(field, checked);
  };

  const handleNumberChange = (field: string, value: number | undefined) => {
    onChange(field, value ?? 0);
  };

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    onNestedChange("typicalWorkingDays", day, checked);
  };

  return (
    <div className="w-full space-y-2 pt-4">
      <div className="text-muted-foreground mb-4 px-4 text-sm">
        These settings will be used as the default for new employees. Changes
        will not affect existing employees.
      </div>

      {/* Pay Frequency */}
      <div className="grid w-full grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalPayFrequency"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Pay frequency
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalPayFrequency || ""}
            onValueChange={(value) =>
              handleSelectChange("typicalPayFrequency", value)
            }
          >
            <SelectTrigger id="typicalPayFrequency" className="w-full text-sm">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Weekly">Weekly</SelectItem>
              <SelectItem value="2-Weekly">2-Weekly</SelectItem>
              <SelectItem value="4-Weekly">4-Weekly</SelectItem>
              <SelectItem value="Monthly">Monthly</SelectItem>
              <SelectItem value="Quarterly">Quarterly</SelectItem>

              <SelectItem value="Annually">Annually</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pay Basis */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalPayBasis"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Pay basis
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalPayBasis}
            onValueChange={(value) =>
              handleSelectChange("typicalPayBasis", value)
            }
          >
            <SelectTrigger id="typicalPayBasis" className="w-full text-sm">
              <SelectValue placeholder="Select pay basis" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Salaried">
                Based on a Monthly rate / annual salary
              </SelectItem>
              <SelectItem value="Weekly">
                Based on a Weekly rate / annual salary
              </SelectItem>
              <SelectItem value="Hourly">Based on an Hourly rate</SelectItem>
              <SelectItem value="Daily">Based on a Daily rate</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pay Method */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalPayMethod"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Pay method
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalPayMethod}
            onValueChange={(value) =>
              handleSelectChange("typicalPayMethod", value)
            }
          >
            <SelectTrigger id="typicalPayMethod" className="w-full text-sm">
              <SelectValue placeholder="Select pay method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DirectDeposit">Bank transfer</SelectItem>
              <SelectItem value="Cheque">Cheque</SelectItem>
              <SelectItem value="Cash">Cash</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Leave Year Start */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalLeaveYearStart"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Leave year starts
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalLeaveYearStart || ""}
            onValueChange={(value) =>
              handleSelectChange("typicalLeaveYearStart", value)
            }
          >
            <SelectTrigger
              id="typicalLeaveYearStart"
              className="w-full text-sm"
            >
              <SelectValue placeholder="Select start date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="January">1 January</SelectItem>
              <SelectItem value="April">6 April</SelectItem>
              <SelectItem value="July">1 July</SelectItem>
              <SelectItem value="September">1 September</SelectItem>
              <SelectItem value="Employment">Employment start date</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Leave Calculation Method */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalLeaveCalculationMethod"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Leave calculation method
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalLeaveCalculationMethod}
            onValueChange={(value) =>
              handleSelectChange("typicalLeaveCalculationMethod", value)
            }
          >
            <SelectTrigger
              id="typicalLeaveCalculationMethod"
              className="w-full text-sm"
            >
              <SelectValue placeholder="Select calculation method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Days">
                Set number of annual leave days
              </SelectItem>
              <SelectItem value="Weeks">Weeks per year</SelectItem>
              <SelectItem value="Monthly">Days per month</SelectItem>
              <SelectItem value="Hours">Hours per year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Leave Entitlement */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalLeaveEntitlement"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Leave entitlement
        </Label>
        <div className="col-span-2 flex items-center gap-2">
          <NumberInput
            id="typicalLeaveEntitlement"
            value={employer.typicalLeaveEntitlement}
            onChange={(value) =>
              handleNumberChange("typicalLeaveEntitlement", value)
            }
            decimalPlaces={1}
            min={0}
            allowNegative={false}
            useThousandSeparator={false}
            className="w-full text-sm"
          />
          <span className="w-full text-sm">days</span>
        </div>
        <div className="col-span-2 flex items-center gap-2">
          <NumberInput
            id="typicalLeaveEntitlementWeeks"
            value={
              employer.typicalLeaveEntitlement
                ? employer.typicalLeaveEntitlement / 5
                : 0
            }
            onChange={(value) =>
              handleNumberChange("typicalLeaveEntitlement", (value || 0) * 5)
            }
            decimalPlaces={1}
            min={0}
            allowNegative={false}
            useThousandSeparator={false}
            className="w-full text-sm"
            disabled={employer.typicalLeaveCalculationMethod !== "Weeks"}
          />
          <span className="w-full text-sm">weeks</span>
        </div>
      </div>

      {/* Working Days */}
      <div className="mt-1 grid grid-cols-20 items-start gap-2">
        <Label
          htmlFor="typicalWorkingDays"
          className="col-span-4 mx-4 justify-self-end pt-0.5 text-right font-medium"
        >
          Working days
        </Label>
        <div className="col-span-6 grid grid-cols-2 gap-x-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="monday"
              checked={typicalWorkingDays.monday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("monday", checked === true)
              }
            />
            <label htmlFor="monday" className="w-full text-sm">
              Monday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="tuesday"
              checked={typicalWorkingDays.tuesday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("tuesday", checked === true)
              }
            />
            <label htmlFor="tuesday" className="w-full text-sm">
              Tuesday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="wednesday"
              checked={typicalWorkingDays.wednesday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("wednesday", checked === true)
              }
            />
            <label htmlFor="wednesday" className="w-full text-sm">
              Wednesday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="thursday"
              checked={typicalWorkingDays.thursday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("thursday", checked === true)
              }
            />
            <label htmlFor="thursday" className="w-full text-sm">
              Thursday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="friday"
              checked={typicalWorkingDays.friday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("friday", checked === true)
              }
            />
            <label htmlFor="friday" className="w-full text-sm">
              Friday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="saturday"
              checked={typicalWorkingDays.saturday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("saturday", checked === true)
              }
            />
            <label htmlFor="saturday" className="w-full text-sm">
              Saturday
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="sunday"
              checked={typicalWorkingDays.sunday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("sunday", checked === true)
              }
            />
            <label htmlFor="sunday" className="w-full text-sm">
              Sunday
            </label>
          </div>
        </div>
      </div>

      {/* Minimum Wage Profile */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="typicalMinimumWageProfile"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Minimum wage profile
        </Label>
        <div className="col-span-6">
          <Select
            value={employer.typicalMinimumWageProfile}
            defaultValue={"Standard"}
            onValueChange={(value) =>
              handleSelectChange("typicalMinimumWageProfile", value)
            }
          >
            <SelectTrigger
              id="typicalMinimumWageProfile"
              className="w-full text-sm"
            >
              <SelectValue placeholder="Select minimum wage profile" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Standard">Standard minimum wage</SelectItem>
              <SelectItem value="Apprentice">Apprentice rate</SelectItem>
              <SelectItem value="NotApplicable">Not applicable</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default TypicalEmployeeSection;
