import { app } from "electron";
import { runMigrationsElectron } from "./migrate-master.js";
import path from "path";

// Replace import.meta.url with __filename
if (require.main === module) {
  app.whenReady().then(async () => {
    try {
      await runMigrationsElectron();
      app.quit();
    } catch (error) {
      console.error("Migration failed:", error);
      app.quit();
      process.exit(1);
    }
  });
}
