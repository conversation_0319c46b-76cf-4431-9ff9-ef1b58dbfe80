"use client";

import React, { memo } from "react";
import { NumberInput } from "@/components/ui/number-input";
import { CellChangeHandler } from "./types";

interface MemoizedCellRendererProps {
  value: number;
  employeeId: string;
  columnId: string;
  isClosed: boolean;
  onChange: CellChangeHandler;
}

// Memoized cell component to prevent re-renders
export const MemoizedCellRenderer = memo(
  ({
    value,
    employeeId,
    columnId,
    isClosed,
    onChange,
  }: MemoizedCellRendererProps) => {
    return (
      <NumberInput
        value={value}
        onChange={(newValue) => onChange(employeeId, columnId, newValue)}
        disabled={isClosed}
        className="bg-background dark:bg-background h-7 w-full rounded-sm text-left selection:bg-blue-400 focus-visible:font-semibold"
        decimalPlaces={columnId === "hours" ? 2 : 2}
        allowNegative={true}
        useThousandSeparator={true}
        style={{ textAlign: "left" }}
      />
    );
  },
);

// Add display name for ESLint react/display-name rule
MemoizedCellRenderer.displayName = "MemoizedCellRenderer";
