"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// cli-migrate-employer-db.ts
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const sqlite3_1 = __importDefault(require("sqlite3"));
const sqlite_1 = require("sqlite");
// Fixed employer DB directory
const EMPLOYER_DB_DIR = "A:/WEBSITES/A1 Payroll Software V2/uk-payroll/test-employers";
// Adjust this path to your migrations folder
const MIGRATIONS_DIR = path.resolve(process.cwd(), "dist-electron/migrations/employer");
async function getMigrationFiles() {
    const files = fs.readdirSync(MIGRATIONS_DIR)
        .filter(f => f.endsWith(".sql"))
        .sort(); // Ensure migrations are applied in order
    return files;
}
async function ensureMigrationsTable(db) {
    await db.exec(`
    CREATE TABLE IF NOT EXISTS _drizzle_migrations (
      id TEXT PRIMARY KEY NOT NULL,
      hash TEXT,
      created_at INTEGER
    );
  `);
}
async function getAppliedMigrations(db) {
    const rows = await db.all("SELECT id FROM _drizzle_migrations");
    return new Set(rows.map((r) => r.id));
}
async function applyMigration(db, file, sql) {
    // Split on statement-breakpoint for drizzle-style migration splitting
    const statements = sql.split(/-->\s*statement-breakpoint/).map(s => s.trim()).filter(Boolean);
    for (const stmt of statements) {
        if (stmt)
            await db.exec(stmt);
    }
    await db.run("INSERT INTO _drizzle_migrations (id, hash, created_at) VALUES (?, NULL, strftime('%s','now'))", file);
}
async function runMigrationsForDb(dbPath) {
    const db = await (0, sqlite_1.open)({ filename: dbPath, driver: sqlite3_1.default.Database });
    await ensureMigrationsTable(db);
    const applied = await getAppliedMigrations(db);
    const files = await getMigrationFiles();
    for (const file of files) {
        if (!applied.has(file)) {
            const fullPath = path.join(MIGRATIONS_DIR, file);
            const sql = fs.readFileSync(fullPath, "utf-8");
            console.log(`Applying migration: ${file} to ${dbPath}`);
            await applyMigration(db, file, sql);
        }
        else {
            console.log(`Already applied: ${file} to ${dbPath}`);
        }
    }
    await db.close();
    console.log(`All migrations applied to ${dbPath}!`);
}
async function runMigrationsForAllEmployerDbs() {
    const files = fs.readdirSync(EMPLOYER_DB_DIR)
        .filter(f => f.endsWith(".ukpayroll"));
    if (files.length === 0) {
        console.log("No employer .ukpayroll files found in:", EMPLOYER_DB_DIR);
        return;
    }
    for (const file of files) {
        const dbPath = path.join(EMPLOYER_DB_DIR, file);
        try {
            await runMigrationsForDb(dbPath);
        }
        catch (err) {
            console.error(`Migration failed for ${dbPath}:`, err);
        }
    }
}
runMigrationsForAllEmployerDbs().catch(err => {
    console.error("Migration runner failed:", err);
    process.exit(1);
});
