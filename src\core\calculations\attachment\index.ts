/**
 * Attachment Order Calculation Module
 * 
 * This module exports functions for calculating UK attachment orders
 * according to regulations, including AEOs, CTAOs, DEAs, CMS, and SLAOs.
 */

import { calculateAttachmentOrder } from './calculator';
import {
  AttachmentOrderCalculationInput,
  AttachmentOrderCalculationResult,
  AttachmentOrderType,
  AttachmentOrderPriority,
  AttachmentOrderCalculationMethod,
  PayPeriodType,
  DEARateType
} from './types';

// Export the main calculation function
export { calculateAttachmentOrder };

// Export types
export type {
  AttachmentOrderCalculationInput,
  AttachmentOrderCalculationResult
};

// Export enums
export {
  AttachmentOrderType,
  AttachmentOrderPriority,
  AttachmentOrderCalculationMethod,
  PayPeriodType,
  DEARateType
};
