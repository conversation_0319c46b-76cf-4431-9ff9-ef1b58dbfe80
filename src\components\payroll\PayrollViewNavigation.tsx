import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FileText, Search, Settings } from "lucide-react";

export interface PayrollViewNavigationProps {
  activeView: string;
  onSwitchToOverview?: () => void;
  onSwitchToBatch?: () => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  onSearchTermChange?: (term: string) => void;
  statusFilter: "all" | "open" | "closed";
  setStatusFilter: (filter: "all" | "open" | "closed") => void;
  onStatusFilterChange?: (filter: "all" | "open" | "closed") => void;
  periodDetails: { type: string; number: string; endDate?: string };
  ViewNavigation: React.ComponentType<any>;
}

const PayrollViewNavigation: React.FC<PayrollViewNavigationProps> = ({
  activeView,
  onSwitchToOverview,
  onSwitchToBatch,
  searchTerm,
  setSearchTerm,
  onSearchTermChange,
  statusFilter,
  setStatusFilter,
  onStatusFilterChange,
  periodDetails,
  ViewNavigation,
}) => (
  <div className="w-full">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-1">
        <ViewNavigation
          activeView={activeView}
          onSwitchToOverview={onSwitchToOverview}
          onSwitchToBatch={onSwitchToBatch}
        />
        <div className="relative mr-2 ml-2">
          <Search className="text-muted-foreground absolute top-1.5 left-2.5 h-4 w-4" />
          <Input
            type="search"
            placeholder="Search employees..."
            className="w-[250px] pl-8"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              onSearchTermChange?.(e.target.value);
            }}
          />
        </div>
        <div className="bg-muted/20 flex items-center gap-1 rounded-md border p-1">
          <Button
            size="sm"
            variant={statusFilter === "all" ? "default" : "ghost"}
            onClick={() => {
              setStatusFilter("all");
              onStatusFilterChange?.("all");
            }}
            className="h-7 text-xs"
          >
            All
          </Button>
          <Button
            size="sm"
            variant={statusFilter === "open" ? "default" : "ghost"}
            onClick={() => {
              setStatusFilter("open");
              onStatusFilterChange?.("open");
            }}
            className="h-7 text-xs"
          >
            Open
          </Button>
          <Button
            size="sm"
            variant={statusFilter === "closed" ? "default" : "ghost"}
            onClick={() => {
              setStatusFilter("closed");
              onStatusFilterChange?.("closed");
            }}
            className="h-7 text-xs"
          >
            Closed
          </Button>
        </div>
        <div className="ml-6 flex items-center justify-center text-lg">
          <span>
            <span className="text-xl font-medium">
              {periodDetails.type} {periodDetails.number}
            </span>
            {periodDetails.endDate && (
              <span className="text-muted-foreground">
                {" "}- Ending {periodDetails.endDate}
              </span>
            )}
          </span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <FileText className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Columns
        </Button>
      </div>
    </div>
  </div>
);

export default PayrollViewNavigation;
