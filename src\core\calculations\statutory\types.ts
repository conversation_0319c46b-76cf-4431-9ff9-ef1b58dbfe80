/**
 * Types for Statutory Payments calculations
 * These types define the structure of inputs and outputs for UK statutory payment calculations
 * including Statutory Sick Pay (SSP), Statutory Maternity Pay (SMP), Statutory Paternity Pay (SPP),
 * Statutory Adoption Pay (SAP), Statutory Parental Bereavement Pay (SPBP), and
 * Shared Parental Pay (ShPP).
 */

import { TaxYearConfig } from '../tax-years/types';

/**
 * Statutory payment type
 */
export enum StatutoryPaymentType {
  SSP = 'SSP',
  SMP = 'SMP',
  SPP = 'SPP',
  SAP = 'SAP',
  SPBP = 'SPBP',
  SHPP = 'ShPP'
}

/**
 * Pay period type
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  FORTNIGHTLY = 'fortnightly',
  FOUR_WEEKLY = 'four-weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BI_ANNUALLY = 'bi-annually',
  ANNUALLY = 'annually'
}

/**
 * Metadata about statutory payment types including effective dates
 * This allows for proper handling of payment types that are only
 * available from specific dates
 */
export interface StatutoryPaymentTypeMetadata {
  // The payment type
  type: StatutoryPaymentType;
  
  // Display name for the payment type
  displayName: string;
  
  // Description of the payment type
  description: string;
  
  // The date from which this payment type is available (ISO format)
  effectiveFromDate: string;
  
  // The date until which this payment type is available (ISO format, optional)
  effectiveToDate?: string;
}

/**
 * Statutory payment calculation input
 */
export interface StatutoryPaymentCalculationInput {
  /**
   * Statutory payment type
   */
  paymentType: StatutoryPaymentType;

  /**
   * Average weekly earnings
   */
  averageWeeklyEarnings?: number;

  /**
   * Qualifying days
   */
  qualifyingDays: Date[] | number;

  /**
   * Excluded days
   */
  excludedDays?: Date[] | number;

  /**
   * Weeks to calculate
   */
  weeksToCalculate?: number;

  /**
   * Pay period
   */
  payPeriod?: PayPeriodType;

  /**
   * Payment week (for SMP, SPP, SAP, SPBP, ShPP)
   */
  paymentWeek?: number;

  /**
   * Waiting days served (for SSP)
   */
  waitingDaysServed?: number;
}

/**
 * Statutory payment calculation result
 */
export interface StatutoryPaymentCalculationResult {
  /**
   * Is eligible for payment
   */
  isEligible: boolean;

  /**
   * Ineligibility reason
   */
  ineligibilityReason?: string;

  /**
   * Payment amount
   */
  payment: number;

  /**
   * Daily rate
   */
  dailyRate?: number;

  /**
   * Weekly rate
   */
  weeklyRate?: number;

  /**
   * Paid days
   */
  paidDays?: number;

  /**
   * Qualifying days
   */
  qualifyingDays?: number;

  /**
   * Excluded days
   */
  excludedDays?: number;

  /**
   * Is higher rate period
   */
  inHigherRatePeriod?: boolean;

  /**
   * Is higher rate
   */
  isHigherRate?: boolean;

  /**
   * Higher rate
   */
  higherRate?: number;

  /**
   * Standard rate
   */
  standardRate?: number;

  /**
   * Higher rate weeks
   */
  higherRateWeeks?: number;

  /**
   * Standard rate weeks
   */
  standardRateWeeks?: number;

  /**
   * Average weekly earnings
   */
  averageWeeklyEarnings?: number;

  /**
   * Weeks calculated
   */
  weeksCalculated?: number;

  /**
   * Days paid
   */
  daysPaid?: number;

  /**
   * Cumulative payment
   */
  cumulativePayment?: number;

  /**
   * Cumulative days paid
   */
  cumulativeDaysPaid?: number;

  /**
   * Remaining entitlement weeks
   */
  remainingEntitlementWeeks?: number;

  /**
   * Remaining higher rate weeks
   */
  remainingHigherRateWeeks?: number;

  /**
   * Remaining standard rate weeks
   */
  remainingStandardRateWeeks?: number;

  /**
   * Waiting days applied
   */
  waitingDaysApplied?: boolean;

  /**
   * Waiting days in period
   */
  waitingDaysInPeriod?: number;

  /**
   * Total waiting days served
   */
  totalWaitingDaysServed?: number;

  /**
   * Warnings
   */
  warnings: string[];
}
