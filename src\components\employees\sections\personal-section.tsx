"use client";

import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { UserRound, Plus, Trash2 } from "lucide-react";
import { Employee } from "@/lib/schemas/employee";
import { format, differenceInYears } from "date-fns";

interface PersonalSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
}

const PersonalSection: React.FC<PersonalSectionProps> = ({
  employee,
  onChange,
}) => {
  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    onChange("personal", field, value);

    // Auto-update gender based on title if the title field is changed
    if (field === "title") {
      const titleToGenderMap: Record<string, "Male" | "Female" | null> = {
        Mr: "Male",
        Mrs: "Female",
        Miss: "Female",
        Ms: "Female",
        Dr: null, // Don't change gender for titles that don't clearly indicate gender
        Prof: null, // Don't change gender for titles that don't clearly indicate gender
      };

      const suggestedGender = titleToGenderMap[value];
      if (suggestedGender !== null) {
        onChange("personal", "gender", suggestedGender);
      }
    }
  };

  // Initialize state from employee record to ensure persistence
  type EmailEntry = { type: string; value?: string };
  type PhoneEntry = { type: string; value?: string };

  const [additionalEmails, setAdditionalEmails] = useState<EmailEntry[]>(
    employee.additionalEmails || [],
  );
  const [additionalPhones, setAdditionalPhones] = useState<PhoneEntry[]>(
    employee.additionalPhones || [],
  );

  // Effect to update employee record when additional contacts change
  useEffect(() => {
    // Skip the initial render to prevent update loops

    const handleUpdate = () => {
      handleChange("additionalEmails", additionalEmails);
      handleChange("additionalPhones", additionalPhones);
    };

    // Use a timeout to batch the updates and prevent infinite loops
    const timeoutId = setTimeout(handleUpdate, 0);

    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(additionalEmails), JSON.stringify(additionalPhones)]);

  // Reset local state whenever the employee changes (including when cancel is clicked)
  // This ensures local state matches the current employee data
  useEffect(() => {
    // Always update the email and phone arrays when employee data changes
    // This ensures proper handling of cancel operations from parent component
    setAdditionalEmails(employee.additionalEmails || []);
    setAdditionalPhones(employee.additionalPhones || []);
  }, [employee]);

  // Function to add a new email field
  const addEmailAddress = () => {
    setAdditionalEmails([...additionalEmails, { type: "Personal", value: "" }]);
  };

  // Function to add a new phone field
  const addPhoneNumber = () => {
    setAdditionalPhones([...additionalPhones, { type: "Mobile", value: "" }]);
  };

  // Function to delete an email
  const deleteEmail = (index: number) => {
    const updatedEmails = [...additionalEmails];
    updatedEmails.splice(index, 1);
    setAdditionalEmails(updatedEmails);
  };

  // Function to delete a phone
  const deletePhone = (index: number) => {
    const updatedPhones = [...additionalPhones];
    updatedPhones.splice(index, 1);
    setAdditionalPhones(updatedPhones);
  };

  // Function to update additional email
  const updateAdditionalEmail = (
    index: number,
    field: "type" | "value",
    newValue: string,
  ) => {
    const updatedEmails = [...additionalEmails];
    updatedEmails[index] = { ...updatedEmails[index], [field]: newValue };
    setAdditionalEmails(updatedEmails);
  };

  // Function to update additional phone
  const updateAdditionalPhone = (
    index: number,
    field: "type" | "value",
    newValue: string,
  ) => {
    const updatedPhones = [...additionalPhones];
    updatedPhones[index] = { ...updatedPhones[index], [field]: newValue };
    setAdditionalPhones(updatedPhones);
  };

  // Function to set an email as default (primary)
  const setEmailAsDefault = (index: number) => {
    // Get the values we need
    const emailToPromote = additionalEmails[index];
    const oldPrimaryEmail = employee.email;
    const oldPrimaryType = employee.emailType || "Personal";

    // First update primary email in parent component
    onChange("personal", "email", emailToPromote.value);
    onChange("personal", "emailType", emailToPromote.type);

    // Then update additional emails list
    // 1. Create a copy of all emails except the one being promoted
    const updatedEmails = additionalEmails.filter((_, i) => i !== index);

    // 2. Add the old primary email to the list (if it exists)
    if (oldPrimaryEmail) {
      updatedEmails.push({
        type: oldPrimaryType,
        value: oldPrimaryEmail,
      });
    }

    // 3. Update the state with the new list directly
    onChange("personal", "additionalEmails", updatedEmails);
  };

  // Calculate age based on date of birth
  const calculateAge = (dob: string): number => {
    if (!dob) return 0;
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  return (
    <div className="space-y-1 pt-4">
      {/* Name Fields Row */}
      <div className="grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="title"
          className="col-span-4 mx-4 justify-self-end text-right font-medium"
        >
          Name
        </Label>
        <div className="col-span-2">
          <Select
            value={employee.title || ""}
            onValueChange={(value) => handleChange("title", value)}
          >
            <SelectTrigger id="title">
              <SelectValue placeholder="Select Title" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Mr">Mr</SelectItem>
              <SelectItem value="Mrs">Mrs</SelectItem>
              <SelectItem value="Miss">Miss</SelectItem>
              <SelectItem value="Ms">Ms</SelectItem>
              <SelectItem value="Dr">Dr</SelectItem>
              <SelectItem value="Prof">Prof</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="col-span-3">
          <Input
            id="firstName"
            className="w-full text-sm"
            placeholder="First Name"
            value={employee.firstName || ""}
            onChange={(e) => handleChange("firstName", e.target.value)}
          />
        </div>
        <div className="col-span-3">
          <Input
            id="middleName"
            className="w-full text-sm"
            placeholder="Middle Name"
            value={employee.middleName || ""}
            onChange={(e) => handleChange("middleName", e.target.value)}
          />
        </div>
        <div className="col-span-3">
          <Input
            id="lastName"
            className="w-full text-sm"
            placeholder="Last Name"
            value={employee.lastName || ""}
            onChange={(e) => handleChange("lastName", e.target.value)}
          />
        </div>
      </div>

      {/* Date of Birth and Age Row */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label
          htmlFor="dateOfBirth"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Date of birth
        </Label>
        <div className="col-span-3">
          <Input
            id="dateOfBirth"
            type="date"
            className="text-sm"
            value={employee.dateOfBirth || ""}
            onChange={(e) => handleChange("dateOfBirth", e.target.value)}
          />
        </div>
        <div className="col-span-2 flex items-center gap-2">
          <Label
            htmlFor="age"
            className="col-span-2 justify-self-end text-right font-medium whitespace-nowrap"
          >
            Age
          </Label>
          <Input
            id="age"
            type="text"
            className="w-12 text-sm"
            value={calculateAge(employee.dateOfBirth || "").toString()}
            readOnly
          />
        </div>
      </div>

      {/* Gender Row */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium">
          Gender
        </Label>
        <div className="col-span-3">
          <RadioGroup
            className="flex gap-4"
            value={employee.gender || ""}
            onValueChange={(value) => handleChange("gender", value)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Male" id="gender-male" />
              <Label
                htmlFor="gender-male"
                className="cursor-pointer text-sm font-normal"
              >
                Male
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Female" id="gender-female" />
              <Label
                htmlFor="gender-female"
                className="cursor-pointer text-sm font-normal"
              >
                Female
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {/* Address Section */}
      <div className="grid grid-cols-10 items-start gap-2 pt-10">
        <Label className="col-span-2 mx-4 justify-self-end pt-2 text-right font-medium">
          Address
        </Label>
        <div className="col-span-4 space-y-1">
          <Input
            className="text-sm"
            autoComplete="off"
            placeholder="Address Line 1"
            value={employee.address1 || ""}
            onChange={(e) => handleChange("address1", e.target.value)}
          />
          <Input
            className="text-sm"
            placeholder="Address Line 2"
            value={employee.address2 || ""}
            onChange={(e) => handleChange("address2", e.target.value)}
          />
          <Input
            className="text-sm"
            placeholder="Address Line 3"
            value={employee.address3 || ""}
            onChange={(e) => handleChange("address3", e.target.value)}
          />
          <div className="grid grid-cols-2 gap-2">
            <Input
              className="text-sm"
              placeholder="Address Line 4"
              value={employee.address4 || ""}
              onChange={(e) => handleChange("address4", e.target.value)}
            />
            <Input
              className="text-sm"
              placeholder="Postcode"
              value={employee.postcode || ""}
              onChange={(e) => handleChange("postcode", e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Country Row */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium">
          Country
        </Label>
        <div className="col-span-3">
          <Select
            value={employee.country || ""}
            onValueChange={(value) => handleChange("country", value)}
          >
            <SelectTrigger className="h-7 text-sm">
              <SelectValue placeholder="Select Country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="England">England</SelectItem>
              <SelectItem value="Scotland">Scotland</SelectItem>
              <SelectItem value="Wales">Wales</SelectItem>
              <SelectItem value="Northern Ireland">Northern Ireland</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Email Row */}
      <div className="grid grid-cols-10 items-center gap-2 pt-10">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium whitespace-nowrap">
          Primary Email
        </Label>
        <Select
          value={employee.emailType || "Personal"}
          onValueChange={(value) => handleChange("emailType", value)}
        >
          <SelectTrigger className="col-span-1 h-7 w-full text-sm">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Work">Work</SelectItem>
            <SelectItem value="Personal">Personal</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
        <div className="col-span-4">
          <Input
            className="text-sm"
            value={employee.email || ""}
            onChange={(e) => handleChange("email", e.target.value)}
          />
        </div>
      </div>

      {/* Additional Email Rows */}
      {additionalEmails.map((emailItem, index) => (
        <div
          key={`email-${index}`}
          className="grid grid-cols-10 items-center gap-2 pt-2"
        >
          <div className="col-span-2"></div>
          <Select
            value={emailItem.type}
            onValueChange={(value) =>
              updateAdditionalEmail(index, "type", value)
            }
          >
            <SelectTrigger className="col-span-1 h-7 w-full text-sm">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Work">Work</SelectItem>
              <SelectItem value="Personal">Personal</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
          <div className="col-span-3">
            <Input
              className="text-sm"
              value={emailItem.value || ""}
              onChange={(e) =>
                updateAdditionalEmail(index, "value", e.target.value)
              }
            />
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-red-500"
              onClick={() => deleteEmail(index)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setEmailAsDefault(index)}
              className="h-7 px-2 text-xs"
            >
              Set as primary
            </Button>
          </div>
        </div>
      ))}

      {/* Add Email Button - Moved below all email entries */}
      <div className="grid grid-cols-10 gap-2 justify-self-start">
        <div></div>
        <div className="col-span-2">
          <Button
            variant="link"
            size="sm"
            className="p-0 text-blue-500 dark:text-blue-300"
            onClick={addEmailAddress}
          >
            <Plus className="mr-1 h-3 w-3" /> Add email address
          </Button>
        </div>
      </div>

      {/* Phone Row */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium whitespace-nowrap">
          Phone number(s)
        </Label>
        <Select
          value={employee.phoneType || "Work"}
          onValueChange={(value) => handleChange("phoneType", value)}
        >
          <SelectTrigger className="col-span-1 h-7 w-full text-sm">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Work">Work</SelectItem>
            <SelectItem value="Mobile">Mobile</SelectItem>
            <SelectItem value="Home">Home</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
        <div className="col-span-3">
          <Input
            className="text-sm"
            value={employee.phone || ""}
            onChange={(e) => handleChange("phone", e.target.value)}
          />
        </div>
      </div>

      {/* Additional Phone Rows */}
      {additionalPhones.map((phoneItem, index) => (
        <div
          key={`phone-${index}`}
          className="grid grid-cols-10 items-center gap-2 pt-2"
        >
          <div className="col-span-2"></div>
          <Select
            value={phoneItem.type}
            onValueChange={(value) =>
              updateAdditionalPhone(index, "type", value)
            }
          >
            <SelectTrigger className="col-span-1 h-7 w-full text-sm">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Work">Work</SelectItem>
              <SelectItem value="Mobile">Mobile</SelectItem>
              <SelectItem value="Home">Home</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
          <div className="col-span-3">
            <Input
              className="text-sm"
              value={phoneItem.value || ""}
              onChange={(e) =>
                updateAdditionalPhone(index, "value", e.target.value)
              }
            />
          </div>
          <div>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-red-500"
              onClick={() => deletePhone(index)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}

      {/* Add Phone Button - Moved below all phone entries */}
      <div className="grid grid-cols-10 items-center gap-2 justify-self-start">
        <div></div>
        <div className="col-span-2">
          <Button
            variant="link"
            size="sm"
            className="p-0 text-blue-500 dark:text-blue-300"
            onClick={addPhoneNumber}
          >
            <Plus className="mr-1 h-3 w-3" /> Add phone number
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PersonalSection;
