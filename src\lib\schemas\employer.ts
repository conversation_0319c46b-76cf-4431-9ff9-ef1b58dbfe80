import { z } from "zod";

// Define validation schema for employer data using Zod
export const employerSchema = z.object({
  // System fields
  id: z.string(), // ID is required, aligns with DB primary key

  // Basic Information
  name: z.string().min(1, "Company name is required"),
  tradingName: z.string().optional(),
  address1: z.string().optional(),
  address2: z.string().optional(),
  address3: z.string().optional(),
  address4: z.string().optional(),
  postcode: z.string().optional(),
  country: z.string().default("United Kingdom"),

  // Registration Details
  officeNumber: z.string().optional(),
  payeReference: z.string().optional(),
  accountsOfficeReference: z.string().optional(),
  hmrcOffice: z.string().optional(),
  hmrcPhone: z.string().optional(),
  smallEmployersRelief: z.boolean().default(true),
  benefitTaxAccountingMethod: z.string().default("P11D"),
  companyRegistrationNumber: z.string().optional(),
  uniqueTaxReference: z.string().optional(),
  corporationTaxReference: z.string().optional(),
  bacsSUN: z.string().optional(),

  // Typical Employee Settings
  typicalPayFrequency: z
    .enum(["2-Weekly", "4-Weekly", "Monthly"])
    .default("Monthly"),
  typicalPayBasis: z.string().default("Salaried"),
  typicalPayMethod: z.string().default("DirectDeposit"),
  typicalLeaveYearStart: z.string().default("January"),
  typicalLeaveCalculationMethod: z.string().default("Days"),
  typicalLeaveEntitlement: z.number().default(28),
  typicalHoursWorked: z.number().optional(), // Added from UI

  // Working days
  typicalWorkingDays: z
    .object({
      monday: z.boolean().default(true),
      tuesday: z.boolean().default(true),
      wednesday: z.boolean().default(true),
      thursday: z.boolean().default(true),
      friday: z.boolean().default(true),
      saturday: z.boolean().default(false),
      sunday: z.boolean().default(false),
    })
    .optional()
    .nullable(),

  typicalMinimumWageProfile: z.string().default("Standard"), // Renamed from minimumWageProfile

  // RTI Submission Information
  senderType: z.string().default("Employer"),
  senderId: z.string().optional(),
  password: z.string().optional(),
  contactTitleRTI: z.string().default("Mr"),
  contactFirstNameRTI: z.string().optional(),
  contactLastNameRTI: z.string().optional(),
  contactEmailRTI: z.string().email().optional(),
  contactPhoneRTI: z.string().optional(),
  contactFaxRTI: z.string().optional(),

  // Client Details
  contactName: z.string().optional(),
  contactEmail: z.string().email().optional(),
  contactPhone: z.string().optional(),
  altContactName: z.string().optional(),
  altContactEmail: z.string().email().optional(),
  altContactPhone: z.string().optional(),
  filingTypes: z
    .object({
      finalPays: z.boolean().default(false),
      eps: z.boolean().default(false),
      p60s: z.boolean().default(false),
      p11ds: z.boolean().default(false),
    })
    .optional()
    .nullable(),
  clientDefaultPassword: z.string().optional(),
  clientNotes: z.string().optional(),
});

// Extract the TypeScript type from the Zod schema
export type Employer = z.infer<typeof employerSchema>;

// Default values for new employer
export const defaultEmployer: Employer = {
  id: "",
  name: "",
  tradingName: "",
  address1: "",
  address2: "",
  address3: "",
  address4: "",
  postcode: "",
  country: "United Kingdom",

  officeNumber: "",
  payeReference: "",
  accountsOfficeReference: "",
  hmrcOffice: "",
  hmrcPhone: "",
  smallEmployersRelief: false,
  benefitTaxAccountingMethod: "P11D",
  companyRegistrationNumber: "",
  uniqueTaxReference: "",
  corporationTaxReference: "",
  bacsSUN: "",

  typicalPayFrequency: "2-Weekly",
  typicalPayBasis: "Salaried",
  typicalPayMethod: "DirectDeposit",
  typicalLeaveYearStart: "January",
  typicalLeaveCalculationMethod: "Days",
  typicalLeaveEntitlement: 28, // Corrected default based on Zod schema
  typicalHoursWorked: undefined, // Added

  typicalWorkingDays: {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false,
  },

  typicalMinimumWageProfile: "Standard", // Renamed

  senderType: "Employer",
  senderId: "",
  password: "",
  contactTitleRTI: "Mr",
  contactFirstNameRTI: "",
  contactLastNameRTI: "",
  contactEmailRTI: "",
  contactPhoneRTI: "",
  contactFaxRTI: "",

  contactName: "",
  contactEmail: "",
  contactPhone: "",
  altContactName: "",
  altContactEmail: "",
  altContactPhone: "",
  filingTypes: {
    finalPays: false,
    eps: false,
    p60s: false,
    p11ds: false,
  },
  clientDefaultPassword: "",
  clientNotes: "",
};
