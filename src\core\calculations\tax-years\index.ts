/**
 * Tax Years Module
 * Exports tax year configurations and utility functions
 */

import { TaxYearConfig } from './types';
import { TaxYear2023_2024, getCurrentTaxYearConfig as getCurrentTaxYear2023_2024 } from './tax-year-2023-2024';
import { TaxYear2024_2025, getCurrentTaxYearConfig as getCurrentTaxYear2024_2025 } from './tax-year-2024-2025';

// Export all tax year configurations
export const TaxYears: Record<string, TaxYearConfig> = {
  '2023-2024': TaxYear2023_2024,
  '2024-2025': TaxYear2024_2025
};

/**
 * Get tax year configuration by ID
 * @param taxYearId Tax year ID (e.g., "2023-2024")
 * @returns The tax year configuration or undefined if not found
 */
export function getTaxYearConfig(taxYearId: string): TaxYearConfig | undefined {
  return TaxYears[taxYearId];
}

/**
 * Get the current tax year configuration
 * @returns The current tax year configuration
 */
export function getCurrentTaxYearConfig(): TaxYearConfig {
  // Determine the current tax year based on the current date
  const currentDate = new Date();
  
  // UK tax year runs from April 6 to April 5 of the following year
  // If we're before April 6, we're in the previous tax year
  if (currentDate >= new Date('2024-04-06')) {
    return TaxYear2024_2025;
  } else {
    return TaxYear2023_2024;
  }
}

// Export types
export * from './types';
