/**
 * Pension Calculation Module
 * 
 * This module exports functions for calculating UK pension contributions
 * according to HMRC guidelines and auto-enrollment requirements.
 */

import { calculatePension } from './calculator';
import { 
  PensionCalculationInput, 
  PensionCalculationResult, 
  PensionSchemeType,
  PayPeriodType 
} from './types';

// Export the main calculation function
export { calculatePension };

// Export types
export type { 
  PensionCalculationInput, 
  PensionCalculationResult 
};

// Export enums
export { 
  PensionSchemeType,
  PayPeriodType 
};
