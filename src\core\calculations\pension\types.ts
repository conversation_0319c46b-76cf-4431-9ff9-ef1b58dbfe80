/**
 * Types for Pension calculations
 * These types define the structure of inputs and outputs for UK pension calculations
 */

import { TaxYearConfig } from '../tax-years/types';

/**
 * Pension scheme types in the UK
 */
export enum PensionSchemeType {
  // Relief at source - contributions made from net pay, basic rate tax relief added by pension provider
  RELIEF_AT_SOURCE = 'reliefAtSource',
  
  // Net pay arrangement - contributions made from gross pay before tax is calculated
  NET_PAY = 'netPay',
  
  // Salary sacrifice - employee sacrifices part of salary, employer makes pension contribution
  SALARY_SACRIFICE = 'salarySacrifice'
}

/**
 * Pay period types
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  TWO_WEEKLY = 'two_weekly',
  FOUR_WEEKLY = 'four_weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BI_ANNUALLY = 'bi_annually',
  ANNUALLY = 'annually'
}

/**
 * Input data for Pension calculation
 */
export interface PensionCalculationInput {
  // Gross pay for the period
  grossPay: number;
  
  // Pay period type
  payPeriod: PayPeriodType;
  
  // Tax year configuration
  taxYearConfig: TaxYearConfig;
  
  // Week or month number in the tax year (1-based)
  periodNumber: number;
  
  // Pension scheme type
  schemeType: PensionSchemeType;
  
  // Employee contribution percentage or fixed amount
  employeeContribution: number;
  
  // Whether employee contribution is a percentage (true) or fixed amount (false)
  employeeContributionIsPercentage: boolean;
  
  // Employer contribution percentage or fixed amount
  employerContribution: number;
  
  // Whether employer contribution is a percentage (true) or fixed amount (false)
  employerContributionIsPercentage: boolean;
  
  // Salary for pension calculation (may differ from gross pay for certain elements)
  pensionableSalary?: number;
  
  // Previous pensionable earnings in this tax year
  previousPensionableEarningsInTaxYear?: number;
  
  // Previous employee pension contributions in this tax year
  previousEmployeeContributionsInTaxYear?: number;
  
  // Previous employer pension contributions in this tax year
  previousEmployerContributionsInTaxYear?: number;
  
  // Whether auto-enrollment rules should be applied
  applyAutoEnrollment?: boolean;
  
  // Whether the employee has opted out of auto-enrollment
  hasOptedOut?: boolean;
  
  // Employee age (for auto-enrollment eligibility)
  employeeAge?: number;
  
  // Whether this is a qualifying earnings calculation
  useQualifyingEarnings?: boolean;
  
  // Employee tax rate (for relief at source calculations)
  employeeTaxRate?: number;
}

/**
 * Result of Pension calculation
 */
export interface PensionCalculationResult {
  // Employee pension contribution for the period
  employeeContribution: number;
  
  // Employer pension contribution for the period
  employerContribution: number;
  
  // Total pension contribution for the period
  totalContribution: number;
  
  // Pensionable earnings used for calculation
  pensionableEarnings: number;
  
  // Qualifying earnings (if applicable)
  qualifyingEarnings?: number;
  
  // Tax relief amount (for relief at source schemes)
  taxRelief?: number;
  
  // Whether auto-enrollment was applied
  autoEnrollmentApplied: boolean;
  
  // Auto-enrollment status
  autoEnrollmentStatus?: string;
  
  // Cumulative values
  cumulativePensionableEarnings: number;
  cumulativeEmployeeContribution: number;
  cumulativeEmployerContribution: number;
  cumulativeTotalContribution: number;
  
  // Scheme type used for calculation
  schemeTypeUsed: PensionSchemeType;
}
