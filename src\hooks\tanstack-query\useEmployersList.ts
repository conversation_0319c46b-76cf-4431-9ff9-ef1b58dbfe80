import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

// Define interfaces for employer updates
export interface RemovedEmployer {
  id: string;
  name: string;
  filePath: string;
}

export interface UpdatedEmployer {
  id: string;
  oldName: string;
  newName: string;
}

/**
 * Hook to fetch all employers from the master DB using Drizzle ORM.
 * - Returns all employers as typed by selectEmployerSchema.
 * - Filtering by master DB columns (e.g. status) can be done in the table component.
 * - Filtering by employer DB data (e.g. outstanding RTI, tax code notices) requires additional queries and is not included here.
 * - Supports manual refetch (for refresh button) and refetches after new employer creation.
 * - Returns information about employers that were removed due to missing files.
 */
export function useEmployersList() {
  const [removedEmployers, setRemovedEmployers] = useState<RemovedEmployer[]>(
    [],
  );
  const [updatedEmployers, setUpdatedEmployers] = useState<UpdatedEmployer[]>(
    [],
  );

  const query = useQuery({
    queryKey: ["employers"],
    queryFn: async () => {
      const result = await window.api.getEmployers();

      // Check if we have the new response format with employers data
      if (result && typeof result === "object" && "employers" in result) {
        const typedResult = result as {
          employers: any[];
          removedEmployers?: RemovedEmployer[];
          updatedEmployers?: UpdatedEmployer[];
        };

        // Store removed employers for notification
        if (
          typedResult.removedEmployers &&
          typedResult.removedEmployers.length > 0
        ) {
          setRemovedEmployers(typedResult.removedEmployers);
        } else {
          setRemovedEmployers([]);
        }

        // Store updated employers for notification
        if (
          typedResult.updatedEmployers &&
          typedResult.updatedEmployers.length > 0
        ) {
          setUpdatedEmployers(typedResult.updatedEmployers);
        } else {
          setUpdatedEmployers([]);
        }

        // Map database field names to UI field names
        return typedResult.employers.map((row: any) => ({
          id: row.id,
          name: row.display_name, // Map display_name to name for UI
          paye_ref: row.paye_ref,
          accounts_ref: row.accounts_ref,
          next_pay_date: row.next_pay_date,
          employees: row.employees,
          tax_code_notices: row.tax_code_notices,
          rti_submissions: row.rti_submissions,
          notifications: row.notifications,
          filePath: row.file_path, // Add filePath for DB operations
        }));
      } else {
        // Handle old response format (just an array of employers)
        setRemovedEmployers([]);
        return result.map((row: any) => ({
          id: row.id,
          name: row.display_name, // Map display_name to name for UI
          paye_ref: row.paye_ref,
          accounts_ref: row.accounts_ref,
          next_pay_date: row.next_pay_date,
          employees: row.employees,
          tax_code_notices: row.tax_code_notices,
          rti_submissions: row.rti_submissions,
          notifications: row.notifications,
          filePath: row.file_path, // Add filePath for DB operations
        }));
      }
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    staleTime: 0, // Always fetch fresh on mount
  });

  return {
    ...query,
    removedEmployers,
    updatedEmployers,
    clearRemovedEmployers: () => setRemovedEmployers([]),
    clearUpdatedEmployers: () => setUpdatedEmployers([]),
  };
}
