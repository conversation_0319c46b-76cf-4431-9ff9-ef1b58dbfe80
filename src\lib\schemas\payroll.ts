import { z } from "zod";

// Basic Pay Elements
const payElementBase = z.object({
  id: z.string(),
  isRepeating: z.boolean().default(false),
  zeroizeNext: z.boolean().default(false),
});

const salaryElement = payElementBase.extend({
  type: z.literal("salary"),
  amount: z.number(),
  periodType: z.string(),
});

const dailyElement = payElementBase.extend({
  type: z.literal("daily"),
  rate: z.number(),
  days: z.number(),
  amount: z.number().optional(),
  rateSource: z.enum(["selector", "manual"]).optional(),
  rateLabel: z.string().optional(),
});

const hourlyElement = payElementBase.extend({
  type: z.literal("hourly"),
  rate: z.number(),
  hours: z.number(),
  amount: z.number().optional(),
  rateSource: z.enum(["selector", "manual"]).optional(),
  rateLabel: z.string().optional(),
});

const h2WeeklyElement = payElementBase.extend({
  type: z.literal("h2-Weekly"),
  rate: z.number(),
  hours: z.number(),
  amount: z.number().optional(),
  rateSource: z.enum(["selector", "manual"]).optional(),
  rateLabel: z.string().optional(),
});

const h4WeeklyElement = payElementBase.extend({
  type: z.literal("h4-Weekly"),
  rate: z.number(),
  hours: z.number(),
  amount: z.number().optional(),
  rateSource: z.enum(["selector", "manual"]).optional(),
  rateLabel: z.string().optional(),
});

export const basicPayElement = z.discriminatedUnion("type", [
  salaryElement,
  dailyElement,
  hourlyElement,
  h2WeeklyElement,
  h4WeeklyElement,
]);

export const basicSectionSchema = z.object({
  elements: z.array(basicPayElement),
});

// Additions Section
const additionItem = z.object({
  id: z.string(),
  type: z.string(),
  name: z.string().min(1, "Name is required"),
  amount: z.number(),
  isRepeating: z.boolean().default(false),
  zeroizeNext: z.boolean().default(false),
});

export const additionsSectionSchema = z.object({
  items: z.array(additionItem),
});

// Deductions Section
const deductionItem = z.object({
  id: z.string(),
  type: z.string(),
  name: z.string().min(1, "Name is required"),
  amount: z.number(),
  isRepeating: z.boolean().default(false),
  zeroizeNext: z.boolean().default(false),
});

export const deductionsSectionSchema = z.object({
  items: z.array(deductionItem),
});

// Statutory Section
const statutoryPayItem = z.object({
  id: z.string(),
  type: z.string(),
  name: z.string().min(1, "Name is required"),
  amount: z.number(),
  isRepeating: z.boolean().default(false),
  zeroizeNext: z.boolean().default(false),
  showOnPayslip: z.boolean().default(true),
});

export const statutorySectionSchema = z.object({
  items: z.array(statutoryPayItem),
});

// Pension Section
const pensionItem = z.object({
  id: z.string(),
  type: z.string(),
  name: z.string().min(1, "Name is required"),
  employeeAmount: z.number(),
  employerAmount: z.number(),
  isRepeating: z.boolean().default(false),
  zeroizeNext: z.boolean().default(false),
});

export const pensionSectionSchema = z.object({
  items: z.array(pensionItem),
});

// Notes Section
const noteItem = z.object({
  id: z.string(),
  note: z.string(),
  isRepeating: z.boolean().default(false),
  showOnPayslip: z.boolean().default(true),
});

export const notesSectionSchema = z.object({
  items: z.array(noteItem),
});

// Summary Section
export const summarySectionSchema = z.object({
  totalPay: z.number(),
  totalDeductions: z.number(),
  totalStatutory: z.number(),
  totalPensionEmployee: z.number(),
  totalPensionEmployer: z.number(),
  netPay: z.number(),
});

// Complete Payslip Schema
export const payslipSchema = z.object({
  id: z.string(),
  employeeId: z.string(),
  payPeriodId: z.string(),
  status: z.enum(["draft", "pending", "approved", "paid", "cancelled"]),
  paymentDate: z.string(),
  payPeriodStart: z.string(),
  payPeriodEnd: z.string(),
  basic: basicSectionSchema,
  additions: additionsSectionSchema,
  deductions: deductionsSectionSchema,
  statutory: statutorySectionSchema,
  pension: pensionSectionSchema,
  notes: notesSectionSchema,
  summary: summarySectionSchema,
});

// Extract TypeScript types
export type BasicPayElement = z.infer<typeof basicPayElement>;
export type BasicSectionData = z.infer<typeof basicSectionSchema>;
export type AdditionItem = z.infer<typeof additionItem>;
export type AdditionsSectionData = z.infer<typeof additionsSectionSchema>;
export type DeductionItem = z.infer<typeof deductionItem>;
export type DeductionsSectionData = z.infer<typeof deductionsSectionSchema>;
export type StatutoryPayItem = z.infer<typeof statutoryPayItem>;
export type StatutorySectionData = z.infer<typeof statutorySectionSchema>;
export type PensionItem = z.infer<typeof pensionItem>;
export type PensionSectionData = z.infer<typeof pensionSectionSchema>;
export type NoteItem = z.infer<typeof noteItem>;
export type NotesSectionData = z.infer<typeof notesSectionSchema>;
export type SummarySectionData = z.infer<typeof summarySectionSchema>;
export type Payslip = z.infer<typeof payslipSchema>;

// Default values
export const defaultPayslip: Payslip = {
  id: "",
  employeeId: "",
  payPeriodId: "",
  status: "draft",
  paymentDate: "",
  payPeriodStart: "",
  payPeriodEnd: "",
  basic: { elements: [] },
  additions: { items: [] },
  deductions: { items: [] },
  statutory: { items: [] },
  pension: { items: [] },
  notes: { items: [] },
  summary: {
    totalPay: 0,
    totalDeductions: 0,
    totalStatutory: 0,
    totalPensionEmployee: 0,
    totalPensionEmployer: 0,
    netPay: 0,
  },
};
