/**
 * Web Worker Manager
 * 
 * This module provides a manager for Web Workers that handle intensive payroll calculations.
 * It abstracts the communication with workers and provides a promise-based API.
 */



// Define the message types for worker communication
export interface WorkerRequest {
  id: string;
  type: string;
  payload: any;
}

export interface WorkerResponse {
  id: string;
  type: string;
  payload: any;
  error?: string;
}

// Worker manager class
export class WorkerManager {
  private worker: Worker | null = null;
  private pendingRequests: Map<string, { resolve: (value: any) => void; reject: (reason: any) => void }> = new Map();
  private workerUrl: string;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;
  
  /**
   * Create a new worker manager
   * @param workerUrl - The URL of the worker script
   */
  constructor(workerUrl: string) {
    this.workerUrl = workerUrl;
  }
  
  /**
   * Initialize the worker
   * @returns A promise that resolves when the worker is initialized
   */
  public async initialize(): Promise<void> {
    // If already initialized or initializing, return the existing promise
    if (this.isInitialized) {
      return Promise.resolve();
    }
    
    if (this.initPromise) {
      return this.initPromise;
    }
    
    this.initPromise = new Promise<void>((resolve, reject) => {
      try {
        // Check if Web Workers are supported
        if (typeof Worker === 'undefined') {
          throw new Error('Web Workers are not supported in this environment');
        }
        
        // Create the worker
        this.worker = new Worker(this.workerUrl);
        
        // Set up message handler
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
        
        // Set up error handler
        this.worker.onerror = (error) => {
          console.error('Worker error:', error);
          reject(new Error(`Worker error: ${error.message}`));
        };
        
        this.isInitialized = true;
        resolve();
      } catch (error) {
        reject(error);
      }
    });
    
    return this.initPromise;
  }
  
  /**
   * Send a request to the worker
   * @param type - The type of request
   * @param payload - The request payload
   * @returns A promise that resolves with the worker response
   */
  public async sendRequest<T>(type: string, payload: any): Promise<T> {
    // Initialize the worker if not already initialized
    await this.initialize();
    
    // Generate a unique ID for this request
    const id = Math.random().toString(36).substring(2, 15);
    
    // Create a promise that will be resolved when the worker responds
    const promise = new Promise<T>((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
    });
    
    // Send the request to the worker
    this.worker!.postMessage({
      id,
      type,
      payload
    } as WorkerRequest);
    
    return promise;
  }
  
  /**
   * Handle a message from the worker
   * @param event - The message event
   */
  private handleWorkerMessage(event: MessageEvent): void {
    const response = event.data as WorkerResponse;
    
    // Find the pending request
    const pendingRequest = this.pendingRequests.get(response.id);
    
    if (pendingRequest) {
      // Remove the request from the pending map
      this.pendingRequests.delete(response.id);
      
      // If there was an error, reject the promise
      if (response.error) {
        pendingRequest.reject(new Error(response.error));
      } else {
        // Otherwise, resolve the promise with the payload
        pendingRequest.resolve(response.payload);
      }
    }
  }
  
  /**
   * Terminate the worker
   */
  public terminate(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      this.initPromise = null;
      
      // Reject all pending requests
      for (const [id, { reject }] of this.pendingRequests) {
        reject(new Error('Worker terminated'));
        this.pendingRequests.delete(id);
      }
    }
  }
}

// Create a singleton instance for the payroll calculation worker
let payrollWorkerManager: WorkerManager | null = null;

/**
 * Get the payroll worker manager
 * @returns The payroll worker manager
 */
export function getPayrollWorkerManager(): WorkerManager {
  if (!payrollWorkerManager) {
    // In desktop mode, we need to use a different path for the worker
    const workerUrl = './workers/payroll-worker.js';
    
    payrollWorkerManager = new WorkerManager(workerUrl);
  }
  
  return payrollWorkerManager;
}
