import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Employer } from '@/lib/schemas/employer';

interface BasicInformationSectionProps {
  employer: Employer;
  onChange: (field: string, value: any) => void;
}

const BasicInformationSection: React.FC<BasicInformationSectionProps> = ({ 
  employer, 
  onChange 
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSelectChange = (field: string, value: string) => {
    onChange(field, value);
  };

  return (
    <div className="space-y-2 pt-4 w-full">
      {/* Name Fields Row */}
      <div className="grid grid-cols-20 gap-2 items-center w-full">
        <Label htmlFor="name" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Company Name
        </Label>
        <div className="col-span-6">
          <Input
            id="name"
            name="name"
            value={employer.name || ""}
            onChange={handleInputChange}
            placeholder="Company name"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="tradingName" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Trading Name
        </Label>
        <div className="col-span-6">
          <Input
            id="tradingName"
            name="tradingName"
            value={employer.tradingName || ""}
            onChange={handleInputChange}
            placeholder="Trading name (if different)"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Address Fields */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="address1" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Address
        </Label>
        <div className="col-span-6">
          <Input
            id="address1"
            name="address1"
            value={employer.address1 || ""}
            onChange={handleInputChange}
            placeholder="Address line 1"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <div className="col-span-4"></div>
        <div className="col-span-6">
          <Input
            id="address2"
            name="address2"
            value={employer.address2 || ""}
            onChange={handleInputChange}
            placeholder="Address line 2"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <div className="col-span-4"></div>
        <div className="col-span-6">
          <Input
            id="address3"
            name="address3"
            value={employer.address3 || ""}
            onChange={handleInputChange}
            placeholder="Address line 3"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <div className="col-span-4"></div>
        <div className="col-span-6">
          <Input
            id="address4"
            name="address4"
            value={employer.address4 || ""}
            onChange={handleInputChange}
            placeholder="Address line 4"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="postcode" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Postcode
        </Label>
        <div className="col-span-3">
          <Input
            id="postcode"
            name="postcode"
            value={employer.postcode || ""}
            onChange={handleInputChange}
            placeholder="Postcode"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="country" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Country
        </Label>
        <div className="col-span-3">
          <Select 
            value={employer.country} 
            onValueChange={(value) => handleSelectChange("country", value)}
          >
            <SelectTrigger id="country" className="text-sm">
              <SelectValue placeholder="Select a country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="England">England</SelectItem>
              <SelectItem value="Scotland">Scotland</SelectItem>
              <SelectItem value="Wales">Wales</SelectItem>
              <SelectItem value="Northern Ireland">Northern Ireland</SelectItem>
              <SelectItem value="United Kingdom">United Kingdom</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default BasicInformationSection;
