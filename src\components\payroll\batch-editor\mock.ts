import { BatchEmployee } from "./types";
import { mockEmployees } from "../shared-mock-data";

// Mock data for employees in batch edit mode
export const mockEmployeesForBatch: BatchEmployee[] = mockEmployees.map(
  (emp) => ({
    ...emp,
  }),
);

/* Original mock data - now using shared data
const originalMockEmployees: BatchEmployee[] = [
  {
    id: "1",
    name: "<PERSON>",
    status: "closed",
    salary: 2500,
    hourlyRate: 15.63,
    hours: 160,
    bonus: 150,
    commission: 0,
    deduction: 120,
    pensionEe: 125,
    pensionEr: 250,
  },
  {
    id: "2",
    name: "<PERSON>",
    status: "open",
    salary: 3200,
    hourlyRate: 20,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 160,
    pensionEr: 320,
  },
  {
    id: "3",
    name: "<PERSON>",
    status: "open",
    salary: 1950,
    hourlyRate: 16.25,
    hours: 120,
    bonus: 200,
    commission: 0,
    deduction: 50,
    pensionEe: 97.50,
    pensionEr: 195,
  },
  {
    id: "4",
    name: "<PERSON>",
    status: "open",
    salary: 2800,
    hourlyRate: 17.50,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 140,
    pensionEr: 280,
  },
  {
    id: "5",
    name: "<PERSON>",
    status: "closed",
    salary: 2100,
    hourlyRate: 13.13,
    hours: 160,
    bonus: 300,
    commission: 0,
    deduction: 75,
    pensionEe: 120,
    pensionEr: 240,
  },
  {
    id: "6",
    name: "Sarah Davis",
    status: "open",
    salary: 2750,
    hourlyRate: 17.19,
    hours: 160,
    bonus: 100,
    commission: 50,
    deduction: 80,
    pensionEe: 137.50,
    pensionEr: 275,
  },
  {
    id: "7",
    name: "David Wilson",
    status: "open",
    salary: 3100,
    hourlyRate: 19.38,
    hours: 160,
    bonus: 200,
    commission: 0,
    deduction: 100,
    pensionEe: 155,
    pensionEr: 310,
  },
  {
    id: "8",
    name: "Jennifer Taylor",
    status: "closed",
    salary: 2900,
    hourlyRate: 18.13,
    hours: 160,
    bonus: 0,
    commission: 150,
    deduction: 90,
    pensionEe: 145,
    pensionEr: 290,
  },
  {
    id: "9",
    name: "Thomas Anderson",
    status: "open",
    salary: 3300,
    hourlyRate: 20.63,
    hours: 160,
    bonus: 250,
    commission: 100,
    deduction: 110,
    pensionEe: 165,
    pensionEr: 330,
  },
  {
    id: "10",
    name: "Jessica Martinez",
    status: "open",
    salary: 2600,
    hourlyRate: 16.25,
    hours: 160,
    bonus: 120,
    commission: 0,
    deduction: 85,
    pensionEe: 130,
    pensionEr: 260,
  },
  {
    id: "11",
    name: "Daniel Thompson",
    status: "open",
    salary: 2850,
    hourlyRate: 17.81,
    hours: 160,
    bonus: 175,
    commission: 0,
    deduction: 95,
    pensionEe: 142.50,
    pensionEr: 285,
  },
  {
    id: "12",
    name: "Lisa Garcia",
    status: "closed",
    salary: 3050,
    hourlyRate: 19.06,
    hours: 160,
    bonus: 0,
    commission: 200,
    deduction: 105,
    pensionEe: 152.50,
    pensionEr: 305,
  },
  {
    id: "13",
    name: "Christopher Rodriguez",
    status: "open",
    salary: 2700,
    hourlyRate: 16.88,
    hours: 160,
    bonus: 130,
    commission: 50,
    deduction: 90,
    pensionEe: 135,
    pensionEr: 270,
  },
  {
    id: "14",
    name: "Michelle Lewis",
    status: "open",
    salary: 3150,
    hourlyRate: 19.69,
    hours: 160,
    bonus: 225,
    commission: 0,
    deduction: 100,
    pensionEe: 157.50,
    pensionEr: 315,
  },
  {
    id: "15",
    name: "Andrew Walker",
    status: "closed",
    salary: 2950,
    hourlyRate: 18.44,
    hours: 160,
    bonus: 0,
    commission: 175,
    deduction: 95,
    pensionEe: 147.50,
    pensionEr: 295,
  },
  {
    id: "16",
    name: "Elizabeth Hall",
    status: "open",
    salary: 3250,
    hourlyRate: 20.31,
    hours: 160,
    bonus: 275,
    commission: 0,
    deduction: 110,
    pensionEe: 162.50,
    pensionEr: 325,
  },
  {
    id: "17",
    name: "James Allen",
    status: "open",
    salary: 2550,
    hourlyRate: 15.94,
    hours: 160,
    bonus: 110,
    commission: 40,
    deduction: 80,
    pensionEe: 127.50,
    pensionEr: 255,
  },
  {
    id: "18",
    name: "Patricia Young",
    status: "closed",
    salary: 3000,
    hourlyRate: 18.75,
    hours: 160,
    bonus: 0,
    commission: 190,
    deduction: 100,
    pensionEe: 150,
    pensionEr: 300,
  },
  {
    id: "19",
    name: "Richard Hernandez",
    status: "open",
    salary: 2650,
    hourlyRate: 16.56,
    hours: 160,
    bonus: 125,
    commission: 60,
    deduction: 85,
    pensionEe: 132.50,
    pensionEr: 265,
  },
  {
    id: "20",
    name: "Linda King",
    status: "open",
    salary: 3350,
    hourlyRate: 20.94,
    hours: 160,
    bonus: 300,
    commission: 0,
    deduction: 115,
    pensionEe: 167.50,
    pensionEr: 335,
  },
  {
    id: "21",
    name: "Charles Wright",
    status: "closed",
    salary: 2800,
    hourlyRate: 17.50,
    hours: 160,
    bonus: 0,
    commission: 140,
    deduction: 90,
    pensionEe: 140,
    pensionEr: 280,
  },
  {
    id: "22",
    name: "Barbara Lopez",
    status: "open",
    salary: 3100,
    hourlyRate: 19.38,
    hours: 160,
    bonus: 210,
    commission: 0,
    deduction: 105,
    pensionEe: 155,
    pensionEr: 310,
  },
  {
    id: "23",
    name: "Joseph Hill",
    status: "open",
    salary: 2500,
    hourlyRate: 15.63,
    hours: 160,
    bonus: 100,
    commission: 30,
    deduction: 75,
    pensionEe: 125,
    pensionEr: 250,
  },
  {
    id: "24",
    name: "Susan Scott",
    status: "closed",
    salary: 2950,
    hourlyRate: 18.44,
    hours: 160,
    bonus: 0,
    commission: 160,
    deduction: 95,
    pensionEe: 147.50,
    pensionEr: 295,
  },
  {
    id: "25",
    name: "Paul Green",
    status: "open",
    salary: 2750,
    hourlyRate: 17.19,
    hours: 160,
    bonus: 150,
    commission: 0,
    deduction: 90,
    pensionEe: 137.50,
    pensionEr: 275,
  },
  {
    id: "26",
    name: "Karen Adams",
    status: "open",
    salary: 3200,
    hourlyRate: 20.00,
    hours: 160,
    bonus: 250,
    commission: 0,
    deduction: 110,
    pensionEe: 160,
    pensionEr: 320,
  },
  {
    id: "27",
    name: "Mark Baker",
    status: "closed",
    salary: 2600,
    hourlyRate: 16.25,
    hours: 160,
    bonus: 0,
    commission: 120,
    deduction: 80,
    pensionEe: 130,
    pensionEr: 260,
  },
  {
    id: "28",
    name: "Nancy Gonzalez",
    status: "open",
    salary: 3050,
    hourlyRate: 19.06,
    hours: 160,
    bonus: 190,
    commission: 0,
    deduction: 100,
    pensionEe: 152.50,
    pensionEr: 305,
  },
  {
    id: "29",
    name: "George Nelson",
    status: "open",
    salary: 2850,
    hourlyRate: 17.81,
    hours: 160,
    bonus: 170,
    commission: 50,
    deduction: 95,
    pensionEe: 142.50,
    pensionEr: 285,
  },
  {
    id: "30",
    name: "Donna Carter",
    status: "closed",
    salary: 3150,
    hourlyRate: 19.69,
    hours: 160,
    bonus: 0,
    commission: 200,
    deduction: 105,
    pensionEe: 157.50,
    pensionEr: 315,
  },
  {
    id: "31",
    name: "Steven Mitchell",
    status: "open",
    salary: 2700,
    hourlyRate: 16.88,
    hours: 160,
    bonus: 130,
    commission: 0,
    deduction: 85,
    pensionEe: 135,
    pensionEr: 270,
  },
  {
    id: "32",
    name: "Carol Perez",
    status: "open",
    salary: 3300,
    hourlyRate: 20.63,
    hours: 160,
    bonus: 280,
    commission: 0,
    deduction: 115,
    pensionEe: 165,
    pensionEr: 330,
  },
  {
    id: "33",
    name: "Edward Roberts",
    status: "closed",
    salary: 2550,
    hourlyRate: 15.94,
    hours: 160,
    bonus: 0,
    commission: 110,
    deduction: 75,
    pensionEe: 127.50,
    pensionEr: 255,
  },
  {
    id: "34",
    name: "Sandra Turner",
    status: "open",
    salary: 3000,
    hourlyRate: 18.75,
    hours: 160,
    bonus: 180,
    commission: 0,
    deduction: 100,
    pensionEe: 150,
    pensionEr: 300,
  },
  {
    id: "35",
    name: "Kenneth Phillips",
    status: "open",
    salary: 2900,
    hourlyRate: 18.13,
    hours: 160,
    bonus: 175,
    commission: 60,
    deduction: 95,
    pensionEe: 145,
    pensionEr: 290,
  },
];
*/
