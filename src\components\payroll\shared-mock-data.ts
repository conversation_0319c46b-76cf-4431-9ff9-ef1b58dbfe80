import { BatchEmployee } from "./batch-editor/types";

// Mock data for employees that can be used by both batch editor and payroll overview
export const mockEmployees = [
  {
    id: "1",
    name: "<PERSON>",
    status: "closed",
    salary: 2500,
    hourlyRate: 15.63,
    hours: 160,
    bonus: 150,
    commission: 0,
    deduction: 120,
    pensionEe: 125,
    pensionEr: 250,
  },
  {
    id: "2",
    name: "<PERSON>",
    status: "open",
    salary: 3200,
    hourlyRate: 20,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 160,
    pensionEr: 320,
  },
  {
    id: "3",
    name: "<PERSON>",
    status: "open",
    salary: 1950,
    hourlyRate: 16.25,
    hours: 120,
    bonus: 200,
    commission: 0,
    deduction: 50,
    pensionEe: 97.5,
    pensionEr: 195,
  },
  {
    id: "4",
    name: "<PERSON>",
    status: "open",
    salary: 2800,
    hourlyRate: 17.5,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 140,
    pensionEr: 280,
  },
  {
    id: "5",
    name: "<PERSON>",
    status: "closed",
    salary: 2100,
    hourlyRate: 13.13,
    hours: 160,
    bonus: 300,
    commission: 0,
    deduction: 75,
    pensionEe: 120,
    pensionEr: 240,
  },
  {
    id: "6",
    name: "<PERSON>",
    status: "open",
    salary: 3100,
    hourlyRate: 19.38,
    hours: 160,
    bonus: 210,
    commission: 0,
    deduction: 105,
    pensionEe: 155,
    pensionEr: 310,
  },
  {
    id: "7",
    name: "Carol Perez",
    status: "open",
    salary: 3300,
    hourlyRate: 20.63,
    hours: 160,
    bonus: 280,
    commission: 0,
    deduction: 115,
    pensionEe: 165,
    pensionEr: 330,
  },
  {
    id: "8",
    name: "Christopher Rodriguez",
    status: "open",
    salary: 2700,
    hourlyRate: 16.88,
    hours: 160,
    bonus: 130,
    commission: 50,
    deduction: 90,
    pensionEe: 135,
    pensionEr: 270,
  },
  {
    id: "9",
    name: "Daniel Thompson",
    status: "open",
    salary: 2850,
    hourlyRate: 17.81,
    hours: 160,
    bonus: 175,
    commission: 0,
    deduction: 95,
    pensionEe: 142.5,
    pensionEr: 285,
  },
  {
    id: "10",
    name: "David Wilson",
    status: "open",
    salary: 3100,
    hourlyRate: 19.38,
    hours: 160,
    bonus: 200,
    commission: 0,
    deduction: 100,
    pensionEe: 155,
    pensionEr: 310,
  },
  {
    id: "11",
    name: "Elizabeth Hall",
    status: "open",
    salary: 3250,
    hourlyRate: 20.31,
    hours: 160,
    bonus: 275,
    commission: 0,
    deduction: 110,
    pensionEe: 162.5,
    pensionEr: 325,
  },
  {
    id: "12",
    name: "Emily Williams",
    status: "open",
    salary: 2800,
    hourlyRate: 17.5,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 140,
    pensionEr: 280,
  },
  {
    id: "13",
    name: "George Nelson",
    status: "open",
    salary: 2850,
    hourlyRate: 17.81,
    hours: 160,
    bonus: 170,
    commission: 50,
    deduction: 95,
    pensionEe: 142.5,
    pensionEr: 285,
  },
  {
    id: "14",
    name: "James Allen",
    status: "open",
    salary: 2550,
    hourlyRate: 15.94,
    hours: 160,
    bonus: 110,
    commission: 40,
    deduction: 80,
    pensionEe: 127.5,
    pensionEr: 255,
  },
  {
    id: "15",
    name: "Jane Doe",
    status: "open",
    salary: 3200,
    hourlyRate: 20,
    hours: 160,
    bonus: 0,
    commission: 0,
    deduction: 0,
    pensionEe: 160,
    pensionEr: 320,
  },
  // Additional employees for testing scrolling
  {
    id: "16",
    name: "Jessica Martinez",
    status: "open",
    salary: 2600,
    hourlyRate: 16.25,
    hours: 160,
    bonus: 120,
    commission: 0,
    deduction: 85,
    pensionEe: 130,
    pensionEr: 260,
  },
  {
    id: "17",
    name: "Joseph Hill",
    status: "open",
    salary: 2500,
    hourlyRate: 15.63,
    hours: 160,
    bonus: 100,
    commission: 30,
    deduction: 75,
    pensionEe: 125,
    pensionEr: 250,
  },
  {
    id: "18",
    name: "Karen Adams",
    status: "open",
    salary: 3200,
    hourlyRate: 20.0,
    hours: 160,
    bonus: 250,
    commission: 0,
    deduction: 110,
    pensionEe: 160,
    pensionEr: 320,
  },
  {
    id: "19",
    name: "Kenneth Phillips",
    status: "open",
    salary: 2900,
    hourlyRate: 18.13,
    hours: 160,
    bonus: 175,
    commission: 60,
    deduction: 95,
    pensionEe: 145,
    pensionEr: 290,
  },
  {
    id: "20",
    name: "Linda King",
    status: "open",
    salary: 3350,
    hourlyRate: 20.94,
    hours: 160,
    bonus: 300,
    commission: 0,
    deduction: 115,
    pensionEe: 167.5,
    pensionEr: 335,
  },
  {
    id: "21",
    name: "Mark Baker",
    status: "closed",
    salary: 2600,
    hourlyRate: 16.25,
    hours: 160,
    bonus: 0,
    commission: 120,
    deduction: 80,
    pensionEe: 130,
    pensionEr: 260,
  },
  {
    id: "22",
    name: "Michelle Lewis",
    status: "open",
    salary: 3150,
    hourlyRate: 19.69,
    hours: 160,
    bonus: 225,
    commission: 0,
    deduction: 100,
    pensionEe: 157.5,
    pensionEr: 315,
  },
  {
    id: "23",
    name: "Nancy Gonzalez",
    status: "open",
    salary: 3050,
    hourlyRate: 19.06,
    hours: 160,
    bonus: 190,
    commission: 0,
    deduction: 100,
    pensionEe: 152.5,
    pensionEr: 305,
  },
  {
    id: "24",
    name: "Patricia Young",
    status: "closed",
    salary: 3000,
    hourlyRate: 18.75,
    hours: 160,
    bonus: 0,
    commission: 190,
    deduction: 100,
    pensionEe: 150,
    pensionEr: 300,
  },
  {
    id: "25",
    name: "Paul Green",
    status: "open",
    salary: 2750,
    hourlyRate: 17.19,
    hours: 160,
    bonus: 150,
    commission: 0,
    deduction: 90,
    pensionEe: 137.5,
    pensionEr: 275,
  },
];

// Helper function to format currency for display
export const formatCurrency = (value: number): string => {
  return `£${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
};

// Helper function to parse currency string to number
export const parseCurrency = (value: string): number => {
  return parseFloat(value.replace(/[£,]/g, "")) || 0;
};

// Helper function to calculate tax
export const calculateTax = (salary: number): number => {
  // Simple tax calculation (20% of salary)
  return salary * 0.2;
};

// Helper function to calculate NI
export const calculateNI = (salary: number): number => {
  // Simple NI calculation (12% of salary)
  return salary * 0.12;
};

// Helper function to calculate net pay
export const calculateNetPay = (
  salary: number,
  additions: number,
  deductions: number,
  pensionEe: number,
  tax: number,
  ni: number,
): number => {
  return salary + additions - deductions - pensionEe - tax - ni;
};

// Helper function to calculate employer cost
export const calculateCost = (
  salary: number,
  additions: number,
  pensionEr: number,
  ni: number,
): number => {
  // Employer cost includes salary, additions, employer pension, and employer NI (13.8%)
  const employerNI = salary * 0.138;
  return salary + additions + pensionEr + employerNI;
};

// Function to transform batch employee data to overview format
export const transformToOverviewFormat = (employees: any[]) => {
  return employees.map((emp) => {
    const tax = calculateTax(emp.salary);
    const ni = calculateNI(emp.salary);
    const additions = emp.bonus + (emp.commission || 0);
    const net = calculateNetPay(
      emp.salary,
      additions,
      emp.deduction,
      emp.pensionEe,
      tax,
      ni,
    );
    const cost = calculateCost(emp.salary, additions, emp.pensionEr, ni);

    return {
      id: emp.id,
      name: emp.name,
      status: emp.status,
      basic: formatCurrency(emp.salary),
      hours: emp.hours.toString(),
      additions: formatCurrency(additions),
      deductions: formatCurrency(emp.deduction),
      employeePension: formatCurrency(emp.pensionEe),
      employerPension: formatCurrency(emp.pensionEr),
      tax: formatCurrency(tax),
      ni: formatCurrency(ni),
      net: formatCurrency(net),
      cost: formatCurrency(cost),
    };
  });
};
