// Popover for renaming schedule name (max 20 chars, enter or save button)
import React, { useState, useRef, useEffect } from "react";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { z } from "zod";

const nameSchema = z.string().trim().min(1, "Name required").max(20, "Max 20 characters");

interface RenamePopoverProps {
  initialName: string;
  onSave: (newName: string) => void;
  onCancel: () => void;
}

export const PayPeriodScheduleRenamePopover: React.FC<RenamePopoverProps> = ({
  initialName,
  onSave,
  onCancel,
}) => {
  const [name, setName] = useState(initialName);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setName(initialName);
    setError(null);
    setTimeout(() => inputRef.current?.focus(), 100);
  }, [initialName]);

  const handleSave = () => {
    const result = nameSchema.safeParse(name);
    if (!result.success) {
      setError(result.error.errors[0].message);
      return;
    }
    onSave(name.trim());
  };

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (error) setError(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <PopoverContent side="right" align="start" className="w-64 p-3">
      <div className="flex flex-col gap-2">
        <input
          ref={inputRef}
          type="text"
          value={name}
          onChange={handleInput}
          onKeyDown={handleKeyDown}
          maxLength={20}
          className="border rounded px-2 py-1 text-base focus:outline-none focus:ring-2 focus:ring-sky-500"
          placeholder="Schedule name"
        />
        {error && <span className="text-xs text-red-600">{error}</span>}
        <div className="flex gap-2 justify-end">
          <Button variant="ghost" size="sm" onClick={onCancel}>Cancel</Button>
          <Button variant="default" size="sm" onClick={handleSave}>Save</Button>
        </div>
      </div>
    </PopoverContent>
  );
};
