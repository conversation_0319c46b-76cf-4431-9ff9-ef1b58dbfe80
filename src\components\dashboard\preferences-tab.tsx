import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export function PreferencesTab() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Application Preferences</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Display Settings</CardTitle>
            <CardDescription>Customize how your application appears.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="theme-mode">Dark Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Switch between light and dark theme.
                </p>
              </div>
              <Switch id="theme-mode" />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="dense-mode">Dense Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Compact view for information-dense pages.
                </p>
              </div>
              <Switch id="dense-mode" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Application Behavior</CardTitle>
            <CardDescription>Configure how the application works.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="startup-behavior">Load Last Employer on Startup</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically opens your most recent employer.
                </p>
              </div>
              <Switch id="startup-behavior" />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notifications">Desktop Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Show desktop notifications for important events.
                </p>
              </div>
              <Switch id="notifications" />
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">Reset to Defaults</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
