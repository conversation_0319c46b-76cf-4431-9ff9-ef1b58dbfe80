"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AlertCircle } from "lucide-react";

export interface NumberInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "onChange" | "value" | "type"
  > {
  value: number | undefined | null;
  onChange: (value: number | undefined) => void;
  decimalPlaces?: number;
  min?: number;
  max?: number;
  allowNegative?: boolean;
  useThousandSeparator?: boolean;
  className?: string;
  allowCalculations?: boolean;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
}

export function NumberInput({
  value,
  onChange,
  decimalPlaces = 2,
  min,
  max,
  allowNegative = true,
  useThousandSeparator = true,
  className,
  onBlur,
  onKeyDown,
  allowCalculations = true,
  ...props
}: NumberInputProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [displayValue, setDisplayValue] = React.useState(
    value !== undefined && value !== null ? value.toFixed(decimalPlaces) : "",
  );
  const [calculationError, setCalculationError] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState("");
  const lastProcessedValueRef = React.useRef<string | null>(null);

  // Format with thousand separator for display
  const formatWithSeparator = React.useCallback(
    (value: number): string => {
      // First, get the full precision value with all decimal places
      const fullPrecisionValue = value.toFixed(decimalPlaces);

      // Determine how many decimal places to actually display
      // Always show at least 2 decimal places, but show more (up to decimalPlaces) if they're not zero
      let displayDecimalPlaces = 2; // Default to 2 decimal places

      if (decimalPlaces > 2) {
        // Check if there are non-zero digits after the 2nd decimal place
        const valueStr = value.toString();
        const decimalPointIndex = valueStr.indexOf(".");

        if (
          decimalPointIndex !== -1 &&
          valueStr.length > decimalPointIndex + 3
        ) {
          // Get the decimal part after the 2nd decimal place
          const extraDecimals = valueStr.substring(decimalPointIndex + 3);
          // If there are any non-zero digits, show all decimal places
          if (/[1-9]/.test(extraDecimals)) {
            displayDecimalPlaces = decimalPlaces;
          }
        }
      }

      // Format the value with the determined number of decimal places
      const fixedValue = value.toFixed(displayDecimalPlaces);

      if (!useThousandSeparator) return fixedValue;

      // Split into whole and decimal parts
      const parts = fixedValue.split(".");
      const wholePart = parts[0];
      const decimalPart = parts.length > 1 ? "." + parts[1] : "";

      // Add thousand separators to whole part
      const formattedWholePart = wholePart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        ",",
      );

      return formattedWholePart + decimalPart;
    },
    [decimalPlaces, useThousandSeparator],
  );

  // Update display value when prop value changes (if not editing)
  React.useEffect(() => {
    if (!isEditing && value !== undefined && value !== null) {
      setDisplayValue(formatWithSeparator(value));
    } else if (!isEditing) {
      setDisplayValue("");
    }
    // **NEW**: Sync ref when parent changes value
    lastProcessedValueRef.current =
      value !== undefined && value !== null ? value.toFixed(decimalPlaces) : "";
  }, [
    value,
    decimalPlaces,
    isEditing,
    useThousandSeparator,
    formatWithSeparator,
  ]);

  /**
   * Evaluates a mathematical expression string and returns the result
   * Supports +, -, *, /, and parentheses
   */
  const evaluateExpression = (expression: string): number => {
    // Remove all spaces and thousand separators
    const cleanedExpression = expression.replace(/\s/g, "").replace(/,/g, "");

    // Check if it's a simple number with no operators
    if (/^-?\d*\.?\d*$/.test(cleanedExpression)) {
      return parseFloat(cleanedExpression);
    }

    try {
      // Basic validation to prevent malicious code execution
      if (!/^[\d\+\-\*\/\(\)\.]+$/.test(cleanedExpression)) {
        throw new Error("Invalid characters in expression");
      }

      // Check for incomplete expressions (ending with an operator)
      if (/[\+\-\*\/]$/.test(cleanedExpression)) {
        throw new Error("Expression cannot end with an operator");
      }

      // Check for unbalanced parentheses
      const openParens = (cleanedExpression.match(/\(/g) || []).length;
      const closeParens = (cleanedExpression.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        throw new Error("Unbalanced parentheses in expression");
      }

      // Check for consecutive operators
      if (/[\+\-\*\/][\+\*\/]/.test(cleanedExpression)) {
        throw new Error("Expression contains consecutive operators");
      }

      // Use Function constructor to evaluate the expression safely
      // This is safer than eval() but still needs the input validation above
      let result;
      try {
        result = new Function(`return ${cleanedExpression}`)();
      } catch (syntaxError) {
        throw new Error("Invalid expression syntax");
      }

      // Verify the result is a valid number
      if (typeof result !== "number" || isNaN(result) || !isFinite(result)) {
        throw new Error("Expression did not evaluate to a valid number");
      }

      return result;
    } catch (error) {
      // If any error occurs during evaluation, throw it up
      throw error;
    }
  };

  // Function to process the input value (calculation or direct number)
  const processInputValue = (inputValue: string) => {
    try {
      // Reset previous error
      setCalculationError(false);
      setErrorMessage("");

      // Check if it's potentially a calculation
      if (
        (allowCalculations && /[\+\*\/\(\)]/.test(inputValue)) ||
        (inputValue.includes("-") && inputValue.lastIndexOf("-") > 0)
      ) {
        const result = evaluateExpression(inputValue);
        let finalValue = result;

        // Apply constraints after calculation
        if (min !== undefined && finalValue < min) finalValue = min;
        if (max !== undefined && finalValue > max) finalValue = max;
        if (!allowNegative && finalValue < 0) finalValue = 0;

        onChange(finalValue);
        setDisplayValue(formatWithSeparator(finalValue)); // Format result for display
        // **NEW**: Update ref with the successfully processed formatted value
        lastProcessedValueRef.current = formatWithSeparator(finalValue);
      } else {
        // Treat as a simple number
        const cleanedValue = inputValue.replace(/,/g, ""); // Remove thousand separators

        // Check for multiple decimal points
        if ((cleanedValue.match(/\./g) || []).length > 1) {
          throw new Error("Invalid number: Multiple decimal points");
        }

        // Handle single hyphen as zero
        if (cleanedValue === "-") {
          const zeroValue = 0;
          // Check constraints for zero
          if (!allowNegative && zeroValue < 0) {
            onChange(0); // Should already be 0, but for clarity
            setDisplayValue(formatWithSeparator(0));
          } else {
            onChange(zeroValue);
            setDisplayValue(formatWithSeparator(zeroValue));
          }
          return; // Exit processing early
        }

        const parsedValue = parseFloat(cleanedValue);
        if (!isNaN(parsedValue)) {
          let finalValue = parsedValue;
          if (min !== undefined && finalValue < min) finalValue = min;
          if (max !== undefined && finalValue > max) finalValue = max;
          if (!allowNegative && finalValue < 0) finalValue = 0;

          onChange(finalValue);
          setDisplayValue(formatWithSeparator(finalValue)); // Format for display
          // **NEW**: Update ref with the successfully processed formatted value
          lastProcessedValueRef.current = formatWithSeparator(finalValue);
        } else {
          // This case should now primarily catch invalid characters (non-numeric, non-calc)
          throw new Error("Invalid number");
        }
      }
    } catch (error) {
      // Handle all errors (calculation or invalid number)
      setCalculationError(true);
      const message = error instanceof Error ? error.message : "Invalid input";
      // Prepend the original input to the error message
      setErrorMessage(`"${inputValue}": ${message}`);
      setDisplayValue(inputValue); // Show the invalid input as entered
      onChange(undefined);
      // **NEW**: Keep the last successful value in the ref on error
      // lastProcessedValueRef.current = null; // Or keep the last known good value
    }
  };

  // **REVERT**: Back to minimal handleChange
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Update display immediately for responsiveness
    setDisplayValue(inputValue);

    // Reset error state if it was active
    if (calculationError) {
      setCalculationError(false);
      setErrorMessage("");
    }

    // Only handle the edge case of clearing the input or starting a negative number
    // to ensure the underlying value becomes undefined immediately for the parent.
    if (inputValue === "" || inputValue === "-") {
      onChange(undefined);
    }
    // No other parsing or onChange calls here.
  };

  // **NEW**: Centralized logic for processing value on blur/enter
  const handleValueProcessing = (currentInputValue: string) => {
    const lastFormattedValue = lastProcessedValueRef.current;

    if (!currentInputValue) {
      // If empty on blur, set to 0
      const zeroValue = 0;
      const formattedZero = formatWithSeparator(zeroValue);
      // Only update if value changed
      if (formattedZero !== lastFormattedValue) {
        setDisplayValue(formattedZero);
        onChange(zeroValue);
        lastProcessedValueRef.current = formattedZero;
      } else {
        // If visually empty but already 0 underneath, ensure display matches
        setDisplayValue(formattedZero);
      }
    } else if (currentInputValue === "-") {
      // If just hyphen on blur, treat as 0
      const zeroValue = 0;
      const formattedZero = formatWithSeparator(zeroValue);
      if (formattedZero !== lastFormattedValue) {
        setDisplayValue(formattedZero);
        onChange(zeroValue);
        lastProcessedValueRef.current = formattedZero;
      } else {
        // If already 0 underneath, ensure display matches
        setDisplayValue(formattedZero);
      }
    } else if (currentInputValue !== lastFormattedValue) {
      // Only process if the input value is different from the last successfully processed value
      processInputValue(currentInputValue);
    } else {
      // Value hasn't changed, ensure display matches last good formatted value
      setDisplayValue(lastFormattedValue ?? "");
    }
  };

  const handleFocus = () => setIsEditing(true);

  // Refactored: Use centralized processing logic
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Use setTimeout to defer processing slightly, allowing focus to shift smoothly
    setTimeout(() => {
      setIsEditing(false);
      handleValueProcessing(e.target.value);
    }, 0); // Delay of 0ms

    // Call original onBlur prop immediately if provided
    if (onBlur) onBlur(e);
  };

  // Refactored: Use centralized processing logic
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault(); // Prevent default form submission or other actions
      handleValueProcessing(e.currentTarget.value);
      // Optional: Select text after processing? Depends on desired UX
      // e.currentTarget.select();
    }
    // Call original onKeyDown prop if provided
    if (onKeyDown) onKeyDown(e);
  };

  return (
    <div className="relative">
      <Input
        type="text" // Use text to allow calculation input
        inputMode="decimal" // Hint for mobile keyboards
        value={displayValue} // Always bind to internal displayValue state
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown} // Add keydown handler
        className={cn(
          calculationError ? "border-red-500 focus-visible:ring-red-500" : "",
          className,
        )}
        {...props}
      />
      {calculationError && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute top-1/2 right-2 -translate-y-1/2 cursor-help">
                <AlertCircle className="h-4 w-4 text-red-500" />
              </div>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="max-w-xs border-red-600 bg-gradient-to-b from-red-400 to-red-700 text-white"
            >
              <p className="text-sm">{errorMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
