CREATE TABLE `app_settings` (
	`id` text PRIMARY KEY DEFAULT 'singleton' NOT NULL,
	`default_employer_directory` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `employers` (
	`id` text PRIMARY KEY NOT NULL,
	`display_name` text NOT NULL,
	`file_path` text NOT NULL,
	`status` text DEFAULT 'open' NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	`last_opened_at` integer,
	`paye_ref` text,
	`accounts_ref` text,
	`next_pay_date` text,
	`employees` integer,
	`tax_code_notices` integer,
	`rti_submissions` integer,
	`notifications` integer
);
