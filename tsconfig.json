{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESnext", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "allowJs": true, "moduleResolution": "node"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "src/electron/preload/index.ts", "src/electron/main/main.ts"]}