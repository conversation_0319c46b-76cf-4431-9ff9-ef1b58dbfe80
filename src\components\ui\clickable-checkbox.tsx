"use client"

import * as React from "react"
import { Checkbox } from "./checkbox"
import { cn } from "@/lib/utils"
import type { ComponentRef } from "react"

interface ClickableCheckboxProps extends React.ComponentProps<typeof Checkbox> {
  label?: React.ReactNode;
  wrapperClassName?: string;
}

/**
 * ClickableCheckbox extends the base Checkbox component to make the entire area
 * (including any children/label elements) clickable, improving usability.
 */
const ClickableCheckbox = React.forwardRef<
  ComponentRef<typeof Checkbox>,
  ClickableCheckboxProps
>(({ className, label, wrapperClassName, ...props }, ref) => {
  const handleWrapperClick = (e: React.MouseEvent) => {
    // Only trigger if the click wasn't directly on the checkbox
    if (!(e.target as HTMLElement).closest('[data-slot="checkbox"]')) {
      // Programmatically toggle the checkbox
      if (!props.disabled) {
        props.onCheckedChange?.(!props.checked)
      }
    }
  }

  return (
    <div 
      className={cn(
        "flex items-center gap-2 cursor-pointer select-none",
        props.disabled && "opacity-50 cursor-not-allowed",
        wrapperClassName
      )}
      onClick={handleWrapperClick}
    >
      <Checkbox
        ref={ref}
        className={className}
        {...props}
      />
      {label}
    </div>
  )
})

ClickableCheckbox.displayName = "ClickableCheckbox"

export { ClickableCheckbox }
