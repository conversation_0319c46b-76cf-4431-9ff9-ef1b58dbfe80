/**
 * Student Loan Calculator
 * 
 * This module provides functions for calculating UK student loan deductions
 * according to HMRC guidelines. It supports all current UK student loan plans:
 * - Plan 1
 * - Plan 2
 * - Plan 4 (Scotland)
 * - Plan 5 (Northern Ireland)
 * - Postgraduate Loan
 * 
 * Multiple plans can be active simultaneously, with specific calculation rules for each.
 */

import { 
  StudentLoanCalculationInput, 
  StudentLoanCalculationResult, 
  StudentLoanPlanType,
  PayPeriodType
} from './types';

/**
 * Calculate student loan deductions for the given input
 * 
 * @param input Student loan calculation input parameters
 * @returns Student loan calculation result with breakdown by plan
 */
export function calculateStudentLoan(
  input: StudentLoanCalculationInput
): StudentLoanCalculationResult {
  const { 
    grossPay, 
    planTypes, 
    payPeriod, 
    taxYearConfig, 
    periodNumber, 
    previousEarningsInTaxYear = 0,
    previousDeductionsByPlan = {} as Record<StudentLoanPlanType, number>
  } = input;

  // Initialize result structure with empty records for all plan types
  const initialDeductionsByPlan: Record<StudentLoanPlanType, number> = {
    [StudentLoanPlanType.PLAN_1]: 0,
    [StudentLoanPlanType.PLAN_2]: 0,
    [StudentLoanPlanType.PLAN_4]: 0,
    [StudentLoanPlanType.PLAN_5]: 0,
    [StudentLoanPlanType.POSTGRADUATE_LOAN]: 0
  };

  const initialThresholdsByPlan: Record<StudentLoanPlanType, number> = {
    [StudentLoanPlanType.PLAN_1]: 0,
    [StudentLoanPlanType.PLAN_2]: 0,
    [StudentLoanPlanType.PLAN_4]: 0,
    [StudentLoanPlanType.PLAN_5]: 0,
    [StudentLoanPlanType.POSTGRADUATE_LOAN]: 0
  };

  const initialRatesByPlan: Record<StudentLoanPlanType, number> = {
    [StudentLoanPlanType.PLAN_1]: 0,
    [StudentLoanPlanType.PLAN_2]: 0,
    [StudentLoanPlanType.PLAN_4]: 0,
    [StudentLoanPlanType.PLAN_5]: 0,
    [StudentLoanPlanType.POSTGRADUATE_LOAN]: 0
  };

  // Initialize cumulative deductions with previous values or zeros
  const initialCumulativeDeductionsByPlan: Record<StudentLoanPlanType, number> = {
    [StudentLoanPlanType.PLAN_1]: previousDeductionsByPlan[StudentLoanPlanType.PLAN_1] || 0,
    [StudentLoanPlanType.PLAN_2]: previousDeductionsByPlan[StudentLoanPlanType.PLAN_2] || 0,
    [StudentLoanPlanType.PLAN_4]: previousDeductionsByPlan[StudentLoanPlanType.PLAN_4] || 0,
    [StudentLoanPlanType.PLAN_5]: previousDeductionsByPlan[StudentLoanPlanType.PLAN_5] || 0,
    [StudentLoanPlanType.POSTGRADUATE_LOAN]: previousDeductionsByPlan[StudentLoanPlanType.POSTGRADUATE_LOAN] || 0
  };

  const result: StudentLoanCalculationResult = {
    totalDeduction: 0,
    deductionsByPlan: { ...initialDeductionsByPlan },
    earningsSubjectToDeduction: grossPay,
    thresholdsByPlan: { ...initialThresholdsByPlan },
    ratesByPlan: { ...initialRatesByPlan },
    cumulativeGrossPay: previousEarningsInTaxYear + grossPay,
    cumulativeDeduction: 0,
    cumulativeDeductionsByPlan: { ...initialCumulativeDeductionsByPlan }
  };

  // If no plan types, return zero deduction
  if (!planTypes || planTypes.length === 0) {
    return result;
  }

  // Get student loan config from tax year config
  const studentLoanConfig = taxYearConfig.studentLoan;
  if (!studentLoanConfig) {
    throw new Error('Student loan configuration not found in tax year config');
  }

  // Calculate period factor based on pay period
  const periodFactor = getPeriodFactor(payPeriod);

  // Process each plan type
  for (const planType of planTypes) {
    // Get threshold and rate based on plan type
    let threshold = 0;
    let rate = 0;

    switch (planType) {
      case StudentLoanPlanType.PLAN_1:
        threshold = studentLoanConfig.plan1Threshold;
        rate = studentLoanConfig.repaymentRate;
        break;
      case StudentLoanPlanType.PLAN_2:
        threshold = studentLoanConfig.plan2Threshold;
        rate = studentLoanConfig.repaymentRate;
        break;
      case StudentLoanPlanType.PLAN_4:
        threshold = studentLoanConfig.plan4Threshold;
        rate = studentLoanConfig.repaymentRate;
        break;
      case StudentLoanPlanType.PLAN_5:
        // Plan 5 is new for Northern Ireland - if not in config, use Plan 1 as fallback
        // Note: Plan 5 may not be in all tax year configs yet, so we fallback to Plan 1
        threshold = studentLoanConfig.plan5Threshold || studentLoanConfig.plan1Threshold;
        rate = studentLoanConfig.repaymentRate;
        break;
      case StudentLoanPlanType.POSTGRADUATE_LOAN:
        threshold = studentLoanConfig.postgraduateThreshold;
        rate = studentLoanConfig.postgraduateRepaymentRate;
        break;
      default:
        throw new Error(`Unsupported student loan plan type: ${planType}`);
    }

    // Store threshold and rate in result
    result.thresholdsByPlan[planType] = threshold;
    result.ratesByPlan[planType] = rate;

    // Calculate period threshold
    const periodThreshold = threshold / periodFactor;

    // Calculate deduction for this plan
    let deduction = 0;
    if (grossPay > periodThreshold) {
      deduction = Math.floor((grossPay - periodThreshold) * rate * 100) / 100;
    }

    // Store deduction in result
    result.deductionsByPlan[planType] = deduction;
    result.totalDeduction += deduction;

    // Update cumulative deductions
    result.cumulativeDeductionsByPlan[planType] += deduction;
  }

  // Calculate total cumulative deduction
  result.cumulativeDeduction = Object.values(result.cumulativeDeductionsByPlan)
    .reduce((sum, value) => sum + value, 0);

  // Round total deduction to 2 decimal places
  result.totalDeduction = Math.round(result.totalDeduction * 100) / 100;

  return result;
}

/**
 * Get the period factor for converting annual values to period values
 * 
 * @param payPeriod The pay period type
 * @returns The period factor (number of periods in a year)
 */
function getPeriodFactor(payPeriod: PayPeriodType): number {
  switch (payPeriod) {
    case PayPeriodType.WEEKLY:
      return 52;
    case PayPeriodType.TWO_WEEKLY:
      return 26;
    case PayPeriodType.FOUR_WEEKLY:
      return 13;
    case PayPeriodType.MONTHLY:
      return 12;
    case PayPeriodType.QUARTERLY:
      return 4;
    case PayPeriodType.BI_ANNUALLY:
      return 2;
    case PayPeriodType.ANNUALLY:
      return 1;
    default:
      throw new Error(`Unsupported pay period: ${payPeriod}`);
  }
}
