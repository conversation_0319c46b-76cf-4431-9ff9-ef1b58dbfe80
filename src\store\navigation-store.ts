import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from '@/constants/file';

export type SectionId =
  | 'payroll'
  | 'rti'
  | 'reports'
  | 'pensions'
  | 'employees'
  | 'employer'
  | 'hmrc'
  | 'cis'
  | 'dashboard';

interface NavigationState {
  activeEmployerId: string | null;
  globalSection: SectionId; // For dashboard/no employer open
  employerSections: Record<string, SectionId>; // employerId -> last section
  setActiveEmployer: (employerId: string | null) => void;
  setEmployerSection: (employerId: string, section: SectionId) => void;
  setGlobalSection: (section: SectionId) => void;
}

export const useNavigationStore = create<NavigationState>()(
  persist(
    (set, get) => ({
      activeEmployerId: null,
      globalSection: 'dashboard',
      employerSections: {},
      // --- Add a hydration effect to reset globalSection if no employer is open ---
      hydrate: () => {
        const state = get();
        // If there is no active employer, always reset to dashboard
        if (!state.activeEmployerId) {
          set({ globalSection: 'dashboard', activeEmployerId: null });
        }
      },
      setActiveEmployer: (employerId) => {
        set({ activeEmployerId: employerId });
        if (employerId) {
          const lastSection = get().employerSections[employerId] || 'payroll';
          set((state) => ({
            employerSections: {
              ...state.employerSections,
              [employerId]: lastSection,
            },
            globalSection: undefined, // Clear globalSection so employer sections render
          }));
        } else {
          set({ globalSection: 'dashboard' }); // When closing employer, show dashboard
        }
      },
      setEmployerSection: (employerId, section) =>
        set((state) => ({
          employerSections: {
            ...state.employerSections,
            [employerId]: section,
          },
        })),
      setGlobalSection: (section) => set({ globalSection: section }),
    }),
    {
      name: `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_nav_state`,
      partialize: (state) => ({
        activeEmployerId: state.activeEmployerId,
        globalSection: state.globalSection,
        employerSections: state.employerSections,
      }),
    }
  )
);
