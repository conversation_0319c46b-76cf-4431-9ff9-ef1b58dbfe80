import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Users } from "lucide-react";

export function UsersTab() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">User and License Management</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
            <CardDescription>Manage users and their access permissions.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 py-2">
              <Avatar>
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <p className="text-sm font-medium"><PERSON></p>
                <p className="text-xs text-muted-foreground">Administrator</p>
              </div>
              <div className="flex-1 text-right">
                <Badge>Active</Badge>
              </div>
            </div>
            <div className="flex items-center space-x-4 py-2">
              <Avatar>
                <AvatarFallback>AS</AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <p className="text-sm font-medium">Alice Smith</p>
                <p className="text-xs text-muted-foreground">Payroll Manager</p>
              </div>
              <div className="flex-1 text-right">
                <Badge>Active</Badge>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <Users className="mr-2 h-4 w-4" />
              Manage Users
            </Button>
          </CardFooter>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>License Information</CardTitle>
            <CardDescription>Your current license status and information.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm font-medium">License Type:</span>
              <span className="text-sm">Premium</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Expires:</span>
              <span className="text-sm">31/03/2026</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">User Limit:</span>
              <span className="text-sm">10 users (2 used)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Employer Limit:</span>
              <span className="text-sm">Unlimited</span>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">Upgrade License</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
