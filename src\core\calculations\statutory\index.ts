/**
 * Statutory Payments Calculation Module
 * 
 * This module exports functions for calculating UK statutory payments
 * according to HMRC guidelines, including SSP, SMP, SPP, SAP, SPBP, and ShPP.
 */

import { calculateStatutoryPayment } from './calculator';
import { 
  StatutoryPaymentCalculationInput, 
  StatutoryPaymentCalculationResult, 
  StatutoryPaymentType,
  PayPeriodType,
  StatutoryPaymentTypeMetadata
} from './types';
import {
  STATUTORY_PAYMENT_METADATA,
  getStatutoryPaymentMetadata,
  isStatutoryPaymentTypeAvailable,
  getAvailableStatutoryPaymentTypes,
  getAvailableStatutoryPaymentMetadata
} from './metadata';

// Export the main calculation function
export { calculateStatutoryPayment };

// Export metadata functions
export {
  getStatutoryPaymentMetadata,
  isStatutoryPaymentTypeAvailable,
  getAvailableStatutoryPaymentTypes,
  getAvailableStatutoryPaymentMetadata,
  STATUTORY_PAYMENT_METADATA
};

// Export types
export type { 
  StatutoryPaymentCalculationInput, 
  StatutoryPaymentCalculationResult,
  StatutoryPaymentTypeMetadata
};

// Export enums
export { 
  StatutoryPaymentType,
  PayPeriodType 
};
