{{ ... }}

## Employee Schema TODOs (2025-04-13)

- Re-implement `endDate` field for `hourlyRateSchema` and `dailyRateSchema` in `src/lib/schemas/employee.ts` and update the UI in `src/components/employees/sections/payment-section.tsx`.
- Re-implement `isProtectedUnderTUPE` field in `employeeSchema` (`src/lib/schemas/employee.ts`) and update the UI in `src/components/employees/sections/starter-leaver-section.tsx`.
