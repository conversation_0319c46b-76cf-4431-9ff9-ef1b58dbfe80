/**
 * Types for PAYE (Pay As You Earn) calculations
 * Implements interfaces according to HMRC PAYErout specification
 */

import { TaxYearConfig } from '@/core/calculations/tax-years/types';
import { TaxCodeComponents } from '@/core/calculations/paye/tax-code-parser';

/**
 * Pay period types
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  TWO_WEEKLY = 'two_weekly',
  FOUR_WEEKLY = 'four_weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BI_ANNUALLY = 'bi_annually',
  ANNUALLY = 'annually'
}

/**
 * Input parameters for PAYE tax calculation
 */
export interface PayeCalculationInput {
  /**
   * Gross pay for the period (required)
   */
  grossPay: number;
  
  /**
   * Tax code (e.g., "1257L", "K500", "S1257L") (required)
   */
  taxCode: string;
  
  /**
   * Pay period type (required)
   */
  payPeriod: PayPeriodType;
  
  /**
   * Tax year configuration (required)
   */
  taxYearConfig: TaxYearConfig;
  
  /**
   * Whether the employee is subject to Scottish tax rates
   * (Overrides automatic detection from tax code when true)
   */
  isScottishTaxpayer?: boolean;
  
  /**
   * Whether the employee is subject to Welsh tax rates
   * (Overrides automatic detection from tax code when true)
   */
  isWelshTaxpayer?: boolean;
  
  /**
   * Week or month number in the tax year (1-based)
   * Defaults to 1 if not provided
   */
  periodNumber?: number;
  
  /**
   * Previous gross pay to date (for cumulative calculations)
   */
  previousPayToDate?: number;
  
  /**
   * Previous cumulative gross pay (for special tax code cumulative calculations)
   */
  previousCumulativeGrossPay?: number;
  
  /**
   * Previous taxable pay to date (for cumulative calculations)
   */
  previousTaxablePay?: number;
  
  /**
   * Previous tax due to date (for cumulative calculations)
   */
  previousTaxDue?: number;
  
  /**
   * Whether to use non-cumulative calculation (Week 1/Month 1 basis)
   * Overrides detection from tax code if true
   */
  isNonCumulative?: boolean;
  
  /**
   * Value of benefits in kind included in grossPay
   * Used for regulatory limit calculation
   */
  benefitsInKind?: number;
}

/**
 * Result of PAYE tax calculation
 */
export interface PayeCalculationResult {
  /**
   * Gross pay for the period
   */
  grossPay: number;
  
  /**
   * Tax due for the current period
   */
  taxDue: number;
  
  /**
   * Tax free amount for the period
   */
  taxFreeAmount: number;
  
  /**
   * Taxable pay for the period
   */
  taxablePay: number;
  
  /**
   * Breakdown of tax calculation by tax band
   */
  taxBreakdown: TaxBandBreakdown[];
  
  /**
   * Effective tax rate as a percentage
   */
  effectiveTaxRate: number;
  
  /**
   * Cumulative gross pay including current period
   */
  cumulativeGrossPay: number;
  
  /**
   * Cumulative taxable pay including current period
   */
  cumulativeTaxablePay: number;
  
  /**
   * Cumulative tax due including current period
   */
  cumulativeTaxDue: number;
  
  /**
   * Tax code used for calculation
   */
  taxCodeUsed: string;
  
  /**
   * Whether emergency tax was applied
   */
  emergencyTaxApplied: boolean;
  
  /**
   * Whether the regulatory limit (50% maximum deduction) was applied
   */
  regulatoryLimitApplied?: boolean;
  
  /**
   * Whether personal allowance reduction was applied for high earners
   */
  personalAllowanceReductionApplied?: boolean;
}

/**
 * Breakdown of tax calculation by tax band
 */
export interface TaxBandBreakdown {
  /**
   * Name of the tax band (e.g., "Basic Rate", "Higher Rate")
   */
  bandName: string;
  
  /**
   * Amount of income in this band
   */
  amount: number;
  
  /**
   * Tax rate as a percentage (e.g., 20 for 20%)
   */
  rate: number;
  
  /**
   * Tax due on this band
   */
  taxDue: number;
}