import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Employer } from "@/lib/schemas/employer";

interface ClientDetailsSectionProps {
  employer: Employer;
  onChange: (field: string, value: any) => void;
  onNestedChange: (parentField: string, field: string, value: any) => void;
}

const ClientDetailsSection: React.FC<ClientDetailsSectionProps> = ({
  employer,
  onChange,
  onNestedChange,
}) => {
  // Defensive: fallback if filingTypes is null/undefined
  const filingTypes = employer.filingTypes ?? {
    finalPays: false,
    eps: false,
    p60s: false,
    p11ds: false,
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSelectChange = (field: string, value: string) => {
    onChange(field, value);
  };

  const handleFilingTypeChange = (field: string, checked: boolean) => {
    onNestedChange("filingTypes", field, checked);
  };

  return (
    <div className="w-full space-y-2 pt-4">
      <div className="text-muted-foreground mb-4 px-4 text-sm">
        Use the following fields to track client information regarding this
        employer (these fields can be shown as columns on the BrightPay start
        screen).
      </div>

      {/* First Row: Contact Name & Label */}
      <div className="grid w-full grid-cols-20 items-center gap-2">
        <Label
          htmlFor="contactName"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Contact name
        </Label>
        <div className="col-span-7">
          <Input
            id="contactName"
            name="contactName"
            value={(employer.contactName || "") || ""}
            onChange={handleInputChange}
            placeholder="Client contact name"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Second Row: Contact Email & Managed By */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="contactEmail"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Contact email
        </Label>
        <div className="col-span-7">
          <Input
            id="contactEmail"
            name="contactEmail"
            value={(employer.contactEmail || "") || ""}
            onChange={handleInputChange}
            placeholder="Client contact email"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Third Row: Contact Phone & Status */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="contactPhone"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Contact phone
        </Label>
        <div className="col-span-7">
          <Input
            id="contactPhone"
            name="contactPhone"
            value={(employer.contactPhone || "") || ""}
            onChange={handleInputChange}
            placeholder="Client phone number"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Fourth Row: Alt Contact Name & Due Date */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="altContactName"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Alt. contact name
        </Label>
        <div className="col-span-7">
          <Input
            id="altContactName"
            name="altContactName"
            value={(employer.altContactName || "") || ""}
            onChange={handleInputChange}
            placeholder="Alternative contact name"
            className="w-full text-sm"
          />
        </div>
      </div>

      {/* Fifth Row: Alt Contact Email & Completed Checkboxes */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="altContactEmail"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Alt. contact email
        </Label>
        <div className="col-span-7">
          <Input
            id="altContactEmail"
            name="altContactEmail"
            value={(employer.altContactEmail || "") || ""}
            onChange={handleInputChange}
            placeholder="Alternative contact email"
            className="w-full text-sm"
          />
        </div>

        <Label
          htmlFor="completed"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Completed
        </Label>
        <div className="col-span-7 flex flex-row gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="finalPays"
              checked={!!filingTypes.finalPays}
              onCheckedChange={(checked) =>
                handleFilingTypeChange("finalPays", checked === true)
              }
            />
            <label htmlFor="finalPays" className="text-sm">
              Final P45s/FPS
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="eps"
              checked={!!filingTypes.eps}
              onCheckedChange={(checked) =>
                handleFilingTypeChange("eps", checked === true)
              }
            />
            <label htmlFor="eps" className="text-sm">
              EPS
            </label>
          </div>
        </div>
      </div>

      {/* Sixth Row: Alt Contact Phone & Forms Checkboxes */}
      <div className="mt-1 grid grid-cols-20 items-center gap-2">
        <Label
          htmlFor="altContactPhone"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Alt. contact phone
        </Label>
        <div className="col-span-7">
          <Input
            id="altContactPhone"
            name="altContactPhone"
            value={(employer.altContactPhone || "") || ""}
            onChange={handleInputChange}
            placeholder="Alternative contact phone"
            className="w-full text-sm"
          />
        </div>

        <Label
          htmlFor="forms"
          className="col-span-3 mx-4 justify-self-end text-right font-medium"
        >
          Forms
        </Label>
        <div className="col-span-7 flex flex-row gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="p60"
              checked={!!filingTypes.p60s}
              onCheckedChange={(checked) =>
                handleFilingTypeChange("p60s", checked === true)
              }
            />
            <label htmlFor="p60" className="text-sm">
              P60
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="p11d"
              checked={!!filingTypes.p11ds}
              onCheckedChange={(checked) =>
                handleFilingTypeChange("p11ds", checked === true)
              }
            />
            <label htmlFor="p11d" className="text-sm">
              P11D
            </label>
          </div>
        </div>
      </div>

      {/* Seventh Row: Notes */}
      <div className="mt-1 grid grid-cols-20 items-start gap-2">
        <Label
          htmlFor="clientNotes"
          className="col-span-3 mx-4 justify-self-end pt-2 text-right font-medium"
        >
          Notes
        </Label>
        <div className="col-span-17">
          <Textarea
            id="clientNotes"
            name="clientNotes"
            value={(employer.clientNotes || "") || ""}
            onChange={handleInputChange}
            placeholder="Client notes and additional information"
            className="min-h-[100px] text-sm"
          />
        </div>
      </div>
    </div>
  );
};

export default ClientDetailsSection;
