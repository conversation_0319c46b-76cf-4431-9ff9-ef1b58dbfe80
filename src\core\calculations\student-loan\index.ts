/**
 * Student Loan Calculation Module
 * 
 * This module exports functions for calculating UK Student Loan deductions
 * according to HMRC guidelines.
 */

import { calculateStudentLoan } from './calculator';
import { 
  StudentLoanCalculationInput, 
  StudentLoanCalculationResult, 
  StudentLoanPlanType,
  PayPeriodType 
} from './types';

// Export the main calculation function
export { calculateStudentLoan };

// Export types
export type { 
  StudentLoanCalculationInput, 
  StudentLoanCalculationResult 
};

// Export enums
export { 
  StudentLoanPlanType,
  PayPeriodType 
};
