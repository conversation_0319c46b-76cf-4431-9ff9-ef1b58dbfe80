# UK Payroll Application - AI Coding Guidelines (Desktop Focus)

This document outlines rules and best practices for AI assistants (Claude, GP<PERSON>, etc.) when helping with the UK Payroll Application development in Cursor, Windsurf, or similar AI-powered coding environments. The application focuses on a desktop-first approach with a SQLite file-per-employer database architecture.

## Code Quality Standards

1. **Follow UK-specific payroll regulations**

   - Adhere to HMRC guidelines for PAYE, National Insurance, and statutory payments
   - Use correct tax year configurations as defined in the project structure
   - Ensure proper handling of UK-specific concepts (tax codes, NI categories, etc.)
   - Implement HMRC API integration for RTI submissions and other filings
   - Follow UK-specific validation rules for data formats

2. **Type safety**

   - Use TypeScript with strict typing for all code
   - Create proper interfaces and types for all data structures
   - Avoid `any` types unless absolutely necessary
   - Use Zod for runtime validation through drizzle-zod integration
   - Ensure proper typing of database operations

3. **Error handling**

   - Implement comprehensive error handling for database operations
   - Include detailed error messages for debugging
   - Create custom error types for domain-specific errors
   - Log errors appropriately based on severity
   - Implement graceful fallbacks for network-dependent features

4. **Code organization**

   - Follow the established project structure from the technical guide
   - Maintain separation of concerns between UI, business logic, and data access
   - Keep calculation modules isolated and pure
   - Use proper naming conventions for UK payroll terminology
   - Create clean, reusable components

5. **TanStack Query Best Practices**
   - Use TanStack Query for all data fetching and caching
   - Create custom query hooks for reusable data access patterns
   - Implement proper query invalidation strategies
   - Set appropriate stale times based on data volatility
   - Use optimistic updates for better user experience

## SQLite Implementation Guidelines

1. **File-per-employer structure**

   - Each employer should be contained in a separate SQLite file
   - Use the centralized constant `EMPLOYER_DB_EXTENSION` (from `src/constants/file.ts`) for the employer database file extension instead of hardcoding `.ukpayroll`.
   - When referencing the employer database file extension or branding in code, always import and use the constants from `src/constants/file.ts`.
   - Implement proper file handling for creation, opening, and closing
   - Use libsql for all SQLite operations
   - Set appropriate SQLite pragmas for performance and reliability

2. **SQLite optimization**

   - Use WAL journal mode for better concurrency
   - Implement proper indexing strategies for common queries
   - Use transactions for multi-step operations
   - Minimize database connections and manage them properly
   - Avoid excessive disk I/O operations

3. **File management**

   - Store database files in appropriate platform-specific locations
   - Implement backup and restore mechanisms
   - Validate file integrity before opening
   - Handle file permissions appropriately
   - Implement proper error handling for file operations

4. **Master database**
   - Keep master database separate from employer databases
   - Store minimal metadata in master database
   - Handle user preferences and settings appropriately
   - Track recently opened employers with timestamps
   - Implement scanning for employer files in default locations

## Drizzle ORM Best Practices

1. **Schema definition**

   - Use drizzle-orm's type-safe schema definitions
   - Create separate schema files for master and employer databases
   - Follow consistent naming conventions for tables and columns
   - Use appropriate SQLite types for all columns
   - Implement relationships with proper foreign keys

2. **drizzle-zod integration**

   - Use drizzle-zod to generate Zod schemas from Drizzle schemas
   - Extend Zod schemas with additional validation rules as needed
   - Create separate validation schemas for form inputs
   - Implement custom validation for UK-specific formats
   - Use Zod for validation

3. **Query patterns**

   - Use type-safe query builders instead of raw SQL
   - Implement proper filtering and sorting
   - Use efficient join patterns
   - Implement pagination for large result sets
   - Avoid N+1 query problems

4. **Migration management**

   - Create proper migration scripts for schema changes
   - Test migrations thoroughly
   - Handle version differences gracefully
   - Implement data migration as needed
   - Document breaking changes

5. **Performance considerations**
   - Minimize the number of queries
   - Use efficient query patterns
   - Implement proper indexing
   - Use transactions for related operations
   - Optimize for desktop performance

## TanStack Query Implementation

1. **Query configuration**

   - Set up TanStack Query with proper default configurations
   - Implement appropriate stale time and cache time settings
   - Configure refetching behavior appropriately
   - Set up proper error handling and retry logic
   - Use devtools in development mode

2. **Custom hooks**

   - Create custom query hooks for common data access patterns
   - Implement proper query key structures
   - Use queries with dependencies correctly
   - Handle loading and error states appropriately
   - Create mutation hooks for data updates

3. **Query keys**

   - Use consistent query key conventions
   - Structure query keys to enable targeted invalidation
   - Include all relevant parameters in query keys
   - Document query key patterns
   - Use query key factories for consistency

4. **Optimistic updates**

   - Implement optimistic updates for common operations
   - Handle rollback on failure
   - Update related queries on success
   - Use optimistic updates for better user experience
   - Maintain data consistency during updates

5. **Caching strategies**
   - Set appropriate stale times based on data volatility
   - Implement selective refetching
   - Use cache manipulation for connected data
   - Preload data for common navigation paths
   - Handle initial data loading efficiently

## Desktop Optimization

1. **Performance optimization**

   - Use Web Workers for CPU-intensive calculations
   - Implement proper state management to minimize re-renders
   - Optimize SQLite operations for large datasets
   - Use TanStack Table with virtualization for data-intensive views
   - Implement background operations for long-running tasks

2. **Electron security**

   - Properly implement contextBridge with explicit API exposure
   - Never expose raw Node.js modules to renderer process
   - Follow principle of least privilege for IPC handlers
   - Validate all data passing between processes
   - Use CSP (Content Security Policy) to prevent XSS attacks

3. **File handling**

   - Implement proper file system access via Electron APIs
   - Create reliable file association handling
   - Implement scanning for employer files
   - Build backup and restore functionality
   - Handle file integrity validation

4. **Desktop UX**
   - Create native-feeling desktop experience
   - Implement keyboard shortcuts and accelerators
   - Design for various screen sizes and densities
   - Provide proper loading and progress indicators
   - Ensure responsive UI even during intensive operations

## Database Schema Guidelines

1. **Master database schema**

   - Keep schema minimal and focused on application needs
   - Include tables for employers, preferences, and settings
   - Store minimal employer metadata (name, file path, last accessed)
   - Implement proper relationships between tables
   - Use appropriate indexes for common queries

2. **Employer database schema**

   - Start with minimal schema focused on current requirements
   - Implement tables incrementally as UI features are developed
   - Create comprehensive schema for all employer data
   - Implement audit logging for all changes
   - Design schema to be portable and self-contained

3. **Field naming conventions**

   - Use camelCase for JavaScript/TypeScript properties
   - Use snake_case for database columns
   - Use descriptive names for all fields
   - Follow consistent naming patterns
   - Document non-obvious field purposes

4. **Data modeling**

   - Model UK-specific payroll concepts accurately
   - Create proper relationships between entities
   - Use appropriate data types for all fields
   - Implement proper validation rules
   - Consider future extensibility

5. **JSON storage**
   - Use JSON storage for complex structures
   - Implement proper serialization and deserialization
   - Validate JSON structure with Zod
   - Create type definitions for all JSON structures
   - Consider query performance implications

## Security Requirements

1. **Data protection**

   - Encrypt sensitive employee data like bank details
   - Never suggest storing unencrypted personally identifiable information (PII)
   - Follow GDPR best practices for data handling
   - Use proper security measures for file access
   - Implement field-level encryption for sensitive data

2. **Input validation**

   - Validate all user inputs using Zod schemas
   - Sanitize data from external sources
   - Use parameterized queries for all database operations
   - Validate all file inputs for security risks
   - Implement comprehensive validation for imported data

3. **File security**
   - Implement proper file permissions
   - Validate file integrity before opening
   - Sanitize file paths to prevent directory traversal
   - Implement backup and restore securely
   - Handle file corruption gracefully

## HMRC Integration Guidelines

1. **RTI submissions**

   - Follow HMRC specifications for RTI submissions
   - Implement proper XML generation for FPS and EPS
   - Create comprehensive validation before submission
   - Handle submission responses and errors appropriately
   - Implement proper audit logging for all submissions

2. **HMRC authentication**

   - Implement OAuth flow according to HMRC guidelines
   - Securely store and refresh credentials
   - Handle authentication errors gracefully
   - Implement proper scope validation
   - Create secure storage for authentication tokens

3. **API validation**
   - Validate all data against HMRC specifications
   - Implement proper error handling for API failures
   - Create comprehensive logs for troubleshooting
   - Test with HMRC test environment
   - Implement fallbacks for connectivity issues

## UI/UX Standards

1. **Accessibility**

   - Ensure all components meet WCAG 2.1 AA standards
   - Implement proper keyboard navigation
   - Use semantic HTML elements
   - Provide appropriate ARIA attributes
   - Test with screen readers and assistive technologies

2. **Responsive design**

   - Design primarily for desktop while ensuring responsiveness
   - Use flexbox and grid for layout
   - Implement proper breakpoints for different screen sizes
   - Test on various display densities
   - Create layouts that work well on landscape and portrait orientations

3. **UK-specific user experience**

   - Use UK date formats (DD/MM/YYYY)
   - Display currency values with proper £ symbol and formatting
   - Follow UK terminology for payroll concepts
   - Implement UK-specific validation (NI numbers, tax codes, etc.)
   - Design user flows according to UK payroll practices

4. **Performance perception**

   - Implement proper loading states for all asynchronous operations
   - Use skeleton loaders for content
   - Provide progress indicators for long-running tasks
   - Optimize initial load times
   - Create smooth transitions between states

5. **Data Loading States**
   - Implement consistent loading indicators with TanStack Query
   - Use skeleton loaders for content loading
   - Provide optimistic updates for write operations
   - Display appropriate error states
   - Maintain UI stability during data transitions

## Testing Guidelines

1. **Unit testing**

   - Write comprehensive tests for all calculation functions
   - Validate UK tax calculations across different scenarios
   - Test database operations with test databases
   - Implement proper mocking for external dependencies
   - Create tests for critical business logic

2. **Integration testing**

   - Test complete workflows across components
   - Verify database operations with real SQLite files
   - Test file handling operations
   - Validate form submissions and validations
   - Test critical application paths

3. **End-to-end testing**

   - Create comprehensive workflow tests
   - Test desktop environment thoroughly
   - Verify critical business flows like payroll processing
   - Test import/export functionality
   - Verify document generation and printing

4. **Test data**
   - Use realistic but anonymized UK employee data
   - Create varied test cases for different tax situations
   - Include edge cases for complex calculations
   - Test with both minimal and large datasets
   - Create representative multi-employer test scenarios

## AI-Specific Guidance

1. **Context awareness**

   - Consider the file-per-employer architecture when suggesting code
   - Reference existing patterns and conventions in the codebase
   - Maintain consistency with established naming conventions
   - Be aware of the SQLite limitations and best practices
   - Consider desktop-specific implications

2. **Explanations**

   - Explain complex UK payroll calculations in comments
   - Provide context for why certain approaches were chosen
   - Highlight potential edge cases in the UK tax system
   - Include references to HMRC documentation where relevant
   - Explain performance considerations

3. **Drizzle ORM specifics**

   - Suggest drizzle-orm patterns with better-sqlite3
   - Implement drizzle-zod integration for validation
   - Suggest efficient query patterns
   - Follow the established schema structure
   - Favor type-safe query builders over raw SQL

4. **SQLite considerations**

   - Consider SQLite limitations when suggesting database operations
   - Recommend appropriate indexing strategies
   - Suggest WAL journal mode for better concurrency
   - Recommend transaction usage for related operations
   - Suggest appropriate pragma settings

5. **Code suggestions**

   - Provide complete implementations rather than stubs
   - Include proper error handling
   - Use appropriate database operations
   - Follow established project structure
   - Implement interfaces consistently

6. **TanStack Query implementations**
   - Suggest custom query hooks for reusable data access patterns
   - Provide proper query key structures for efficient caching
   - Implement correct query invalidation strategies
   - Include appropriate error handling and loading states
   - Use optimistic updates where appropriate

By following these guidelines, AI coding assistants will help create a robust, secure, and compliant UK payroll application that functions effectively in a desktop environment, providing an optimal experience while maintaining code consistency and quality across the entire system. The file-per-employer SQLite architecture with Drizzle ORM and TanStack Query provides an excellent foundation for a performant, flexible,
