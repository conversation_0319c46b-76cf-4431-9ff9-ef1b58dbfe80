const fs = require("fs");
const path = require("path");
const { createClient } = require("@libsql/client");
const { v4: uuidv4 } = require("uuid");

// Configuration
const COUNT = 400;
const OUTPUT_DIR = path.join(__dirname, "../test-employers");
const EMPLOYER_DB_EXTENSION = ".ukpayroll";

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  console.log(`Created output directory: ${OUTPUT_DIR}`);
}

// Company name prefixes and suffixes for generating unique names
const prefixes = [
  "Alpha",
  "Beta",
  "Gamma",
  "Delta",
  "Epsilon",
  "Zeta",
  "Eta",
  "Theta",
  "Iota",
  "Kappa",
  "Lambda",
  "Mu",
  "Nu",
  "Xi",
  "Omicron",
  "Pi",
  "Rho",
  "Sigma",
  "Tau",
  "Upsilon",
  "Phi",
  "Chi",
  "Psi",
  "Omega",
  "Global",
  "National",
  "International",
  "United",
  "Premier",
  "Elite",
];

const suffixes = [
  "Technologies",
  "Solutions",
  "Enterprises",
  "Industries",
  "Corporation",
  "Inc",
  "Ltd",
  "Group",
  "Partners",
  "Associates",
  "Consulting",
  "Services",
  "Systems",
  "Networks",
  "Logistics",
  "Manufacturing",
  "Productions",
  "Media",
  "Healthcare",
  "Pharmaceuticals",
];

// Generate a unique company name
function generateCompanyName(index) {
  const prefix = prefixes[index % prefixes.length];
  const suffix =
    suffixes[Math.floor(index / prefixes.length) % suffixes.length];
  const number = Math.floor(index / (prefixes.length * suffixes.length)) + 1;

  return number > 1 ? `${prefix} ${suffix} ${number}` : `${prefix} ${suffix}`;
}

// Generate a random PAYE reference
function generatePayeRef() {
  const office = Math.floor(100 + Math.random() * 900);
  const reference = Math.floor(10000 + Math.random() * 90000);
  return `${office}/${reference}P`;
}

// Generate a random accounts reference
function generateAccountsRef() {
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const letter1 = letters[Math.floor(Math.random() * letters.length)];
  const letter2 = letters[Math.floor(Math.random() * letters.length)];
  const number = Math.floor(10000 + Math.random() * 90000);
  return `${letter1}${letter2}${number}`;
}

// Create a new employer database
async function createEmployerDb(index) {
  const id = uuidv4();
  const name = generateCompanyName(index);
  const fileName = name.replace(/[^a-zA-Z0-9]/g, "_");
  const filePath = path.join(OUTPUT_DIR, `${fileName}${EMPLOYER_DB_EXTENSION}`);

  // Create the database file
  const client = createClient({ url: `file:${filePath}` });

  try {
    // Create employer table
    await client.execute(`
      CREATE TABLE IF NOT EXISTS employer (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        paye_ref TEXT,
        accounts_ref TEXT,
        address_line1 TEXT,
        address_line2 TEXT,
        address_line3 TEXT,
        town TEXT,
        county TEXT,
        postcode TEXT,
        country TEXT DEFAULT 'United Kingdom',
        phone TEXT,
        email TEXT,
        contact_name TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Insert employer data
    await client.execute({
      sql: `
        INSERT INTO employer (
          id, name, paye_ref, accounts_ref, address_line1, town, postcode, 
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `,
      args: [
        id,
        name,
        generatePayeRef(),
        generateAccountsRef(),
        `${Math.floor(1 + Math.random() * 999)} Main Street`,
        "London",
        `SW${Math.floor(1 + Math.random() * 20)} ${Math.floor(1 + Math.random() * 9)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
      ],
    });

    console.log(
      `Created employer database: ${fileName}${EMPLOYER_DB_EXTENSION}`,
    );
    return { id, name, filePath };
  } catch (error) {
    console.error(`Error creating employer database ${fileName}:`, error);
    throw error;
  } finally {
    await client.close();
  }
}

// Main function to create all test employers
async function createTestEmployers() {
  console.log(`Generating ${COUNT} test employer databases...`);
  console.time("Generation completed in");

  const results = [];

  for (let i = 0; i < COUNT; i++) {
    try {
      const result = await createEmployerDb(i);
      results.push(result);
    } catch (error) {
      console.error(`Failed to create employer ${i}:`, error);
    }

    // Log progress every 10 employers
    if ((i + 1) % 10 === 0) {
      console.log(`Progress: ${i + 1}/${COUNT} employers created`);
    }
  }

  console.timeEnd("Generation completed in");
  console.log(
    `Successfully created ${results.length} employer databases in ${OUTPUT_DIR}`,
  );

  // Write a summary file
  const summaryPath = path.join(OUTPUT_DIR, "employers-summary.json");
  fs.writeFileSync(summaryPath, JSON.stringify(results, null, 2));
  console.log(`Summary written to ${summaryPath}`);
}

// Run the main function
createTestEmployers().catch((error) => {
  console.error("Fatal error:", error);
  process.exit(1);
});
