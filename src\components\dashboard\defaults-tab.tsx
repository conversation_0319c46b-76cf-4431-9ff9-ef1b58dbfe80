import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Settings } from "lucide-react";

export function DefaultsTab() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Default Settings</h2>
      <Card>
        <CardHeader>
          <CardTitle>New Employer Defaults</CardTitle>
          <CardDescription>
            Default settings used when creating a new employer.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Default Pay Frequency</Label>
                <p className="text-sm text-muted-foreground">
                  Monthly
                </p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Change
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Default Payment Method</Label>
                <p className="text-sm text-muted-foreground">
                  BACS
                </p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Change
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Default Pension Scheme</Label>
                <p className="text-sm text-muted-foreground">
                  NEST
                </p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Change
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Default Tax Code</Label>
                <p className="text-sm text-muted-foreground">
                  1257L
                </p>
              </div>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Change
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Save All Defaults</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
