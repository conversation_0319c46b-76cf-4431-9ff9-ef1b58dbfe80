// drizzle.config.ts
import { defineConfig } from "drizzle-kit";

export default defineConfig({
  // Configuration for Master DB (if needed later, placeholder for now)
  // You might need separate entries or logic if schemas/outputs differ significantly

  // Configuration specifically for Employer DB Migrations
  schema: "./src/drizzle/schema/employer.ts", // Path to the employer schema
  out: "./src/drizzle/migrations/employer", // Output directory for employer migrations
  dialect: "sqlite",
  dbCredentials: {
    // better-sqlite3 doesn't need credentials, but Drizzle Kit expects this structure.
    // We provide a dummy URL as it's required by the type, even if not used for generation.
    url: "file:./dummy.db", // This file won't actually be used or created by generate
  },
  verbose: true, // Optional: Provides more detailed output
  strict: true, // Optional: Enables strict type checking
});
