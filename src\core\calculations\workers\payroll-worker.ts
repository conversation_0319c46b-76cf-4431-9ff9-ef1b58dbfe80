/**
 * Payroll Calculation Worker
 * 
 * This Web Worker handles intensive payroll calculations off the main thread.
 * It receives calculation requests, processes them, and returns the results.
 */

import { WorkerRequest, WorkerResponse } from './worker-manager';
import { calculatePaye } from '@/core/calculations/paye/calculator';
import { calculateNationalInsurance } from '../ni/calculator';
import { PayeCalculationInput, PayeCalculationResult } from '@/core/calculations/paye/types';
import { NiCalculationInput, NiCalculationResult } from '@/core/calculations/ni/types';

// Define the calculation types supported by this worker
enum CalculationType {
  PAYE = 'paye',
  NATIONAL_INSURANCE = 'national_insurance',
  PAYSLIP = 'payslip',
  PAY_RUN = 'pay_run'
}

// Set up the worker context
const ctx: Worker = self as any;

// Handle messages from the main thread
ctx.addEventListener('message', (event: MessageEvent<WorkerRequest>) => {
  const request = event.data;
  
  try {
    // Process the request based on its type
    let result: any;
    
    switch (request.type) {
      case CalculationType.PAYE:
        result = handlePayeCalculation(request.payload);
        break;
        
      case CalculationType.NATIONAL_INSURANCE:
        result = handleNiCalculation(request.payload);
        break;
        
      case CalculationType.PAYSLIP:
        result = handlePayslipCalculation(request.payload);
        break;
        
      case CalculationType.PAY_RUN:
        result = handlePayRunCalculation(request.payload);
        break;
        
      default:
        throw new Error(`Unsupported calculation type: ${request.type}`);
    }
    
    // Send the result back to the main thread
    ctx.postMessage({
      id: request.id,
      type: request.type,
      payload: result
    } as WorkerResponse);
  } catch (error) {
    // Send the error back to the main thread
    ctx.postMessage({
      id: request.id,
      type: request.type,
      payload: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    } as WorkerResponse);
  }
});

/**
 * Handle a PAYE calculation request
 * @param input - The PAYE calculation input
 * @returns The PAYE calculation result
 */
function handlePayeCalculation(input: PayeCalculationInput): PayeCalculationResult {
  return calculatePaye(input);
}

/**
 * Handle a National Insurance calculation request
 * @param input - The NI calculation input
 * @returns The NI calculation result
 */
function handleNiCalculation(input: NiCalculationInput): NiCalculationResult {
  return calculateNationalInsurance(input);
}

/**
 * Handle a payslip calculation request
 * @param input - The payslip calculation input
 * @returns The payslip calculation result
 */
function handlePayslipCalculation(input: any): any {
  // This is a placeholder for the payslip calculation logic
  // In a real implementation, this would calculate all components of a payslip
  
  // Calculate PAYE
  const payeResult = calculatePaye(input.paye);
  
  // Calculate National Insurance
  const niResult = calculateNationalInsurance(input.ni);
  
  // Calculate other components (pension, student loan, etc.)
  // ...
  
  // Return the complete payslip result
  return {
    employeeId: input.employeeId,
    payPeriod: input.payPeriod,
    grossPay: input.paye.grossPay,
    taxDue: payeResult.taxDue,
    employeeNi: niResult.employeeNi,
    employerNi: niResult.employerNi,
    netPay: input.paye.grossPay - payeResult.taxDue - niResult.employeeNi,
    // Other components...
  };
}

/**
 * Handle a pay run calculation request
 * @param input - The pay run calculation input
 * @returns The pay run calculation result
 */
function handlePayRunCalculation(input: any): any {
  // This is a placeholder for the pay run calculation logic
  // In a real implementation, this would calculate payslips for multiple employees
  
  // Calculate payslips for each employee
  const payslips = input.employees.map((employee: any) => {
    return handlePayslipCalculation({
      employeeId: employee.id,
      payPeriod: input.payPeriod,
      paye: {
        grossPay: employee.grossPay,
        taxCode: employee.taxCode,
        payPeriod: input.payPeriod,
        taxYearConfig: input.taxYearConfig,
        isScottishTaxpayer: employee.isScottishTaxpayer,
        periodNumber: input.periodNumber,
        previousPayToDate: employee.previousPayToDate,
        previousTaxPaidToDate: employee.previousTaxPaidToDate,
        isDirector: employee.isDirector,
        isNonCumulative: employee.isNonCumulative
      },
      ni: {
        grossPay: employee.grossPay,
        category: employee.niCategory,
        payPeriod: input.payPeriod,
        taxYearConfig: input.taxYearConfig,
        periodNumber: input.periodNumber,
        previousEarningsInTaxYear: employee.previousEarningsInTaxYear,
        previousEmployeeNiInTaxYear: employee.previousEmployeeNiInTaxYear,
        previousEmployerNiInTaxYear: employee.previousEmployerNiInTaxYear,
        isDirector: employee.isDirector,
        employeeAge: employee.age,
        isApprentice: employee.isApprentice,
        isVeteran: employee.isVeteran,
        isInFreeport: employee.isInFreeport
      }
    });
  });
  
  // Calculate totals
  const totals = payslips.reduce((acc: any, payslip: any) => {
    acc.grossPay += payslip.grossPay;
    acc.taxDue += payslip.taxDue;
    acc.employeeNi += payslip.employeeNi;
    acc.employerNi += payslip.employerNi;
    acc.netPay += payslip.netPay;
    return acc;
  }, {
    grossPay: 0,
    taxDue: 0,
    employeeNi: 0,
    employerNi: 0,
    netPay: 0
  });
  
  // Return the complete pay run result
  return {
    payPeriod: input.payPeriod,
    periodNumber: input.periodNumber,
    payslips,
    totals
  };
}

// Notify the main thread that the worker is ready
ctx.postMessage({
  id: 'init',
  type: 'init',
  payload: { status: 'ready' }
} as WorkerResponse);
