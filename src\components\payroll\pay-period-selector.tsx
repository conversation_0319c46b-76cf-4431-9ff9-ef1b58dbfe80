"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";
import React, { useState, useEffect, useRef } from "react";

// Normalize type string to match sortOrder keys (capitalize, handle dashes)
export function normalizeType(type: string): string {
  const t = type.trim().toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
  if (t === 'weekly') return 'Weekly';
  if (t === '2weekly' || t === 'twoweekly' || t === 'biweekly') return '2-Weekly';
  if (t === '4weekly' || t === 'fourweekly') return '4-Weekly';
  if (t === 'monthly') return 'Monthly';
  if (t === 'quarterly') return 'Quarterly';
  if (t === 'yearly' || t === 'annual' || t === 'annually') return 'Yearly';
  return type;
}

import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { PinIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

import { PayPeriod as BasePayPeriod } from '@/drizzle/schema/employer/payPeriod';

type PayPeriod = BasePayPeriod & {
  period_start?: string;
  period_end?: string;
  pay_date?: string;
};

type PayPeriodStatus = 'open' | 'closed' | 'future';

export interface PayPeriodSelectorProps {
  payPeriods: PayPeriod[];
  activePeriod: string;
  onPeriodChange: (periodId: string) => void;
  defaultPinned?: boolean;
  scheduleLabels: Record<string, string>; // scheduleId -> label
  schedules: Array<{ id: string; label: string; type?: string }>;
}


import { PayPeriodScheduleRenamePopover } from "./pay-period-selector-rename-popover";
import { Popover, PopoverTrigger } from "@/components/ui/popover";
import { useRenamePayPeriodScheduleMutation, usePayPeriodSchedules } from "@/hooks/tanstack-query/usePayPeriodSchedules";
import { useTaxYear } from "@/providers/tax-year-provider";

const PayPeriodSelector: React.FC<Omit<PayPeriodSelectorProps, 'schedules'> & { schedules?: any[] }> = ({
  payPeriods,
  activePeriod,
  onPeriodChange,
  defaultPinned = true,
}) => {
  // Get current tax year from context
  const { taxYear } = useTaxYear();
  // Fetch schedules for the current tax year
  const { data: schedules = [], isLoading: schedulesLoading, error: schedulesError } = usePayPeriodSchedules(taxYear);


  // Set initial state from localStorage for persistence across navigation
  const initialPinned = typeof window !== "undefined"
    ? localStorage.getItem(`${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`) === "true"
    : defaultPinned;
  const [isPinned, setIsPinned] = useState(initialPinned);
  const [isVisible, setIsVisible] = useState(initialPinned);

  // Utility: compute status for each period
  const today = new Date();

  // Compute status for each period (placeholder logic)
  function getPeriodStatus(period: PayPeriod, idx: number, arr: PayPeriod[]): PayPeriodStatus {
    const endDate = new Date(period.period_end ?? '');
    if (endDate > today) {
      // If this is the first future period, it's open
      const prev = arr[idx - 1];
      if (!prev || new Date(prev.period_end ?? '') <= today) return 'open';
      return 'future';
    }
    if (endDate.toDateString() === today.toDateString()) return 'open';
    return 'closed'; // before today
    // TODO: Replace with payslip closure logic
  }

  // Utility: format end date for display
  function formatPeriodEndDate(period: PayPeriod): string {
    return period.period_end ? new Date(period.period_end).toLocaleDateString('en-GB') : '-';
  }

  // Utility: map type to label for tooltip
  function getPeriodTypeLabel(type?: string): string {
    switch (normalizeType(type || '')) {
      case 'Weekly': return 'Week';
      case '2-Weekly': return '2-Week';
      case '4-Weekly': return '4-Week';
      case 'Monthly': return 'Month';
      case 'Quarterly': return 'Quarter';
      case 'Yearly': return 'Year';
      default: return type || '';
    }
  }

  // Group periods by schedule_id (not type)
  const groupedBySchedule = payPeriods.reduce<Record<string, PayPeriod[]>>(
    (acc: Record<string, PayPeriod[]>, period: PayPeriod) => {
      const scheduleId = period.schedule_id || 'unknown';
      if (!acc[scheduleId]) acc[scheduleId] = [];
      acc[scheduleId].push(period);
      return acc;
    },
    {}
  );

  // State for renaming popover
  const [isRenamingId, setIsRenamingId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState("");

  const handleRenameClick = (id: string, value: string) => {
    setIsRenamingId(id);
    setRenameValue(value);
  };

  // Mutation for renaming schedule
  const renameMutation = useRenamePayPeriodScheduleMutation();
  function renameSchedule(label: string) {
    if (!isRenamingId) return;
    renameMutation.mutate({ scheduleId: isRenamingId, label });
    setIsRenamingId(null);
  }

  // Sync pin state with localStorage
  useEffect(() => {
    const savedPinState = localStorage.getItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`
    );
    if (savedPinState !== null) {
      setIsPinned(savedPinState === "true");
    }
    // Visibility calculation function
    const checkVisibility = () => {
      const buttonHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`
        ) === "true";
      const selfHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`
        ) === "true";
      const pinState = localStorage.getItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`
      );
      const pinned = pinState === "true";
      setIsVisible(pinned || buttonHover || selfHover);
    };
    // Call on mount to set initial visibility
    checkVisibility();
    // Listen for visibility change events dispatched by the toolbar
    const handleVisibilityChange = (event: Event) => {
      const pinState = localStorage.getItem(
        `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`
      );
      if (pinState !== null) {
        setIsPinned(pinState === "true");
      }
      checkVisibility();
    };
    window.addEventListener("periodSelectorVisibilityChange", handleVisibilityChange);
    return () => {
      window.removeEventListener("periodSelectorVisibilityChange", handleVisibilityChange);
    };
  }, []);
  useEffect(() => {
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
      isPinned.toString()
    );
  }, [isPinned]);

  // Visibility logic (hover, pin, etc)
  useEffect(() => {
    const checkVisibility = () => {
      const buttonHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`
        ) === "true";
      const selfHover =
        localStorage.getItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`
        ) === "true";
      const newVisibility = isPinned || buttonHover || selfHover;
      if (isVisible !== newVisibility) {
        setIsVisible(newVisibility);
        localStorage.setItem(
          `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`,
          newVisibility.toString()
        );
        const event = new CustomEvent("periodSelectorVisibilityChange", {
          detail: { visible: newVisibility },
        });
        window.dispatchEvent(event);
      }
    };
    checkVisibility();
    const interval = setInterval(checkVisibility, 200);
    return () => clearInterval(interval);
  }, [isPinned, isVisible]);

  // Mouse event handlers for localStorage hover
  const handleSelectorMouseEnter = () => {
    setIsVisible(true);
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`,
      "true"
    );
  };
  const handleSelectorMouseLeave = () => {
    setIsVisible(isPinned);
    localStorage.removeItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_self_hover`
    );
  };

  // Sort period types from shortest to longest
  const sortOrder = {
    Weekly: 1,
    "2-Weekly": 2,
    "4-Weekly": 3,
    Monthly: 4,
    Quarterly: 5,
    Yearly: 6,
  };

  // Sort schedules by type, then label
  const sortedSchedules = schedules.slice().sort((a, b) => {
    const normA = normalizeType(a.type || '');
    const normB = normalizeType(b.type || '');
    const typeOrderA = sortOrder[normA as keyof typeof sortOrder] || 99;
    const typeOrderB = sortOrder[normB as keyof typeof sortOrder] || 99;
    if (typeOrderA !== typeOrderB) return typeOrderA - typeOrderB;
    return (a.label || '').localeCompare(b.label || '');
  });

  // Button style util
  const getButtonStyle = (
    period: PayPeriod,
    isActive: boolean,
  ) => {
    const commonStyles = "mx-0.25 transition-all rounded-sm";
    if (isActive)
      return cn(commonStyles, "bg-pink-500 hover:bg-emerald-600 text-white");
    // Compute status for styling
    const arr = (groupedBySchedule[period.type] || []);
    const idx = arr.findIndex((p: PayPeriod) => p.id === period.id);
    const status = getPeriodStatus(period, idx, arr);
    switch (status) {
      case "open":
        return cn(
          commonStyles,
          "bg-gradient-to-t from-slate-500 to-slate-800 text-white hover:bg-slate-800 dark:bg-zinc-600",
        );
      case "closed":
        return cn(
          commonStyles,
          "bg-slate-400 text-white hover:bg-slate-800 dark:bg-zinc-800",
        );
      case "future":
        return cn(
          commonStyles,
          "bg-slate-200 text-slate-400 hover:bg-slate-800 hover:text-white dark:bg-zinc-800 dark:text-zinc-400",
        );
      default:
        return commonStyles;
    }
  };

  return (
    <div className="relative w-full" data-component-name="PayPeriodSelector">

      {/* Main selector UI */}
      <div
        className="relative w-full transition-all duration-300"
        onMouseEnter={handleSelectorMouseEnter}
        onMouseLeave={handleSelectorMouseLeave}
      >
        <div
          className={cn(
            "w-full overflow-hidden transition-all duration-300",
            isVisible ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0",
          )}
        >
          {sortedSchedules.map((sched) => {
            const periods = groupedBySchedule[sched.id] || [];
            if (periods.length === 0) return null;
            function getDisplayLabel() {
              return sched.label && sched.label.trim() !== '' ? sched.label : (sched.type ? normalizeType(sched.type) : '');
            }
            return (
              <div key={sched.id} className="mb-1 flex last:border-b-0">
                {/* Schedule label */}
                <div className="flex w-32 flex-shrink-0 items-center justify-end px-2 py-0.5 text-xs font-semibold capitalize">
  <Popover
    open={isRenamingId === sched.id}
    onOpenChange={open => {
      if (!open) {
        setIsRenamingId(null);
      }
    }}
  >
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <PopoverTrigger asChild>
            <span

              className={cn(
                "transition-colors cursor-pointer select-none focus:outline-none focus:ring-0",
                isRenamingId === sched.id ? "text-sky-700" : "hover:text-sky-700"
              )}
              tabIndex={0}
              role="button"
              aria-label="Rename schedule"
              onClick={() => {
                setIsRenamingId(sched.id);
                setRenameValue(getDisplayLabel());
              }}
              onKeyDown={e => {
                if (e.key === "Enter" || e.key === " ") {
                  setIsRenamingId(sched.id);
                  setRenameValue(getDisplayLabel());
                }
              }}
            >
              {getDisplayLabel()}
            </span>
          </PopoverTrigger>
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="text-xs">
          Click to rename
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
    {isRenamingId === sched.id && (
      <PayPeriodScheduleRenamePopover
        initialName={renameValue}
        onSave={name => {
          renameSchedule(name);
        }}
        onCancel={() => setIsRenamingId(null)}
      />
    )}
  </Popover>
</div>
                {/* Period buttons */}
                <div className="flex-grow overflow-x-auto">
                  <div className="flex w-full">
                    {periods.map((period, idx) => {
                      const isActive = activePeriod === period.id;
                      return (
                        <Tooltip key={period.id}>
                          <TooltipTrigger asChild>
                            <button
                              style={{ flex: 1, minWidth: 0 }}
                              className={cn(
                                "border-r text-center last:border-r-0 py-0.5 text-xs font-normal flex-1 min-w-0 h-6 px-0.5 box-border",
                                getButtonStyle(period, isActive)
                              )}
                              onClick={() => onPeriodChange(period.id)}
                            >
                              {idx + 1}
                            </button>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            align="center"
                            className="border-zinc-700 bg-zinc-800 text-white"
                          >
                            <div className="text-center">
                              <div className="font-medium">
  {`${getPeriodTypeLabel(sched.type)} ${period.period_number ?? ''} ending ${formatPeriodEndDate(period)}`}
</div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PayPeriodSelector;
