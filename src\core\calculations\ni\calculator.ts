//Rounding function
function roundToNearestPenny(value: number): number {
  return Math.round(value * 100) / 100;
}

import { NiCalculationInput, NiCalculationResult, NiBreakdown, PayPeriodType } from './types';
import { NiCategory, TaxYearConfig, NationalInsurance } from '../tax-years/types';

// Define the NiRate interface locally since it's not exported from tax-years/types
interface NiRate {
  threshold: number;
  rate: number;
}

// Define thresholds for special NI categories
interface AdjustedThresholds extends NiCategory {
  lowerEarningsLimit?: number;
  freeportsUpperSecondaryThreshold?: number;
  investmentZonesUpperSecondaryThreshold?: number;
}

/**
 * Calculate National Insurance contributions
 * @param input - The calculation input parameters
 * @returns The calculation result
 */
export function calculateNationalInsurance(input: NiCalculationInput): NiCalculationResult {
  
  // Special handling for directors - use cumulative earnings approach
  if (input.isDirector) {
    return calculateDirectorsNi(input);
  }
  // Validate input
  if (input.grossPay < 0) {
    throw new Error('Gross pay cannot be negative');
  }
  if (!input.taxYearConfig) {
    throw new Error('Tax year configuration is required');
  }
  if (!input.taxYearConfig.nationalInsurance) {
    throw new Error('National Insurance configuration is missing from tax year config');
  }
  if (!input.payPeriod) {
    throw new Error('Pay period is required');
  }
  if (input.periodNumber < 1) {
    throw new Error('Period number must be positive');
  }
  if (input.previousEarningsInTaxYear !== undefined && input.previousEarningsInTaxYear < 0) {
    throw new Error('Previous earnings cannot be negative');
  }
  if (input.previousEmployeeNiInTaxYear !== undefined && input.previousEmployeeNiInTaxYear < 0) {
    throw new Error('Previous employee NI contributions cannot be negative');
  }
  if (input.previousEmployerNiInTaxYear !== undefined && input.previousEmployerNiInTaxYear < 0) {
    throw new Error('Previous employer NI contributions cannot be negative');
  }
  if (input.employeeAge !== undefined && (input.employeeAge < 12 || input.employeeAge > 110)) {
    throw new Error('Employee age must be between 12 and 110');
  }

  // Get the NI category configuration
  const categoryConfig = getCategoryConfig(input.category, input.taxYearConfig.nationalInsurance.categories);
  if (!categoryConfig) {
    throw new Error(`Invalid NI category: ${input.category}`);
  }

  // Determine the earnings period factor based on pay period
  const periodFactor = getPeriodFactor(input.payPeriod);

  // Adjust thresholds for the pay period
  const adjustedThresholds = adjustThresholdsForPeriod(
    categoryConfig,
    periodFactor,
    input.taxYearConfig.nationalInsurance,
    input.isDirector,
    input.weeksRemainingInTaxYear
  );

  // Calculate earnings in each band
  const earningsBands = calculateEarningsBands(input.grossPay, adjustedThresholds);

  // Calculate employee and employer NI contributions
  // Apply mariners' rate reduction if applicable (0.5% reduction)
  const primaryRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.primaryRate - 0.005) 
    : adjustedThresholds.primaryRate;
  
  const upperRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.upperRate - 0.005) 
    : adjustedThresholds.upperRate;
  
  const employerRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.employerRate - 0.005) 
    : adjustedThresholds.employerRate;

  const employeeRates = [
    { threshold: 0, rate: 0 }, // Below LEL
    { threshold: adjustedThresholds.lowerEarningsLimit || 0, rate: 0 }, // LEL to PT
    { threshold: adjustedThresholds.primaryThreshold, rate: primaryRate }, // PT to UEL
    { threshold: adjustedThresholds.upperEarningsLimit, rate: upperRate } // Above UEL
  ];
  
  const employeeNiBreakdown = calculateContributions(earningsBands, employeeRates);

  // For employer NI, we need to calculate based on secondary threshold
  const stValue = adjustedThresholds.secondaryThreshold;
  const employerBands = [
    // Below secondary threshold (usually 0%)
    { 
      thresholdName: 'Below Secondary Threshold', 
      lowerThreshold: 0, 
      upperThreshold: stValue,
      amount: Math.min(input.grossPay, stValue),
      rate: 0,
      contribution: 0
    },
    // Above secondary threshold (employer rate)
    { 
      thresholdName: 'Above Secondary Threshold', 
      lowerThreshold: stValue, 
      upperThreshold: undefined,
      amount: Math.max(0, input.grossPay - stValue),
      rate: 0,
      contribution: 0
    }
  ];

  const employerRates = [
    { threshold: 0, rate: 0 }, // Below ST
    { threshold: stValue, rate: employerRate } // Above ST
  ];
  
  const employerNiBreakdown = calculateContributions(employerBands, employerRates);

  // Apply special cases (veterans, freeports, etc.)
  applySpecialCases(
    employeeNiBreakdown,
    employerNiBreakdown,
    input,
    adjustedThresholds
  );
  
  // Sum up the contributions
  const employeeNi = sumContributions(employeeNiBreakdown);
  const employerNi = sumContributions(employerNiBreakdown);
  
  // Calculate Class 1A NICs on termination awards and sporting testimonials
  const class1aNics = calculateClass1ANics(input);
  
  // Calculate cumulative values
  const cumulativeGrossPay = (input.previousEarningsInTaxYear || 0) + input.grossPay;
  const cumulativeEmployeeNi = (input.previousEmployeeNiInTaxYear || 0) + employeeNi;
  const cumulativeEmployerNi = (input.previousEmployerNiInTaxYear || 0) + employerNi;

  // Return the result
  return {
    employeeNi,
    employerNi,
    employeeNiBreakdown,
    employerNiBreakdown,
    earningsSubjectToNi: input.grossPay,
    earningsAboveUel: earningsBands.find(band => band.thresholdName === 'Above Upper Earnings Limit')?.amount || 0,
    earningsBetweenPtAndUel: earningsBands.find(band => band.thresholdName === 'Primary Threshold to Upper Earnings Limit')?.amount || 0,
    earningsBelowPt: earningsBands.find(band => band.thresholdName === 'Below Primary Threshold')?.amount || 0,
    cumulativeGrossPay,
    cumulativeEmployeeNi,
    cumulativeEmployerNi,
    categoryUsed: input.category,
    ...class1aNics
  };
}

/**
 * Calculate National Insurance contributions for company directors
 * Directors' NI is calculated on a cumulative basis for the tax year
 * @param input - The calculation input parameters
 * @returns The calculation result
 */
function calculateDirectorsNi(input: NiCalculationInput): NiCalculationResult {
  // Get the NI category configuration
  const categoryConfig = getCategoryConfig(input.category, input.taxYearConfig.nationalInsurance.categories);
  if (!categoryConfig) {
    throw new Error(`Invalid NI category: ${input.category}`);
  }

  // For directors, we use their cumulative earnings for the tax year
  const cumulativeGrossPay = (input.previousEarningsInTaxYear || 0) + input.grossPay;
  
  // For directors, we use annual thresholds (no period adjustment)
  const adjustedThresholds = adjustThresholdsForPeriod(
    categoryConfig,
    1,
    input.taxYearConfig.nationalInsurance,
    true,
    input.weeksRemainingInTaxYear
  );
  
  // Calculate earnings in each band for cumulative pay
  const cumulativeEarningsBands = calculateEarningsBands(cumulativeGrossPay, adjustedThresholds);
  
  // Calculate cumulative employee and employer NI contributions
  const primaryRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.primaryRate - 0.005) 
    : adjustedThresholds.primaryRate;
  
  const upperRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.upperRate - 0.005) 
    : adjustedThresholds.upperRate;
  
  const employerRate = input.isMariner 
    ? Math.max(0, adjustedThresholds.employerRate - 0.005) 
    : adjustedThresholds.employerRate;

  const employeeRates = [
    { threshold: 0, rate: 0 }, // Below LEL
    { threshold: adjustedThresholds.lowerEarningsLimit || 0, rate: 0 }, // LEL to PT
    { threshold: adjustedThresholds.primaryThreshold, rate: primaryRate }, // PT to UEL
    { threshold: adjustedThresholds.upperEarningsLimit, rate: upperRate } // Above UEL
  ];
  
  const employerRates = [
    { threshold: 0, rate: 0 }, // Below ST
    { threshold: adjustedThresholds.secondaryThreshold, rate: employerRate } // Above ST
  ];
  
  const cumulativeEmployeeNiBreakdown = calculateContributions(cumulativeEarningsBands, employeeRates);
  const cumulativeEmployerNiBreakdown = calculateContributions(cumulativeEarningsBands, employerRates);
  
  // Apply special cases (veterans, freeports, etc.)
  applySpecialCases(
    cumulativeEmployeeNiBreakdown,
    cumulativeEmployerNiBreakdown,
    input,
    adjustedThresholds
  );
  
  // Sum up the cumulative contributions
  const cumulativeEmployeeNi = sumContributions(cumulativeEmployeeNiBreakdown);
  const cumulativeEmployerNi = sumContributions(cumulativeEmployerNiBreakdown);
  
  // Calculate this period's contribution by subtracting previous contributions
  const employeeNi = Math.max(0, cumulativeEmployeeNi - (input.previousEmployeeNiInTaxYear || 0));
  const employerNi = Math.max(0, cumulativeEmployerNi - (input.previousEmployerNiInTaxYear || 0));
  
  // Calculate this period's breakdown
  const employeeNiBreakdown = cumulativeEmployeeNiBreakdown.map(band => ({
    ...band,
    // We need to adjust the contribution to represent only this period's contribution
    contribution: band.rate * Math.min(band.amount, input.grossPay)
  }));
  
  const employerNiBreakdown = cumulativeEmployerNiBreakdown.map(band => ({
    ...band,
    // We need to adjust the contribution to represent only this period's contribution
    contribution: band.rate * Math.min(band.amount, input.grossPay)
  }));
  
  // Calculate Class 1A NICs on termination awards and sporting testimonials
  const class1aNics = calculateClass1ANics(input);
  
  // Return the result with both period and cumulative values
  return {
    employeeNi,
    employerNi,
    employeeNiBreakdown,
    employerNiBreakdown,
    earningsSubjectToNi: input.grossPay,
    earningsAboveUel: cumulativeEarningsBands.find(band => band.thresholdName === 'Above Upper Earnings Limit')?.amount || 0,
    earningsBetweenPtAndUel: cumulativeEarningsBands.find(band => band.thresholdName === 'Primary Threshold to Upper Earnings Limit')?.amount || 0,
    earningsBelowPt: cumulativeEarningsBands.find(band => band.thresholdName === 'Below Primary Threshold')?.amount || 0,
    cumulativeGrossPay,
    cumulativeEmployeeNi,
    cumulativeEmployerNi,
    categoryUsed: input.category,
    isDirectorsCalculation: true,
    ...class1aNics
  };
}

/**
 * Get the NI category configuration
 * @param category - The NI category letter
 * @param categories - The available NI categories
 * @returns The category configuration
 */
function getCategoryConfig(category: string, categories: NiCategory[]): NiCategory | undefined {
  // Find the category configuration
  return categories.find(cat => cat.category === category.toUpperCase());
}

/**
 * Get the period factor for adjusting annual thresholds
 * @param payPeriod - The pay period type
 * @returns The period factor
 */
function getPeriodFactor(payPeriod: PayPeriodType): number {
  switch (payPeriod) {
    case PayPeriodType.WEEKLY:
      return 52;
    case PayPeriodType.TWO_WEEKLY:
      return 26;
    case PayPeriodType.FOUR_WEEKLY:
      return 13;
    case PayPeriodType.MONTHLY:
      return 12;
    case PayPeriodType.QUARTERLY:
      return 4;
    case PayPeriodType.BI_ANNUALLY:
      return 2;
    case PayPeriodType.ANNUALLY:
      return 1;
    default:
      throw new Error(`Invalid pay period: ${payPeriod}`);
  }
}

/**
 * Adjust thresholds for the pay period
 * @param categoryConfig - The NI category configuration
 * @param periodFactor - The period factor
 * @param niConfig - The National Insurance configuration
 * @param isDirector - Whether the employee is a director
 * @param weeksRemaining - For pro-rata directors, number of weeks remaining in tax year (optional)
 * @returns The adjusted thresholds
 */
function adjustThresholdsForPeriod(
  categoryConfig: NiCategory, 
  periodFactor: number,
  niConfig: NationalInsurance,
  isDirector?: boolean,
  weeksRemaining?: number
): AdjustedThresholds {
  // For directors, handle either annual or pro-rata thresholds
  if (isDirector) {
    // If weeks remaining is provided, calculate pro-rata thresholds
    // This is for directors who become directors during the tax year
    if (weeksRemaining && weeksRemaining < 52) {
      // For pro-rata directors, thresholds are calculated as: annual_threshold ÷ 52 × weeks_remaining
      // and rounded up to the next whole pound as per HMRC guidance
      const proRataFactor = weeksRemaining / 52;
      
      return {
        ...categoryConfig,
        primaryThreshold: Math.ceil(categoryConfig.primaryThreshold * proRataFactor),
        secondaryThreshold: Math.ceil(categoryConfig.secondaryThreshold * proRataFactor),
        upperEarningsLimit: Math.ceil(categoryConfig.upperEarningsLimit * proRataFactor),
        lowerEarningsLimit: Math.ceil(niConfig.lowerEarningsLimit * proRataFactor),
        freeportsUpperSecondaryThreshold: Math.ceil(niConfig.freeportsUpperSecondaryThreshold * proRataFactor),
        investmentZonesUpperSecondaryThreshold: Math.ceil(niConfig.investmentZonesUpperSecondaryThreshold * proRataFactor)
      };
    }
    
    // Full-year director, use annual thresholds
    return {
      ...categoryConfig,
      lowerEarningsLimit: niConfig.lowerEarningsLimit,
      freeportsUpperSecondaryThreshold: niConfig.freeportsUpperSecondaryThreshold,
      investmentZonesUpperSecondaryThreshold: niConfig.investmentZonesUpperSecondaryThreshold
    };
  }

  // For regular employees, adjust all thresholds based on the period factor
  return {
    ...categoryConfig,
    primaryThreshold: Math.floor(categoryConfig.primaryThreshold / periodFactor * 100) / 100,
    secondaryThreshold: Math.floor(categoryConfig.secondaryThreshold / periodFactor * 100) / 100,
    upperEarningsLimit: Math.floor(categoryConfig.upperEarningsLimit / periodFactor * 100) / 100,
    lowerEarningsLimit: Math.floor(niConfig.lowerEarningsLimit / periodFactor * 100) / 100,
    freeportsUpperSecondaryThreshold: Math.floor(niConfig.freeportsUpperSecondaryThreshold / periodFactor * 100) / 100,
    investmentZonesUpperSecondaryThreshold: Math.floor(niConfig.investmentZonesUpperSecondaryThreshold / periodFactor * 100) / 100
  };
}

/**
 * Calculate earnings in each band
 * @param grossPay - The gross pay
 * @param categoryConfig - The adjusted NI category configuration
 * @returns The earnings bands
 */
function calculateEarningsBands(grossPay: number, categoryConfig: AdjustedThresholds): NiBreakdown[] {
  const bands: NiBreakdown[] = [];

  // Below Lower Earnings Limit (LEL)
  const lelValue = categoryConfig.lowerEarningsLimit || 0;
  const belowLelAmount = Math.min(grossPay, lelValue);
  bands.push({
    thresholdName: 'Below Lower Earnings Limit',
    lowerThreshold: 0,
    upperThreshold: lelValue,
    rate: 0,
    amount: belowLelAmount,
    contribution: 0
  });

  // Between Lower Earnings Limit (LEL) and Primary Threshold (PT)
  const ptValue = categoryConfig.primaryThreshold;
  const betweenLelAndPtAmount = Math.max(0, Math.min(grossPay - lelValue, ptValue - lelValue));
  bands.push({
    thresholdName: 'Lower Earnings Limit to Primary Threshold',
    lowerThreshold: lelValue,
    upperThreshold: ptValue,
    rate: 0, // Will be set in calculateContributions
    amount: betweenLelAndPtAmount,
    contribution: 0 // Will be calculated in calculateContributions
  });

  // Between Primary Threshold (PT) and Upper Earnings Limit (UEL)
  const uelValue = categoryConfig.upperEarningsLimit;
  const betweenPtAndUelAmount = Math.max(0, Math.min(grossPay - ptValue, uelValue - ptValue));
  bands.push({
    thresholdName: 'Primary Threshold to Upper Earnings Limit',
    lowerThreshold: ptValue,
    upperThreshold: uelValue,
    rate: 0, // Will be set in calculateContributions
    amount: betweenPtAndUelAmount,
    contribution: 0 // Will be calculated in calculateContributions
  });

  // Above Upper Earnings Limit (UEL)
  const aboveUelAmount = Math.max(0, grossPay - uelValue);
  bands.push({
    thresholdName: 'Above Upper Earnings Limit',
    lowerThreshold: uelValue,
    upperThreshold: undefined,
    rate: 0, // Will be set in calculateContributions
    amount: aboveUelAmount,
    contribution: 0 // Will be calculated in calculateContributions
  });

  return bands;
}

/**
 * Calculate contributions for each band
 * @param bands - The earnings bands
 * @param rates - The NI rates for each band
 * @returns The updated bands with contributions
 */
function calculateContributions(bands: NiBreakdown[], rates: NiRate[]): NiBreakdown[] {
  return bands.map(band => {
    // Default rate is 0
    let rate = 0;
    
    // Find the appropriate rate for this band
    for (const rateConfig of rates) {
      if (band.lowerThreshold === rateConfig.threshold) {
        rate = rateConfig.rate;
        break;
      }
    }
    
    // Calculate contribution using the determined rate
    const contribution = roundToNearestPenny(band.amount * rate);
    
    return {
      ...band,
      rate,
      contribution
    };
  });
}

/**
 * Apply special cases to NI calculations
 * @param employeeBreakdown - The employee NI breakdown
 * @param employerBreakdown - The employer NI breakdown
 * @param input - The calculation input
 * @param categoryConfig - The NI category configuration
 */
function applySpecialCases(
  employeeBreakdown: NiBreakdown[],
  employerBreakdown: NiBreakdown[],
  input: NiCalculationInput,
  categoryConfig: AdjustedThresholds
): void {
  // Veterans relief - zero rate employer NI up to VUST threshold
  if (input.isVeteran) {
    const vust = categoryConfig.upperEarningsLimit; // VUST is set at UEL (£50,270)
    
    employerBreakdown.forEach(band => {
      // Only apply 0% rate up to VUST
      if (band.lowerThreshold < vust) {
        if (band.upperThreshold === undefined || band.upperThreshold <= vust) {
          // This band is entirely below VUST - set rate to 0%
          band.rate = 0;
          band.contribution = 0;
        } else {
          // This band crosses the VUST threshold
          // Calculate what portion of the earnings is below VUST (eligible for relief)
          const eligibleAmount = vust - band.lowerThreshold;
          const totalAmount = band.amount;
          const ineligibleAmount = totalAmount - eligibleAmount;
          
          // Recalculate the contribution (0% on eligible amount, normal rate on ineligible)
          const originalRate = categoryConfig.employerRate;
          band.contribution = roundToNearestPenny(ineligibleAmount * originalRate);
          
          // Calculate effective rate (will be lower than normal rate but not 0)
          band.rate = band.contribution / totalAmount;
        }
      }
      // Bands entirely above VUST keep their original rate and contribution
    });
  }

  // Freeport relief - zero rate employer NI up to FUST threshold
  if (input.isInFreeport) {
    const fust = categoryConfig.freeportsUpperSecondaryThreshold; // FUST is £25,000
    
    if (fust) {
      employerBreakdown.forEach(band => {
        // Only apply 0% rate up to FUST
        if (band.lowerThreshold < fust) {
          if (band.upperThreshold === undefined || band.upperThreshold <= fust) {
            // This band is entirely below FUST - set rate to 0%
            band.rate = 0;
            band.contribution = 0;
          } else {
            // This band crosses the FUST threshold
            // Calculate what portion of the earnings is below FUST (eligible for relief)
            const eligibleAmount = fust - band.lowerThreshold;
            const totalAmount = band.amount;
            const ineligibleAmount = totalAmount - eligibleAmount;
            
            // Recalculate the contribution (0% on eligible amount, normal rate on ineligible)
            const originalRate = categoryConfig.employerRate;
            band.contribution = roundToNearestPenny(ineligibleAmount * originalRate);
            
            // Calculate effective rate (will be lower than normal rate but not 0)
            band.rate = band.contribution / totalAmount;
          }
        }
        // Bands entirely above FUST keep their original rate and contribution
      });
    }
  }

  // Investment Zone relief - zero rate employer NI up to IZUST threshold
  if (input.isInInvestmentZone) {
    const izust = categoryConfig.investmentZonesUpperSecondaryThreshold; // IZUST is £25,000
    
    if (izust) {
      employerBreakdown.forEach(band => {
        // Only apply 0% rate up to IZUST
        if (band.lowerThreshold < izust) {
          if (band.upperThreshold === undefined || band.upperThreshold <= izust) {
            // This band is entirely below IZUST - set rate to 0%
            band.rate = 0;
            band.contribution = 0;
          } else {
            // This band crosses the IZUST threshold
            // Calculate what portion of the earnings is below IZUST (eligible for relief)
            const eligibleAmount = izust - band.lowerThreshold;
            const totalAmount = band.amount;
            const ineligibleAmount = totalAmount - eligibleAmount;
            
            // Recalculate the contribution (0% on eligible amount, normal rate on ineligible)
            const originalRate = categoryConfig.employerRate;
            band.contribution = roundToNearestPenny(ineligibleAmount * originalRate);
            
            // Calculate effective rate (will be lower than normal rate but not 0)
            band.rate = band.contribution / totalAmount;
          }
        }
        // Bands entirely above IZUST keep their original rate and contribution
      });
    }
  }

  // Under 21 / Apprentice under 25 - zero rate employer NI on earnings up to Upper Secondary Threshold
  if ((input.employeeAge && input.employeeAge < 21) || 
      (input.isApprentice && input.employeeAge && input.employeeAge < 25)) {
    
    // Check if we have an upper secondary threshold defined in the tax year config
    const ust = categoryConfig.upperEarningsLimit; // UST is set at UEL (£50,270)
    
    if (ust) {
      employerBreakdown.forEach(band => {
        // Only apply 0% rate up to UST
        if (band.lowerThreshold < ust) {
          if (band.upperThreshold === undefined || band.upperThreshold <= ust) {
            // This band is entirely below UST - set rate to 0%
            band.rate = 0;
            band.contribution = 0;
          } else {
            // This band crosses the UST threshold
            // Calculate what portion of the earnings is below UST (eligible for relief)
            const eligibleAmount = ust - band.lowerThreshold;
            const totalAmount = band.amount;
            const ineligibleAmount = totalAmount - eligibleAmount;
            
            // Recalculate the contribution (0% on eligible amount, normal rate on ineligible)
            const originalRate = categoryConfig.employerRate;
            band.contribution = roundToNearestPenny(ineligibleAmount * originalRate);
            
            // Calculate effective rate (will be lower than normal rate but not 0)
            band.rate = band.contribution / totalAmount;
          }
        }
        // Bands entirely above UST keep their original rate and contribution
      });
    }
  }
}

/**
 * Sum up contributions from all bands
 * @param breakdown - The NI breakdown
 * @returns The total contribution
 */
function sumContributions(breakdown: NiBreakdown[]): number {
  const sum = breakdown.reduce((sum, band) => sum + band.contribution, 0);
  return roundToNearestPenny(sum);
}

/**
 * Calculate Class 1A NICs on termination awards and sporting testimonials
 * @param input - The calculation input
 * @returns Class 1A NICs calculation results
 */
function calculateClass1ANics(input: NiCalculationInput): {
  class1aTerminationNi: number;
  terminationAwardSubjectToNi: number;
  class1aSportingTestimonialNi: number;
  sportingTestimonialSubjectToNi: number;
} {
  // Get thresholds from tax year configuration
  const niConfig = input.taxYearConfig.nationalInsurance;
  const TERMINATION_AWARD_THRESHOLD = niConfig.terminationAwardThreshold;
  const SPORTING_TESTIMONIAL_THRESHOLD = niConfig.sportingTestimonialThreshold;
  const CLASS_1A_RATE = niConfig.class1ARate;
  
  // Calculate for termination awards if present
  let class1aTerminationNi = 0;
  let terminationAwardSubjectToNi = 0;
  
  if (input.terminationAward && input.terminationAward > TERMINATION_AWARD_THRESHOLD) {
    terminationAwardSubjectToNi = input.terminationAward - TERMINATION_AWARD_THRESHOLD;
    class1aTerminationNi = roundToNearestPenny(terminationAwardSubjectToNi * CLASS_1A_RATE);
  }
  
  // Calculate for sporting testimonials if present
  let class1aSportingTestimonialNi = 0;
  let sportingTestimonialSubjectToNi = 0;
  
  if (input.sportingTestimonial && input.sportingTestimonial > SPORTING_TESTIMONIAL_THRESHOLD) {
    sportingTestimonialSubjectToNi = input.sportingTestimonial - SPORTING_TESTIMONIAL_THRESHOLD;
    class1aSportingTestimonialNi = roundToNearestPenny(sportingTestimonialSubjectToNi * CLASS_1A_RATE);
  }
  
  return {
    class1aTerminationNi,
    terminationAwardSubjectToNi,
    class1aSportingTestimonialNi,
    sportingTestimonialSubjectToNi
  };
}