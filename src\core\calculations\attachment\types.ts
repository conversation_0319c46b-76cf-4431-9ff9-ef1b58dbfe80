/**
 * Types for Attachment Order calculations
 * These types define the structure of inputs and outputs for UK attachment order calculations
 * including Attachment of Earnings Orders (AEOs), Council Tax Attachment Orders (CTAOs),
 * and Direct Earnings Attachments (DEAs).
 */

import { TaxYearConfig } from '../tax-years/types';

/**
 * Attachment order types in the UK
 */
export enum AttachmentOrderType {
  // Attachment of Earnings Order
  AEO = 'aeo',
  
  // Council Tax Attachment Order
  CTAO = 'ctao',
  
  // Direct Earnings Attachment
  DEA = 'dea',
  
  // Child Maintenance Service
  CMS = 'cms',
  
  // Student Loan Attachment Order
  SLAO = 'slao'
}

/**
 * Priority levels for attachment orders
 * This affects the order in which deductions are made
 */
export enum AttachmentOrderPriority {
  // Highest priority - processed first
  HIGH = 'high',
  
  // Medium priority - processed after high priority
  MEDIUM = 'medium',
  
  // Low priority - processed last
  LOW = 'low'
}

/**
 * Calculation methods for attachment orders
 */
export enum AttachmentOrderCalculationMethod {
  // Fixed amount deduction
  FIXED_AMOUNT = 'fixed_amount',
  
  // Percentage of earnings
  PERCENTAGE = 'percentage',
  
  // Table-based calculation (e.g., DEA tables)
  TABLE_BASED = 'table_based'
}

/**
 * Pay period types
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  TWO_WEEKLY = 'two_weekly',
  FOUR_WEEKLY = 'four_weekly',
  MONTHLY = 'monthly'
}

/**
 * DEA rate types as defined by HMRC
 */
export enum DEARateType {
  STANDARD = 'standard',
  HIGHER = 'higher'
}

/**
 * Input data for Attachment Order calculation
 */
export interface AttachmentOrderCalculationInput {
  // Type of attachment order
  orderType: AttachmentOrderType;
  
  // Priority of the attachment order
  priority: AttachmentOrderPriority;
  
  // Calculation method for the attachment order
  calculationMethod: AttachmentOrderCalculationMethod;
  
  // Pay period type
  payPeriod: PayPeriodType;
  
  // Tax year configuration
  taxYearConfig: TaxYearConfig;
  
  // Gross pay for the period
  grossPay: number;
  
  // Net pay after tax, NI, and pension deductions
  netPay: number;
  
  // Total deductions already made (tax, NI, pension, etc.)
  totalDeductions: number;
  
  // Fixed amount to deduct (if calculationMethod is FIXED_AMOUNT)
  fixedAmount?: number;
  
  // Percentage to deduct (if calculationMethod is PERCENTAGE)
  percentage?: number;
  
  // DEA rate type (if orderType is DEA)
  deaRateType?: DEARateType;
  
  // Protected earnings amount (minimum amount employee must take home)
  protectedEarnings?: number;
  
  // Total amount remaining to be paid on the attachment order
  outstandingAmount?: number;
  
  // Reference number for the attachment order
  reference?: string;
  
  // Issuing authority for the attachment order
  issuingAuthority?: string;
  
  // Previous attachment order deductions in this tax year
  previousDeductionsInTaxYear?: number;
  
  // Maximum deduction per period (if applicable)
  maximumDeductionPerPeriod?: number;
  
  // For CMS: percentage rate specified by CMS
  cmsRate?: number;
  
  // For CMS: collection fee percentage (typically 20%)
  cmsCollectionFee?: number;
  
  // For CTAO: council tax band
  councilTaxBand?: string;
  
  // For SLAO: student loan plan type
  studentLoanPlanType?: string;
  
  // Higher priority attachment orders already processed in this period
  higherPriorityDeductions?: number;
}

/**
 * Result of Attachment Order calculation
 */
export interface AttachmentOrderCalculationResult {
  // Amount to deduct for the attachment order
  deduction: number;
  
  // Whether the deduction was limited by protected earnings
  limitedByProtectedEarnings: boolean;
  
  // Whether the deduction was limited by maximum deduction per period
  limitedByMaximumDeduction: boolean;
  
  // Whether the deduction was limited by outstanding amount
  limitedByOutstandingAmount: boolean;
  
  // Amount that would have been deducted without limits
  unlimitedDeduction: number;
  
  // Remaining amount to be paid on the attachment order after this deduction
  remainingAmount?: number;
  
  // Cumulative deductions made for this attachment order in the tax year
  cumulativeDeductions: number;
  
  // Protected earnings amount used in the calculation
  protectedEarningsUsed?: number;
  
  // Net pay after this deduction
  netPayAfterDeduction: number;
  
  // For CMS: collection fee amount
  collectionFeeAmount?: number;
  
  // For CMS: amount sent to CMS (deduction minus collection fee)
  amountSentToCMS?: number;
  
  // For DEA: rate used in the calculation
  deaRateUsed?: number;
  
  // For DEA: band used in the calculation
  deaBandUsed?: string;
  
  // For CTAO: percentage used in the calculation
  ctaoPercentageUsed?: number;
}
