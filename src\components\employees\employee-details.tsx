"use client";

import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Employee } from "@/lib/schemas/employee";
import PersonalSection from "@/components/employees/sections/personal-section";
import EmploymentSection from "@/components/employees/sections/employment-section";
import StarterLeaverSection from "@/components/employees/sections/starter-leaver-section";
import PaymentSection from "@/components/employees/sections/payment-section";
import TaxSection from "@/components/employees/sections/tax-section";
import OtherSection from "@/components/employees/sections/other-Section";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { getCurrentTaxYearConfig } from "@/core/calculations/tax-years";

// Define the ref interface
export interface EmployeeDetailsRef {
  saveChanges: () => void;
}

interface EmployeeDetailsProps {
  employee: Employee;
  isNew: boolean;
  onSave: (employee: Employee) => void;
  onCancel: () => void;
  onChangesStatusUpdate?: (hasChanges: boolean) => void;
}

// Convert to forwardRef pattern
const EmployeeDetails = forwardRef<EmployeeDetailsRef, EmployeeDetailsProps>(
  ({ employee, isNew, onSave, onCancel, onChangesStatusUpdate }, ref) => {
    const [formData, setFormData] = useState<Employee>(employee);
    const [activeSection, setActiveSection] = useState<string>("personal");
    const [originalData, setOriginalData] = useState<Employee>(employee);
    const [hasChanges, setHasChanges] = useState(false);
    const [validationErrors, setValidationErrors] = useState<
      Record<string, string>
    >({});
    const [attemptedAction, setAttemptedAction] = useState<string | null>(null);

    // Update formData and originalData when employee prop changes
    useEffect(() => {
      setFormData(employee);
      setOriginalData(employee);

      // Always set active section to personal for new employees
      if (isNew) {
        setActiveSection("personal");
      }
    }, [employee, isNew]);

    // Track changes to enable/disable save/cancel buttons
    useEffect(() => {
      // Simple deep comparison - in a real app you might want to use a library like fast-deep-equal
      const jsonOriginal = JSON.stringify(originalData);
      const jsonCurrent = JSON.stringify(formData);
      const currentHasChanges = jsonOriginal !== jsonCurrent;

      setHasChanges(currentHasChanges);

      // Notify parent component about change status
      if (onChangesStatusUpdate) {
        onChangesStatusUpdate(currentHasChanges);
      }
    }, [formData, originalData, onChangesStatusUpdate]);

    const handleInputChange = (section: string, field: string, value: any) => {
      // Special handling for starter declaration changes to update tax code
      if (section === "starterLeaver" && field === "starterDeclaration") {
        const startDeclaration = value;
        if (startDeclaration) {
          // Get the standard tax code based on personal allowance
          const currentTaxYear = getCurrentTaxYearConfig();
          const taxCodeNumber = Math.floor(
            currentTaxYear.paye.personalAllowance / 10,
          );
          const standardTaxCode = `${taxCodeNumber}L`;

          // Determine the appropriate tax code based on starter declaration
          let newTaxCode = standardTaxCode;
          let newWeek1Month1 = false;

          const hasPreviousEmploymentPay =
            formData.previousEmploymentPay !== undefined;

          switch (startDeclaration) {
            case "A":
              // First job since 6th April - standard tax code, not week1Month1
              newTaxCode = standardTaxCode;
              newWeek1Month1 = false;
              break;

            case "B":
              // Had another job or benefits - standard code, week1Month1 depends on previous pay
              newTaxCode = standardTaxCode;
              newWeek1Month1 = !hasPreviousEmploymentPay; // If no previous pay info, use week1Month1
              break;

            case "C":
              // Second job or pension - BR code, not week1Month1
              newTaxCode = "BR";
              newWeek1Month1 = false;
              break;
          }

          // Update both the starter declaration and tax code in one go
          setFormData((prev) => ({
            ...prev,
            [field]: value,
            taxCode: newTaxCode,
            week1Month1: newWeek1Month1,
          }));

          // Exit early as we've already updated the form data
          return;
        }
      }

      // Special handling for previousEmploymentPay when declaration is B
      if (
        section === "starterLeaver" &&
        field === "previousEmploymentPay" &&
        formData.starterDeclaration === "B"
      ) {
        // For declaration B, week1Month1 should be false if we have previous pay info
        const newWeek1Month1 = value === undefined;

        setFormData((prev) => ({
          ...prev,
          [field]: value,
          week1Month1: newWeek1Month1,
        }));

        return;
      }

      // Default handling for all other cases
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    };

    // Handle validation status changes from child components
    const handleValidationChange = (
      section: string,
      isValid: boolean,
      message?: string,
    ) => {
      setValidationErrors((prev) => {
        if (isValid) {
          // Remove error for this section if it's now valid
          const newErrors = { ...prev };
          delete newErrors[section];
          return newErrors;
        } else {
          // Add or update error for this section
          return {
            ...prev,
            [section]: message || `Validation error in ${section} section`,
          };
        }
      });
    };

    // Check if there are any validation errors
    const hasValidationErrors = () => {
      return Object.keys(validationErrors).length > 0;
    };

    const handleSave = () => {
      // Check for validation errors before saving
      if (hasValidationErrors()) {
        setAttemptedAction("save");
        return;
      }

      onSave(formData);
      // Reset the original data to match the saved data
      setOriginalData(formData);
      setHasChanges(false);
      setAttemptedAction(null);
    };

    const handleCancel = () => {
      // Reset form data to original data
      setFormData(originalData);
      setHasChanges(false);
      setValidationErrors({});
      setAttemptedAction(null);
      onCancel();
    };

    // Handle section change with validation
    const handleSectionChange = (sectionId: string) => {
      // If there are validation errors, don't allow changing sections
      if (hasValidationErrors()) {
        setAttemptedAction(sectionId);
        return;
      }

      setActiveSection(sectionId);
      setAttemptedAction(null);
    };

    // Define section navigation items
    const navigationSections = [
      { id: "personal", label: "Personal" },
      { id: "starterLeaver", label: "Starter/Leaver" },
      { id: "tax", label: "Tax, NIC, RTI" },
      { id: "payment", label: "Payment" },
      { id: "employment", label: "Employment" },
      { id: "other", label: "Other" },
    ];

    // Function to render the active section
    const renderActiveSection = () => {
      switch (activeSection) {
        case "personal":
          return (
            <PersonalSection employee={formData} onChange={handleInputChange} />
          );
        case "employment":
          return (
            <EmploymentSection
              employee={formData}
              onChange={handleInputChange}
            />
          );
        case "starterLeaver":
          return (
            <StarterLeaverSection
              employee={formData}
              onChange={handleInputChange}
              isNew={isNew}
            />
          );
        case "payment":
          return (
            <PaymentSection employee={formData} onChange={handleInputChange} />
          );
        case "tax":
          return (
            <TaxSection
              employee={formData}
              onChange={handleInputChange}
              onValidationChange={(isValid, message) =>
                handleValidationChange("tax", isValid, message)
              }
            />
          );
        case "other":
          return (
            <OtherSection employee={formData} onChange={handleInputChange} />
          );
        default:
          return (
            <PersonalSection employee={formData} onChange={handleInputChange} />
          );
      }
    };

    // Expose methods via useImperativeHandle
    useImperativeHandle(ref, () => ({
      saveChanges: () => {
        handleSave();
      },
    }));

    return (
      <div className="flex h-full flex-1 flex-col overflow-auto">
        {/* Horizontal navigation */}
        <div className="flex gap-1 border-b-2 pb-2">
          {navigationSections.map((section) => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? "default" : "ghost"}
              size="sm"
              onClick={() => handleSectionChange(section.id)}
              className="h-10 px-4 hover:bg-slate-200 dark:hover:bg-zinc-800"
            >
              {section.label}
            </Button>
          ))}
        </div>

        {/* Validation error alert */}
        {attemptedAction && hasValidationErrors() && (
          <Alert variant="destructive" className="mx-2 mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <p>
                Cannot{" "}
                {attemptedAction === "save" ? "save changes" : "change section"}{" "}
                due to validation errors:
              </p>
              <ul className="mt-1 list-disc pl-6">
                {Object.entries(validationErrors).map(([section, message]) => (
                  <li key={section}>{message}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Content area - only shows active section */}
        <div className="flex-1 overflow-y-auto px-2 py-4">
          {renderActiveSection()}
        </div>

        {/* Bottom buttons */}
        <div className="flex items-center justify-between gap-3 border-t p-4">
          {isNew && (
            <span className="font-medium text-sky-600">
              Creating New Employee
            </span>
          )}
          <div className={`mx-auto flex gap-3 ${isNew ? "" : "ml-auto"}`}>
            <Button
              variant="outline"
              onClick={handleCancel}
              size="sm"
              disabled={!hasChanges && !isNew}
              className={
                !hasChanges && !isNew
                  ? "cursor-not-allowed opacity-50"
                  : "hover:bg-red-500/90 hover:text-white dark:hover:bg-red-500/90"
              }
            >
              {isNew ? "Cancel" : "Cancel Changes"}
            </Button>
            <Button
              onClick={handleSave}
              size="sm"
              disabled={!hasChanges}
              className={
                !hasChanges
                  ? "cursor-not-allowed opacity-50"
                  : "hover:bg-emerald-500"
              }
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    );
  },
);

// Set display name for debugging
EmployeeDetails.displayName = "EmployeeDetails";

export default EmployeeDetails;
