"use client";

import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { NumberInput } from "@/components/ui/number-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Plus, AlertTriangle } from "lucide-react";
import { Employee } from "@/lib/schemas/employee";

interface EmploymentSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
}

const EmploymentSection: React.FC<EmploymentSectionProps> = ({
  employee,
  onChange,
}) => {
  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    onChange("employment", field, value);
  };

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    handleChange("workingDays", {
      ...employee.workingDays,
      [day]: checked,
    });
  };

  // Function to get the maximum number of days for a given month
  const getMaxDaysInMonth = (month: string): number => {
    const monthsWith31Days = [
      "January",
      "March",
      "May",
      "July",
      "August",
      "October",
      "December",
    ];
    const monthsWith30Days = ["April", "June", "September", "November"];

    if (monthsWith31Days.includes(month)) {
      return 31;
    } else if (monthsWith30Days.includes(month)) {
      return 30;
    } else {
      // February (assuming non-leap year for simplicity)
      return 29;
    }
  };

  // Parse the current leave year start value
  const parseLeaveYearStart = (): { day: string; month: string } => {
    const parts = (employee.leaveYearStarts || "6 April").split(" ");
    return {
      day: parts[0] || "6",
      month: parts[1] || "April",
    };
  };

  // Get current day and month values and track if the combination is valid
  const { day, month } = parseLeaveYearStart();
  const [isValidCombination, setIsValidCombination] = useState<boolean>(true);

  // Effect to validate the current day/month combination
  useEffect(() => {
    const dayNum = parseInt(day, 10);
    const maxDays = getMaxDaysInMonth(month);
    setIsValidCombination(dayNum <= maxDays);
  }, [day, month]);

  // Calculate the previous leave year end date
  const calculatePreviousLeaveYearEnd = (): string => {
    // Get the current year (we'll use this to determine the appropriate year)
    const currentYear = new Date().getFullYear();
    const currentDate = new Date();

    // Month indices for date object (0-based)
    const monthIndices: Record<string, number> = {
      January: 0,
      February: 1,
      March: 2,
      April: 3,
      May: 4,
      June: 5,
      July: 6,
      August: 7,
      September: 8,
      October: 9,
      November: 10,
      December: 11,
    };

    // Create a date for this year's leave start
    const leaveStartThisYear = new Date(
      currentYear,
      monthIndices[month],
      parseInt(day, 10),
    );

    // Create the end date (day before leave start)
    const endDate = new Date(leaveStartThisYear);
    endDate.setDate(endDate.getDate() - 1);

    // If the end date is in the future relative to today, use previous year's end date
    if (endDate > currentDate) {
      endDate.setFullYear(endDate.getFullYear() - 1);
    }

    // Format the date as DD/MM/YYYY
    return `${endDate.getDate().toString().padStart(2, "0")}/${(endDate.getMonth() + 1).toString().padStart(2, "0")}/${endDate.getFullYear()}`;
  };

  // Get the previous leave year end date
  const previousLeaveYearEnd = calculatePreviousLeaveYearEnd();

  // Get the current calculation method
  const calculationMethod =
    employee.annualLeaveCalculationMethod || "Set number of days";

  // Check if leave functionality should be disabled
  const isLeaveDisabled =
    calculationMethod === "Rolled up holiday pay" ||
    calculationMethod === "No entitlement";

  // Check if the entitlement field should be disabled
  const isEntitlementDisabled =
    isLeaveDisabled ||
    calculationMethod === "Accrued by days worked" ||
    calculationMethod === "Accrued by hours worked";

  // Determine the label and units based on calculation method
  const getEntitlementLabel = () => {
    switch (calculationMethod) {
      case "Set number of hours":
        return "Leave hours";
      default:
        return "Leave days";
    }
  };

  // Get the unit label for the entitlement field
  const getEntitlementUnit = () => {
    switch (calculationMethod) {
      case "Set number of hours":
      case "Accrued by hours worked":
        return "hours";
      default:
        return "days";
    }
  };

  // State for adjustment unit (days or hours)
  const [adjustmentUnit, setAdjustmentUnit] = useState<string>("days");

  // State for carry over unit (days or hours)
  const [carryOverUnit, setCarryOverUnit] = useState<string>("days");

  // Effect to clear entitlement when calculation method changes to disabled options
  useEffect(() => {
    if (isEntitlementDisabled && employee.annualLeaveEntitlement !== 0) {
      handleChange("annualLeaveEntitlement", 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    calculationMethod,
    isEntitlementDisabled,
    employee.annualLeaveEntitlement,
  ]);

  return (
    <div className="space-y-1 pt-4">
      {/* Works Number */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label
          htmlFor="worksNumber"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Works number
        </Label>
        <div className="col-span-3">
          <Input
            id="worksNumber"
            className="w-full text-sm"
            placeholder="Optional"
            value={employee.worksNumber || ""}
            onChange={(e) => handleChange("worksNumber", e.target.value)}
          />
        </div>
      </div>

      {/* Departments */}
      <div className="grid grid-cols-10 items-center gap-2 pt-4">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium">
          Departments
        </Label>
        <div className="col-span-8">
          <Button
            variant="link"
            size="sm"
            className="h-auto p-0 text-xs text-blue-500"
          >
            <Plus className="mr-1 h-3 w-3" /> Add department association
          </Button>
        </div>
      </div>

      {/* Leave Year and Annual Leave */}
      <div className="grid grid-cols-10 items-center gap-2 pt-6">
        <Label
          htmlFor="leaveYearStarts"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Leave year starts
        </Label>
        <div className="col-span-2">
          {/* Custom day/month selectors */}
          <div className="flex gap-2">
            <div className="col-span-1">
              <Select
                value={day}
                onValueChange={(value) => {
                  // Update the day part of the leave year
                  handleChange("leaveYearStarts", `${value} ${month}`);
                }}
              >
                <SelectTrigger
                  className={`text-sm ${!isValidCombination ? "border-red-500" : ""}`}
                >
                  <SelectValue placeholder="Day" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 31 }, (_, i) => i + 1).map((dayNum) => (
                    <SelectItem key={dayNum} value={dayNum.toString()}>
                      {dayNum}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="relative flex-1">
              <Select
                value={month}
                onValueChange={(value) => {
                  // Update the month part of the leave year
                  handleChange("leaveYearStarts", `${day} ${value}`);
                }}
              >
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                  {[
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December",
                  ].map((monthName) => (
                    <SelectItem key={monthName} value={monthName}>
                      {monthName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error message for invalid combinations */}
          {!isValidCombination && (
            <div className="mt-1 flex items-center text-xs text-red-500">
              <AlertTriangle className="mr-1 h-3 w-3" />
              {day} is not valid for {month}. Maximum is{" "}
              {getMaxDaysInMonth(month)}.
            </div>
          )}
        </div>
        <Label
          htmlFor="annualLeaveCalculationMethod"
          className="col-span-1 mx-4 justify-self-end text-right font-medium"
        >
          Calculation
        </Label>
        <div className="col-span-2">
          <Select
            defaultValue={
              employee.annualLeaveCalculationMethod || "Set number of days"
            }
            onValueChange={(value) =>
              handleChange("annualLeaveCalculationMethod", value)
            }
          >
            <SelectTrigger
              id="annualLeaveCalculationMethod"
              className="text-sm"
            >
              <SelectValue placeholder="Select method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Set number of days">
                Set number of days
              </SelectItem>
              <SelectItem value="Set number of hours">
                Set number of hours
              </SelectItem>
              <SelectItem value="Accrued by days worked">
                Accrued by days worked
              </SelectItem>
              <SelectItem value="Accrued by hours worked">
                Accrued by hours worked
              </SelectItem>
              <SelectItem value="Rolled up holiday pay">
                Rolled up holiday pay
              </SelectItem>
              <SelectItem value="No entitlement">No entitlement</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Annual Leave Entitlement */}
      <div className="grid grid-cols-10 items-center gap-2 pt-4">
        <Label
          htmlFor="annualLeaveEntitlement"
          className={`col-span-2 mx-4 justify-self-end text-right font-medium ${isEntitlementDisabled ? "text-muted-foreground" : ""}`}
        >
          {getEntitlementLabel()}
        </Label>
        <div className="col-span-2">
          <div className="flex">
            <NumberInput
              id="annualLeaveEntitlement"
              min={0}
              decimalPlaces={calculationMethod.includes("hours") ? 2 : 2}
              className="text-sm"
              value={
                isEntitlementDisabled
                  ? 0
                  : (employee.annualLeaveEntitlement ?? undefined)
              }
              onChange={(value) =>
                handleChange("annualLeaveEntitlement", value)
              }
              disabled={isEntitlementDisabled}
            />
            <div
              className={`flex items-center justify-center rounded-r-md border border-l-0 ${isEntitlementDisabled ? "bg-muted/30" : "bg-muted"} px-3 text-sm ${isEntitlementDisabled ? "text-muted-foreground" : ""}`}
            >
              {getEntitlementUnit()}
            </div>
          </div>
        </div>
        <div className="col-span-1 flex items-center justify-center font-bold text-yellow-500">
          {!isEntitlementDisabled && "≈"}
        </div>
        <div className="col-span-2">
          {!isEntitlementDisabled && (
            <div className="flex">
              <Input
                readOnly
                value="5.6"
                className="bg-muted/50 rounded-r-none text-sm"
              />
              <div className="bg-muted flex items-center justify-center rounded-r-md border border-l-0 px-3 text-sm">
                weeks
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Annual Leave Adjustment */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          className={`col-span-2 mx-4 justify-self-end text-right font-medium ${isLeaveDisabled ? "text-muted-foreground" : ""}`}
        >
          Adjustments
        </Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="annualLeaveAdjustment"
              checked={employee.annualLeaveAdjustment}
              onCheckedChange={(checked) =>
                handleChange("annualLeaveAdjustment", Boolean(checked))
              }
              disabled={isLeaveDisabled}
            />
            <Label
              htmlFor="annualLeaveAdjustment"
              className={`text-sm ${isLeaveDisabled ? "text-muted-foreground" : ""}`}
            >
              Include additional annual leave
            </Label>
            <NumberInput
              id="annualLeaveAdjustmentValue"
              min={0}
              decimalPlaces={2}
              className="w-20 text-sm"
              value={employee.annualLeaveAdjustmentValue ?? 0}
              onChange={(value) =>
                handleChange("annualLeaveAdjustmentValue", value)
              }
              disabled={isLeaveDisabled || !employee.annualLeaveAdjustment}
            />
            <Select
              value={adjustmentUnit}
              onValueChange={(value) => {
                setAdjustmentUnit(value);
                handleChange("annualLeaveAdjustmentUnit", value);
              }}
              disabled={isLeaveDisabled || !employee.annualLeaveAdjustment}
            >
              <SelectTrigger className="w-24 text-sm">
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="days">days</SelectItem>
                <SelectItem value="hours">hours</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Annual Leave Carry Over */}
      <div className="grid grid-cols-10 items-center gap-2 pt-4">
        <Label
          className={`col-span-2 mx-4 justify-self-end text-right font-medium ${isLeaveDisabled ? "text-muted-foreground" : ""}`}
        >
          Carry over
        </Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="annualLeaveCarryOver"
              checked={employee.annualLeaveCarryOver}
              onCheckedChange={(checked) =>
                handleChange("annualLeaveCarryOver", Boolean(checked))
              }
              disabled={isLeaveDisabled}
            />
            <Label
              htmlFor="annualLeaveCarryOver"
              className={`text-sm ${isLeaveDisabled ? "text-muted-foreground" : ""}`}
            >
              Carry over annual leave from year ending {previousLeaveYearEnd}
            </Label>
            <NumberInput
              id="annualLeaveCarryOverValue"
              min={0}
              decimalPlaces={2}
              className="w-20 text-sm"
              value={employee.annualLeaveCarryOverValue ?? 0}
              onChange={(value) =>
                handleChange("annualLeaveCarryOverValue", value)
              }
              disabled={isLeaveDisabled || !employee.annualLeaveCarryOver}
            />
            <Select
              value={carryOverUnit}
              onValueChange={(value) => {
                setCarryOverUnit(value);
                handleChange("annualLeaveCarryOverUnit", value);
              }}
              disabled={isLeaveDisabled || !employee.annualLeaveCarryOver}
            >
              <SelectTrigger className="w-24 text-sm">
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="days">days</SelectItem>
                <SelectItem value="hours">hours</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Working Days */}
      <div className="grid grid-cols-10 items-start gap-2 pt-6">
        <Label className="col-span-2 mx-4 justify-self-end pt-1 text-right font-medium">
          Working days
        </Label>
        <div className="col-span-8 grid grid-cols-2 gap-x-4 gap-y-2 md:grid-cols-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="monday"
              checked={employee.workingDays.monday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("monday", Boolean(checked))
              }
            />
            <Label htmlFor="monday" className="text-sm">
              Mondays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="tuesday"
              checked={employee.workingDays.tuesday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("tuesday", Boolean(checked))
              }
            />
            <Label htmlFor="tuesday" className="text-sm">
              Tuesdays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="wednesday"
              checked={employee.workingDays.wednesday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("wednesday", Boolean(checked))
              }
            />
            <Label htmlFor="wednesday" className="text-sm">
              Wednesdays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="thursday"
              checked={employee.workingDays.thursday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("thursday", Boolean(checked))
              }
            />
            <Label htmlFor="thursday" className="text-sm">
              Thursdays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="friday"
              checked={employee.workingDays.friday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("friday", Boolean(checked))
              }
            />
            <Label htmlFor="friday" className="text-sm">
              Fridays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="saturday"
              checked={employee.workingDays.saturday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("saturday", Boolean(checked))
              }
            />
            <Label htmlFor="saturday" className="text-sm">
              Saturdays
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sunday"
              checked={employee.workingDays.sunday}
              onCheckedChange={(checked) =>
                handleWorkingDayChange("sunday", Boolean(checked))
              }
            />
            <Label htmlFor="sunday" className="text-sm">
              Sundays
            </Label>
          </div>
        </div>
      </div>

      {/* Minimum Wage Profile */}
      <div className="grid grid-cols-10 items-center gap-2 pt-6">
        <Label
          htmlFor="minimumWageProfile"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Min. wage
        </Label>
        <div className="col-span-4">
          <Select
            defaultValue={
              employee.minimumWageProfile || "National Minimum/Living Wage"
            }
            onValueChange={(value) => handleChange("minimumWageProfile", value)}
          >
            <SelectTrigger id="minimumWageProfile" className="text-sm">
              <SelectValue placeholder="Select minimum wage profile" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="National Minimum/Living Wage">
                National Minimum/Living Wage
              </SelectItem>
              <SelectItem value="Apprentice">Apprentice</SelectItem>
              <SelectItem value="Not Applicable">Not Applicable</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Typical Hours Worked */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="typicalHoursWorked"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Typical hours
        </Label>
        <div className="col-span-2">
          <div className="flex">
            <NumberInput
              id="typicalHoursWorked"
              min={0}
              decimalPlaces={2}
              className="rounded-r-none text-sm"
              value={employee.typicalHoursWorked ?? undefined}
              onChange={(value) => handleChange("typicalHoursWorked", value)}
            />
            <div className="bg-muted col-span-2 flex items-center justify-center rounded-r-md border border-l-0 px-3 text-sm whitespace-nowrap">
              per week
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmploymentSection;
