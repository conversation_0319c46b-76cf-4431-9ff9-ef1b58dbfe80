# UK Payroll Application - Technical Guide Part 3: Custom Database Hooks

## Custom Hooks for Database Access

These React hooks provide a convenient way to access the database functionality from within React components. They encapsulate the database operations and provide a simple, consistent API.

### Master Database Hook

The `useMasterDatabase` hook provides access to the application's master database.

```typescript
// src/hooks/use-master-database.ts
import { useCallback, useEffect, useState } from 'react';
import { getMasterDb } from '@/db/adapters/master';
import { employers } from '@/db/schema/master';
import { eq } from 'drizzle-orm';

export function useMasterDatabase() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [db, setDb] = useState<Awaited<ReturnType<typeof getMasterDb>> | null>(null);

  useEffect(() => {
    const initDb = async () => {
      try {
        setIsLoading(true);
        const masterDb = await getMasterDb();
        setDb(masterDb);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to initialize master database'));
        console.error('Master database initialization error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initDb();
  }, []);

  const getRecentEmployers = useCallback(async (limit: number = 5) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      return await db.select()
        .from(employers)
        .orderBy(employers.lastAccessed)
        .limit(limit);
    } catch (err) {
      console.error('Error fetching recent employers:', err);
      throw err;
    }
  }, [db]);

  const getEmployerById = useCallback(async (id: string) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const results = await db.select()
        .from(employers)
        .where(eq(employers.id, id))
        .limit(1);
      
      return results[0] || null;
    } catch (err) {
      console.error(`Error fetching employer ${id}:`, err);
      throw err;
    }
  }, [db]);

  return {
    db,
    isLoading,
    error,
    getRecentEmployers,
    getEmployerById
  };
}
```

### Employer Database Hook

The `useEmployerDatabase` hook manages employer-specific database operations.

```typescript
// src/hooks/use-employer-database.ts
import { useCallback, useEffect, useState } from 'react';
import { getEmployerDbAdapter } from '@/db/adapters/employer';
import type { NewEmployerDetails } from '@/db/adapters/employer';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import path from 'path';

export function useEmployerDatabase() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [currentEmployerId, setCurrentEmployerId] = useState<string | null>(null);
  const [currentEmployerDb, setCurrentEmployerDb] = useState<ReturnType<typeof drizzle> | null>(null);

  // Clean up database connection on unmount
  useEffect(() => {
    return () => {
      if (currentEmployerId) {
        getEmployerDbAdapter().close(currentEmployerId).catch(console.error);
      }
    };
  }, [currentEmployerId]);

  const openEmployer = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Close previous connection if any
      if (currentEmployerId && currentEmployerId !== id) {
        await getEmployerDbAdapter().close(currentEmployerId);
      }
      
      // Open new connection
      const db = await getEmployerDbAdapter().open(id);
      
      setCurrentEmployerId(id);
      setCurrentEmployerDb(db);
      
      return db;
    } catch (err) {
      setError(err instanceof Error ? err : new Error(`Failed to open employer database ${id}`));
      console.error(`Error opening employer ${id}:`, err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [currentEmployerId]);

  const createEmployer = useCallback(async (details: NewEmployerDetails) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getEmployerDbAdapter().create(details);
      
      // Open the newly created employer
      const db = await getEmployerDbAdapter().open(result.id);
      
      setCurrentEmployerId(result.id);
      setCurrentEmployerDb(db);
      
      return { id: result.id, db };
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create employer database'));
      console.error('Error creating employer:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const closeEmployer = useCallback(async () => {
    if (currentEmployerId) {
      try {
        await getEmployerDbAdapter().close(currentEmployerId);
        setCurrentEmployerId(null);
        setCurrentEmployerDb(null);
      } catch (err) {
        console.error(`Error closing employer ${currentEmployerId}:`, err);
      }
    }
  }, [currentEmployerId]);

  const getRecentEmployers = useCallback(async (limit: number = 10) => {
    try {
      return await getEmployerDbAdapter().getRecentlyAccessed(limit);
    } catch (err) {
      console.error('Error fetching recent employers:', err);
      throw err;
    }
  }, []);

  const scanDefaultDirectory = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get the default directory from settings or use the default
      let defaultDirectory = '';
      
      if (typeof window !== 'undefined' && window.electronAPI) {
        defaultDirectory = path.join(
          window.electronAPI.getPath('userData'),
          'employers'
        );
      } else {
        defaultDirectory = path.join(process.cwd(), '.dev-data', 'employers');
      }
      
      return await getEmployerDbAdapter().scanDirectory(defaultDirectory);
    } catch (err) {
      console.error('Error scanning default directory:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addExistingFile = useCallback(async (filePath: string) => {
    try {
      setIsLoading(true);
      return await getEmployerDbAdapter().addExistingFile(filePath);
    } catch (err) {
      console.error('Error adding existing file:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    db: currentEmployerDb,
    employerId: currentEmployerId,
    isLoading,
    error,
    openEmployer,
    createEmployer,
    closeEmployer,
    getRecentEmployers,
    scanDefaultDirectory,
    addExistingFile
  };
}
```

## TanStack Query Integration

### React Query Provider

The React Query Provider sets up TanStack Query for the application with optimized settings.

```tsx
// src/providers/query-provider.tsx
'use client';

import { ReactNode } from 'react';
import { 
  QueryClient, 
  QueryClientProvider,
  MutationCache, 
  QueryCache
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Data remains fresh for 5 minutes in most cases
      staleTime: 5 * 60 * 1000,
      // Keep unused data cached for 30 minutes
      gcTime: 30 * 60 * 1000,
      // Retry 3 times with exponential backoff
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch on window focus
      refetchOnWindowFocus: true,
    },
  },
  queryCache: new QueryCache({
    onError: (error, query) => {
      // Log errors but don't display them if data exists
      console.error(`Query error: ${error.message}`, query);
    },
  }),
  mutationCache: new MutationCache({
    onError: (error) => {
      console.error(`Mutation error: ${error.message}`);
      // Here you could trigger a toast notification
    }
  }),
});

export function ReactQueryProvider({ children }: { children: ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}
```

### TanStack Query Hooks for Employers

These hooks provide a clean interface for querying and mutating employer data with TanStack Query.

```typescript
// src/hooks/use-query/employers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getEmployerDbAdapter } from '@/db/adapters/employer';
import { useEmployerDatabase } from '@/hooks/use-employer-database';
import type { NewEmployerDetails } from '@/db/adapters/employer';

// Query hook for getting recent employers
export function useRecentEmployers(limit: number = 10) {
  return useQuery({
    queryKey: ['employers', 'recent', limit],
    queryFn: async () => {
      return await getEmployerDbAdapter().getRecentlyAccessed(limit);
    },
  });
}

// Query hook for getting all employers
export function useAllEmployers() {
  return useQuery({
    queryKey: ['employers', 'all'],
    queryFn: async () => {
      return await getEmployerDbAdapter().getAll();
    },
  });
}

// Query hook for getting a single employer
export function useEmployer(employerId: string | null) {
  const { db } = useEmployerDatabase();
  
  return useQuery({
    queryKey: ['employer', employerId],
    queryFn: async () => {
      if (!employerId || !db) throw new Error('Employer ID or database not available');
      
      const result = await db.query.employer.findFirst({
        where: (employer, { eq }) => eq(employer.id, employerId)
      });
      
      if (!result) throw new Error('Employer not found');
      return result;
    },
    // Only run the query if we have an employer ID and database
    enabled: !!employerId && !!db,
  });
}

// Mutation hook for creating an employer
export function useCreateEmployer() {
  const queryClient = useQueryClient();
  const { createEmployer } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: (details: NewEmployerDetails) => createEmployer(details),
    onSuccess: (result) => {
      // Invalidate queries that might be affected
      queryClient.invalidateQueries({ queryKey: ['employers'] });
      
      // Optimistically add to the recent employers list
      queryClient.setQueryData(['employers', 'recent'], (old: any[]) => {
        return [result, ...(old || [])].slice(0, 10);
      });
    },
  });
}

// Mutation hook for opening an employer
export function useOpenEmployer() {
  const { openEmployer } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: (id: string) => openEmployer(id),
  });
}

// Mutation hook for scanning for employer files
export function useScanForEmployers() {
  const queryClient = useQueryClient();
  const { scanDefaultDirectory } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: () => scanDefaultDirectory(),
    onSuccess: (result) => {
      // Invalidate queries that might be affected
      queryClient.invalidateQueries({ queryKey: ['employers'] });
    },
  });
}

// Mutation hook for adding an existing employer file
export function useAddExistingEmployerFile() {
  const queryClient = useQueryClient();
  const { addExistingFile } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: (filePath: string) => addExistingFile(filePath),
    onSuccess: (result) => {
      // Invalidate queries that might be affected
      queryClient.invalidateQueries({ queryKey: ['employers'] });
    },
  });
}
```
