import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export function AutomationTab() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Automation</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Batch Processing</CardTitle>
            <CardDescription>Process multiple employers at once.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Automatic Pay Calculation</Label>
                <p className="text-sm text-muted-foreground">
                  Calculate pay runs automatically on their scheduled dates.
                </p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Scheduled Backups</Label>
                <p className="text-sm text-muted-foreground">
                  Create automatic backups on a schedule.
                </p>
              </div>
              <Switch />
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">Run Batch Process Now</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>RTI Automation</CardTitle>
            <CardDescription>Automate RTI submissions to HMRC.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Automatic FPS Submission</Label>
                <p className="text-sm text-muted-foreground">
                  Send FPS automatically after payroll is finalized.
                </p>
              </div>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Check for Tax Code Notices</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically check HMRC for tax code notices.
                </p>
              </div>
              <Switch />
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">Send Pending Submissions</Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
