PRAGMA foreign_keys=off;

BEGIN TRANSACTION;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS employers;
DROP TABLE IF EXISTS app_settings;

-- Create employers table with the final schema
CREATE TABLE employers (
    id TEXT PRIMARY KEY,
    display_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'open',
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    last_opened_at INTEGER,
    paye_ref TEXT DEFAULT NULL,
    accounts_ref TEXT DEFAULT NULL,
    next_pay_date TEXT DEFAULT NULL,
    employees INTEGER DEFAULT NULL,
    tax_code_notices INTEGER DEFAULT NULL,
    rti_submissions INTEGER DEFAULT NULL,
    notifications INTEGER DEFAULT NULL
);

-- Create app_settings table
CREATE TABLE app_settings (
    id TEXT PRIMARY KEY DEFAULT 'singleton',
    default_employer_directory TEXT NOT NULL
);

COMMIT;

PRAGMA foreign_keys=on;