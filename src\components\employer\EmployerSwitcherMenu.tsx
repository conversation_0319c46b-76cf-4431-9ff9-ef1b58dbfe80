"use client";

import React from "react";
import { <PERSON>u, <PERSON>, Loader2 } from "lucide-react";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import path from "path";
import { useNavigationStore } from "@/store/navigation-store";

export const EmployerSwitcherMenu: React.FC = () => {
  const { openEmployers, activeEmployerId, switchEmployer, closeEmployer } =
    useEmployerDBContext();
  const setActiveEmployer = useNavigationStore((s: any) => s.setActiveEmployer);

  // Show only the base filename (no directory, no extension)
  const getFileName = (dbPath: string) => {
    // Remove everything before the last slash or backslash, then remove the extension
    const base = dbPath.split(/[/\\]/).pop() || dbPath;
    return base.replace(/\.[^/.]+$/, "");
  };

  return (
    <React.Fragment>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            aria-label="Switch Employer"
            className="ml-2"
          >
            <Menu className="size-6" />
          </Button>
        </DropdownMenuTrigger>
        {/* Show active employer indicator to the right of the menu button */}
        {(() => {
          const activeEmployer = openEmployers.find(
            (e) => e.id === activeEmployerId,
          );
          return (
            <span className="ml-2 text-sm font-medium text-sky-700">
              {activeEmployer ? (
                getFileName(activeEmployer.dbPath)
              ) : (
                <span className="text-slate-400">No employer</span>
              )}
            </span>
          );
        })()}

        <DropdownMenuContent align="start" className="min-w-[220px]">
          <DropdownMenuLabel>Open Employers</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {openEmployers.length === 0 && (
            <DropdownMenuItem disabled>No employers open</DropdownMenuItem>
          )}
          {openEmployers.map((emp) => (
            <DropdownMenuItem
              key={emp.id}
              onClick={() => {
                switchEmployer(emp.id);
                setActiveEmployer(emp.id); // Synchronise Zustand store for UI and query updates
              }}
              className={
                emp.id === activeEmployerId ? "bg-accent/60 font-semibold" : ""
              }
            >
              <span className="flex w-full items-center gap-2">
                {emp.isLoading ? (
                  <Loader2 className="text-muted-foreground size-4 animate-spin" />
                ) : null}
                <span className="truncate" title={getFileName(emp.dbPath)}>
                  {getFileName(emp.dbPath)}
                </span>
                {emp.id === activeEmployerId && (
                  <span className="ml-auto text-xs text-sky-600">Active</span>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-muted-foreground hover:text-destructive ml-2 h-5 w-5 p-0"
                  onClick={async (e) => {
                    e.stopPropagation();
                    await closeEmployer(emp.id);
                  }}
                  aria-label={`Close ${getFileName(emp.dbPath)}`}
                >
                  <X className="size-4" />
                </Button>
              </span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </React.Fragment>
  );
};

export default EmployerSwitcherMenu;
