import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"

interface UnsavedChangesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onContinue: () => void
  onDiscard: () => void
  onSave: () => void
}

export function UnsavedChangesDialog({
  open,
  onOpenChange,
  onContinue,
  onDiscard,
  onSave
}: UnsavedChangesDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Unsaved Changes</DialogTitle>
          <DialogDescription>
            You have unsaved changes. What would you like to do?<br/>
            You can click anywhere outside this box or hit <span className="font-bold">Esc</span> to continue editing.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" 
                onClick={onContinue}
                className="border-2 border-sky-500">
            Continue Editing
          </Button>
          <div className="flex gap-2">
            <Button variant="destructive" onClick={onDiscard}>
              Discard Changes
            </Button>
            <Button className="bg-emerald-500" onClick={onSave}>
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}