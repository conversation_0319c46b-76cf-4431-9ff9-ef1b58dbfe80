import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Employer } from '@/lib/schemas/employer';

interface RTISubmissionsSectionProps {
  employer: Employer;
  onChange: (field: string, value: any) => void;
}

const RTISubmissionsSection: React.FC<RTISubmissionsSectionProps> = ({ 
  employer, 
  onChange 
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange(name, value);
  };

  const handleSelectChange = (field: string, value: string) => {
    onChange(field, value);
  };

  return (
    <div className="space-y-2 pt-4">
      
      
      {/* Sender Type */}
      <div className="grid grid-cols-20 gap-2 items-center">
        <Label htmlFor="senderType" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Sender type
        </Label>
        <div className="col-span-6">
          <Select 
            value={employer.senderType} 
            onValueChange={(value) => handleSelectChange("senderType", value)}
          >
            <SelectTrigger id="senderType" className="text-sm">
              <SelectValue placeholder="Select sender type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Employer">Employer</SelectItem>
              <SelectItem value="Agent">Agent</SelectItem>
              <SelectItem value="Bureau">Bureau</SelectItem>
              <SelectItem value="Payroll Provider">Payroll Provider</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Sender ID */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="senderId" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Sender ID
        </Label>
        <div className="col-span-6">
          <Input
            id="senderId"
            name="senderId"
            value={employer.senderId || ""}
            onChange={handleInputChange}
            placeholder="As provided to you by HMRC"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Password */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="password" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Password
        </Label>
        <div className="col-span-6">
          <Input
            id="password"
            name="password"
            type="password"
            value={employer.password || ""}
            onChange={handleInputChange}
            placeholder="Password for HMRC submissions"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Contact Name */}
      <div className="grid grid-cols-20 gap-2 items-center mt-4">
        <Label htmlFor="contactTitleRTI" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Contact name
        </Label>
        <div className="col-span-2">
          <Select 
            value={employer.contactTitleRTI} 
            onValueChange={(value) => handleSelectChange("contactTitleRTI", value)}
          >
            <SelectTrigger id="contactTitleRTI" className="text-sm">
              <SelectValue placeholder="Title" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Mr">Mr</SelectItem>
              <SelectItem value="Mrs">Mrs</SelectItem>
              <SelectItem value="Miss">Miss</SelectItem>
              <SelectItem value="Ms">Ms</SelectItem>
              <SelectItem value="Dr">Dr</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="col-span-4">
          <Input
            id="contactFirstNameRTI"
            name="contactFirstNameRTI"
            value={employer.contactFirstNameRTI || ""}
            onChange={handleInputChange}
            placeholder="First Name"
            className="text-sm w-full"
          />
        </div>
        <div className="col-span-4">
          <Input
            id="contactLastNameRTI"
            name="contactLastNameRTI"
            value={employer.contactLastNameRTI || ""}
            onChange={handleInputChange}
            placeholder="Last Name"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Contact Email */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="contactEmailRTI" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Contact email address
        </Label>
        <div className="col-span-6">
          <Input
            id="contactEmailRTI"
            name="contactEmailRTI"
            value={employer.contactEmailRTI || ""}
            onChange={handleInputChange}
            placeholder="Email address for RTI communications"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Contact Phone */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="contactPhoneRTI" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Contact phone number
        </Label>
        <div className="col-span-6">
          <Input
            id="contactPhoneRTI"
            name="contactPhoneRTI"
            value={employer.contactPhoneRTI || ""}
            onChange={handleInputChange}
            placeholder="Phone number for RTI communications"
            className="text-sm w-full"
          />
        </div>
      </div>
      
      {/* Contact Fax */}
      <div className="grid grid-cols-20 gap-2 items-center mt-1">
        <Label htmlFor="contactFaxRTI" className="text-right font-medium col-span-4 justify-self-end mx-4">
          Contact fax number
        </Label>
        <div className="col-span-6">
          <Input
            id="contactFaxRTI"
            name="contactFaxRTI"
            value={employer.contactFaxRTI || ""}
            onChange={handleInputChange}
            placeholder="Fax number for RTI communications"
            className="text-sm w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default RTISubmissionsSection;
