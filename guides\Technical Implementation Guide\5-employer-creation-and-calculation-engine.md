# UK Payroll Application - Technical Guide Part 5: Employer Creation & Calculation Engine

## Employer Creation Page with TanStack Query

This page demonstrates the use of TanStack Query mutations for creating a new employer database.

```tsx
// src/app/employers/create/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useCreateEmployer } from '@/hooks/use-query/employers';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Schema for form validation
const createEmployerSchema = z.object({
  name: z.string().min(1, 'Employer name is required'),
  tradingName: z.string().optional(),
  taxReference: z.string().optional(),
  companyNumber: z.string().optional(),
});

type CreateEmployerForm = z.infer<typeof createEmployerSchema>;

export default function CreateEmployerPage() {
  const router = useRouter();
  const createEmployerMutation = useCreateEmployer();
  const [error, setError] = useState<string | null>(null);
  
  const form = useForm<CreateEmployerForm>({
    resolver: zodResolver(createEmployerSchema),
    defaultValues: {
      name: '',
      tradingName: '',
      taxReference: '',
      companyNumber: '',
    },
  });
  
  const onSubmit = async (data: CreateEmployerForm) => {
    try {
      setError(null);
      
      const result = await createEmployerMutation.mutateAsync({
        name: data.name,
        tradingName: data.tradingName,
        taxReference: data.taxReference,
        companyNumber: data.companyNumber,
      });
      
      // Navigate to the new employer
      router.push(`/employers/${result.id}`);
    } catch (err) {
      console.error('Failed to create employer:', err);
      setError(err instanceof Error ? err.message : 'Failed to create employer');
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Create New Employer</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Employer Details</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded border border-red-200">
              {error}
            </div>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employer Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="tradingName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Trading Name (optional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="taxReference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax Reference (optional)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="companyNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Number (optional)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="flex justify-end pt-4">
                <Button 
                  type="submit" 
                  disabled={createEmployerMutation.isPending}
                >
                  {createEmployerMutation.isPending ? (
                    <>
                      <span className="animate-spin mr-2">⌛</span>
                      Creating...
                    </>
                  ) : 'Create Employer'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
```

## Employer Card Component

A reusable component to display employer information.

```tsx
// src/components/dashboard/employer-card.tsx
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface EmployerCardProps {
  employer: {
    id: string;
    name: string;
    lastAccessed?: Date;
  };
  onClick: () => void;
}

export function EmployerCard({ employer, onClick }: EmployerCardProps) {
  // Format the last accessed date if available
  const formattedDate = employer.lastAccessed
    ? new Date(employer.lastAccessed).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      })
    : 'Never';

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">{employer.name}</CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm text-slate-500">ID: {employer.id}</p>
      </CardContent>
      <CardFooter>
        <p className="text-xs text-slate-400">Last accessed: {formattedDate}</p>
      </CardFooter>
    </Card>
  );
}
```

## Core Calculation Engine with Web Workers

### PAYE Tax Calculation Module

```typescript
// src/core/calculations/paye/paye-calculator.ts
import { TaxYear } from '../tax-years/types';

interface PayeParams {
  grossPay: number;
  taxCode: string;
  taxPeriod: number;
  ytdTaxablePay: number;
  isScottishTaxpayer: boolean;
}

interface PayeResult {
  taxableIncome: number;
  taxDue: number;
  calculations: {
    freePay: number;
    cumulativeTaxableIncome: number;
    rateApplied: number;
    bandUtilization: Record<string, number>;
  };
}

export function calculatePAYE(
  params: PayeParams,
  taxYear: TaxYear
): PayeResult {
  const { grossPay, taxCode, taxPeriod, ytdTaxablePay, isScottishTaxpayer } = params;
  
  // Extract numeric part and suffix/prefix from tax code
  let numericPart = 0;
  let prefix = '';
  let suffix = '';
  
  if (/^[KSD]/.test(taxCode)) {
    prefix = taxCode.charAt(0);
    numericPart = parseInt(taxCode.substring(1), 10) || 0;
  } else {
    const match = taxCode.match(/(\d+)([A-Z]+)?/);
    if (match) {
      numericPart = parseInt(match[1], 10);
      suffix = match[2] || 'L';
    }
  }
  
  // Calculate Free Pay or Additional Pay
  let freePay = 0;
  let additionalPay = 0;
  
  if (prefix === 'K') {
    // K code: negative allowance (additional pay)
    additionalPay = (numericPart * 10) / 52 * taxPeriod;
  } else {
    // Normal code: positive allowance (free pay)
    freePay = (numericPart * 10 + 9) / 52 * taxPeriod;
  }
  
  // Calculate taxable income
  let currentTaxablePay = grossPay;
  
  if (prefix === 'K') {
    currentTaxablePay += additionalPay;
  } else {
    currentTaxablePay -= freePay;
    if (currentTaxablePay < 0) currentTaxablePay = 0;
  }
  
  // Calculate cumulative taxable income
  const cumulativeTaxableIncome = ytdTaxablePay + currentTaxablePay;
  
  // Determine tax brackets and rates to apply
  const brackets = isScottishTaxpayer ? taxYear.scottishBrackets : taxYear.brackets;
  const rates = isScottishTaxpayer ? taxYear.scottishRates : taxYear.rates;
  
  // Calculate tax due
  let taxDue = 0;
  let remainingIncome = cumulativeTaxableIncome;
  let currentBracket = 0;
  const bandUtilization: Record<string, number> = {};
  
  // Calculate tax based on bracket utilization
  while (remainingIncome > 0 && currentBracket < brackets.length) {
    const bracket = brackets[currentBracket];
    const rate = rates[currentBracket];
    const bracketSize = (bracket.upperLimit - bracket.lowerLimit) / 52 * taxPeriod;
    
    if (remainingIncome <= bracketSize) {
      taxDue += remainingIncome * rate;
      bandUtilization[`bracket_${currentBracket}`] = remainingIncome;
      remainingIncome = 0;
    } else {
      taxDue += bracketSize * rate;
      bandUtilization[`bracket_${currentBracket}`] = bracketSize;
      remainingIncome -= bracketSize;
      currentBracket++;
    }
  }
  
  // Round tax due to nearest penny
  taxDue = Math.round(taxDue * 100) / 100;
  
  return {
    taxableIncome: currentTaxablePay,
    taxDue,
    calculations: {
      freePay,
      cumulativeTaxableIncome,
      rateApplied: rates[currentBracket - 1] || 0,
      bandUtilization
    }
  };
}
```

### Web Worker Implementation

```typescript
// src/workers/calculation-worker.ts

import { TaxYear } from '@/core/calculations/tax-years/types';
import { TAX_YEAR_2025_2026 } from '@/core/calculations/tax-years/2025-2026';
import { calculatePAYE } from '@/core/calculations/paye/paye-calculator';
import { calculateNI } from '@/core/calculations/ni/ni-calculator';
import { calculateStudentLoan } from '@/core/calculations/student-loan/student-loan-calculator';

// Handle messages from the main thread
self.onmessage = function(e) {
  const { type, data } = e.data;
  
  try {
    switch (type) {
      case 'CALCULATE_PAYSLIP':
        const result = calculatePayslip(data);
        self.postMessage({ type: 'CALCULATION_RESULT', data: result });
        break;
        
      case 'CALCULATE_PAYRUN':
        const payrunResults = calculatePayrun(data);
        self.postMessage({ type: 'PAYRUN_RESULT', data: payrunResults });
        break;
        
      default:
        self.postMessage({ 
          type: 'ERROR', 
          error: `Unknown calculation type: ${type}` 
        });
    }
  } catch (error) {
    self.postMessage({ 
      type: 'ERROR', 
      error: error.message || 'Calculation failed' 
    });
  }
};

// Calculate a single payslip
function calculatePayslip(data) {
  const { 
    employee, 
    grossPay, 
    taxYear, 
    taxPeriod, 
    payElements, 
    ytdValues 
  } = data;
  
  // Get tax year configuration
  const taxYearConfig = getTaxYearConfig(taxYear);
  
  // Calculate tax
  const taxResult = calculatePAYE(
    {
      grossPay,
      taxCode: employee.taxCode,
      taxPeriod,
      ytdTaxablePay: ytdValues.taxablePay || 0,
      isScottishTaxpayer: employee.isScottishTaxpayer
    },
    taxYearConfig
  );
  
  // Calculate National Insurance
  const niResult = calculateNI(
    {
      grossPay,
      niCategory: employee.niCategory,
      taxPeriod,
      ytdNiablePay: ytdValues.niablePay || 0
    },
    taxYearConfig
  );
  
  // Calculate student loan (if applicable)
  const studentLoanResult = employee.studentLoanPlan 
    ? calculateStudentLoan(
        {
          grossPay, 
          studentLoanPlan: employee.studentLoanPlan, 
          taxPeriod
        },
        taxYearConfig
      )
    : { deduction: 0, calculations: {} };
  
  // Calculate net pay
  const totalDeductions = taxResult.taxDue + 
                          niResult.contribution + 
                          studentLoanResult.deduction;
  
  const netPay = grossPay - totalDeductions;
  
  // Return complete payslip calculation
  return {
    grossPay,
    taxableIncome: taxResult.taxableIncome,
    taxDue: taxResult.taxDue,
    niContribution: niResult.contribution,
    studentLoanDeduction: studentLoanResult.deduction,
    totalDeductions,
    netPay,
    calculations: {
      tax: taxResult.calculations,
      ni: niResult.calculations,
      studentLoan: studentLoanResult.calculations
    }
  };
}

// Calculate an entire pay run
function calculatePayrun(data) {
  const { employees, payRunDetails } = data;
  const results = [];
  
  for (const employee of employees) {
    // Calculate each employee's payslip
    const payslipResult = calculatePayslip({
      employee,
      grossPay: employee.grossPay,
      taxYear: payRunDetails.taxYear,
      taxPeriod: payRunDetails.taxPeriod,
      payElements: employee.payElements,
      ytdValues: employee.ytdValues
    });
    
    results.push({
      employeeId: employee.id,
      calculation: payslipResult
    });
  }
  
  return {
    payRunId: payRunDetails.id,
    results,
    summary: {
      totalGrossPay: results.reduce((sum, r) => sum + r.calculation.grossPay, 0),
      totalTax: results.reduce((sum, r) => sum + r.calculation.taxDue, 0),
      totalNI: results.reduce((sum, r) => sum + r.calculation.niContribution, 0),
      totalDeductions: results.reduce((sum, r) => sum + r.calculation.totalDeductions, 0),
      totalNetPay: results.reduce((sum, r) => sum + r.calculation.netPay, 0)
    }
  };
}

// Utility function to get tax year configuration
function getTaxYearConfig(taxYear) {
  const taxYearConfigs = {
    '2025-2026': TAX_YEAR_2025_2026,
    // Add more tax years as needed
  };
  
  const config = taxYearConfigs[taxYear];
  if (!config) {
    throw new Error(`Tax year configuration not found for: ${taxYear}`);
  }
  
  return config;
}
```

### Web Worker Hook

```typescript
// src/hooks/use-calculation.ts
import { useState, useEffect } from 'react';

export function useCalculation() {
  const [worker, setWorker] = useState<Worker | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  
  // Initialize worker
  useEffect(() => {
    // Only create worker in browser environment
    if (typeof window !== 'undefined') {
      const calculationWorker = new Worker(
        new URL('@/workers/calculation-worker.ts', import.meta.url)
      );
      
      setWorker(calculationWorker);
      
      // Clean up worker on unmount
      return () => {
        calculationWorker.terminate();
      };
    }
  }, []);
  
  // Function to calculate a payslip using the worker
  const calculatePayslip = async (data) => {
    if (!worker) {
      throw new Error('Calculation worker not initialized');
    }
    
    setIsCalculating(true);
    
    try {
      // Create a promise that resolves when the worker returns a result
      const result = await new Promise((resolve, reject) => {
        // Set up listener for worker messages
        const handleMessage = (e) => {
          if (e.data.type === 'CALCULATION_RESULT') {
            worker.removeEventListener('message', handleMessage);
            resolve(e.data.data);
          } else if (e.data.type === 'ERROR') {
            worker.removeEventListener('message', handleMessage);
            reject(new Error(e.data.error));
          }
        };
        
        worker.addEventListener('message', handleMessage);
        
        // Send calculation request to worker
        worker.postMessage({
          type: 'CALCULATE_PAYSLIP',
          data
        });
      });
      
      return result;
    } finally {
      setIsCalculating(false);
    }
  };
  
  // Function to calculate a complete pay run
  const calculatePayrun = async (data) => {
    if (!worker) {
      throw new Error('Calculation worker not initialized');
    }
    
    setIsCalculating(true);
    
    try {
      // Similar pattern to calculatePayslip but for pay runs
      const result = await new Promise((resolve, reject) => {
        const handleMessage = (e) => {
          if (e.data.type === 'PAYRUN_RESULT') {
            worker.removeEventListener('message', handleMessage);
            resolve(e.data.data);
          } else if (e.data.type === 'ERROR') {
            worker.removeEventListener('message', handleMessage);
            reject(new Error(e.data.error));
          }
        };
        
        worker.addEventListener('message', handleMessage);
        
        worker.postMessage({
          type: 'CALCULATE_PAYRUN',
          data
        });
      });
      
      return result;
    } finally {
      setIsCalculating(false);
    }
  };
  
  return {
    calculatePayslip,
    calculatePayrun,
    isCalculating
  };
}
```
