# Test Employers Generator

This script generates a set of 200 test employer databases for testing purposes.

## Usage

Run the script using npm:

```bash
npm run generate:test-employers
```

## What It Does

1. Creates a `test-employers` directory in the project root
2. Generates 200 employer databases with:
   - Unique UUIDs
   - Unique company names
   - Random PAYE references
   - Random accounts references
   - Basic address information
3. Saves a summary file (`employers-summary.json`) with details of all created employers

## Testing Rename Detection

To test the rename detection functionality:

1. Run the script to generate the test employers
2. Add some of the generated employers to your application using "Add Existing"
3. Close the application
4. Rename some of the employer files in the `test-employers` directory
5. Reopen the application and click the refresh button
6. The application should detect the renamed files and update the references

## Testing Large Lists

The script generates 200 employers by default, which is below the 500 threshold for full rename detection. This allows you to test the performance with a significant number of employers while still having the rename detection functionality active.

If you want to test with more employers, you can modify the `COUNT` constant in the script.

## Cleanup

To remove all test employers, simply delete the `test-employers` directory.
