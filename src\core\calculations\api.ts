/**
 * UK Payroll Calculations API
 * 
 * This module provides a client-side API for the payroll calculation engine.
 * It uses Web Workers for intensive calculations when available, with fallbacks
 * for environments where Web Workers are not supported.
 */

import { getPayrollWorkerManager } from './workers/worker-manager';
import { calculatePaye } from '@/core/calculations/paye/calculator';
import { calculateNationalInsurance } from '@/core/calculations/ni/calculator';
import { PayPeriodType } from '@/core/calculations/paye/types';
import { PayeCalculationInput, PayeCalculationResult } from '@/core/calculations/paye/types';
import { NiCalculationInput, NiCalculationResult } from '@/core/calculations/ni/types';
import { getCurrentTaxYearConfig, getTaxYearConfig } from '@/core/calculations/tax-years';

/**
 * Payroll calculation API class
 */
export class PayrollCalculationApi {
  private useWorkers: boolean;
  
  /**
   * Create a new payroll calculation API
   * @param useWorkers - Whether to use Web Workers for calculations (default: true)
   */
  constructor(useWorkers = true) {
    // Check if Web Workers are supported
    this.useWorkers = useWorkers && typeof Worker !== 'undefined';
  }
  
  /**
   * Calculate PAYE tax
   * @param input - The PAYE calculation input
   * @returns A promise that resolves with the PAYE calculation result
   */
  public async calculatePaye(input: PayeCalculationInput): Promise<PayeCalculationResult> {
    if (this.useWorkers) {
      try {
        // Use the worker for the calculation
        const workerManager = getPayrollWorkerManager();
        return await workerManager.sendRequest<PayeCalculationResult>('paye', input);
      } catch (error) {
        console.warn('Worker calculation failed, falling back to main thread:', error);
        // Fall back to main thread calculation
        return calculatePaye(input);
      }
    } else {
      // Use main thread calculation
      return calculatePaye(input);
    }
  }
  
  /**
   * Calculate National Insurance
   * @param input - The NI calculation input
   * @returns A promise that resolves with the NI calculation result
   */
  public async calculateNationalInsurance(input: NiCalculationInput): Promise<NiCalculationResult> {
    if (this.useWorkers) {
      try {
        // Use the worker for the calculation
        const workerManager = getPayrollWorkerManager();
        return await workerManager.sendRequest<NiCalculationResult>('national_insurance', input);
      } catch (error) {
        console.warn('Worker calculation failed, falling back to main thread:', error);
        // Fall back to main thread calculation
        return calculateNationalInsurance(input);
      }
    } else {
      // Use main thread calculation
      return calculateNationalInsurance(input);
    }
  }
  
  /**
   * Calculate a complete payslip
   * @param input - The payslip calculation input
   * @returns A promise that resolves with the payslip calculation result
   */
  public async calculatePayslip(input: any): Promise<any> {
    if (this.useWorkers) {
      try {
        // Use the worker for the calculation
        const workerManager = getPayrollWorkerManager();
        return await workerManager.sendRequest<any>('payslip', input);
      } catch (error) {
        console.warn('Worker calculation failed, falling back to main thread:', error);
        // Fall back to main thread calculation
        // This is a placeholder - in a real implementation, we would have a main thread version
        throw new Error('Payslip calculation not implemented on main thread');
      }
    } else {
      // This is a placeholder - in a real implementation, we would have a main thread version
      throw new Error('Payslip calculation not implemented on main thread');
    }
  }
  
  /**
   * Calculate a complete pay run
   * @param input - The pay run calculation input
   * @returns A promise that resolves with the pay run calculation result
   */
  public async calculatePayRun(input: any): Promise<any> {
    if (this.useWorkers) {
      try {
        // Use the worker for the calculation
        const workerManager = getPayrollWorkerManager();
        return await workerManager.sendRequest<any>('pay_run', input);
      } catch (error) {
        console.warn('Worker calculation failed, falling back to main thread:', error);
        // Fall back to main thread calculation
        // This is a placeholder - in a real implementation, we would have a main thread version
        throw new Error('Pay run calculation not implemented on main thread');
      }
    } else {
      // This is a placeholder - in a real implementation, we would have a main thread version
      throw new Error('Pay run calculation not implemented on main thread');
    }
  }
  
  /**
   * Get the current tax year configuration
   * @returns The current tax year configuration
   */
  public getCurrentTaxYearConfig() {
    return getCurrentTaxYearConfig();
  }
  
  /**
   * Get the tax year configuration for a specific tax year
   * @param taxYear - The tax year identifier (e.g., "2023-2024")
   * @returns The tax year configuration or undefined if not found
   */
  public getTaxYearConfig(taxYear: string) {
    return getTaxYearConfig(taxYear);
  }
}

// Create a singleton instance of the API
let payrollApi: PayrollCalculationApi | null = null;

/**
 * Get the payroll calculation API
 * @param useWorkers - Whether to use Web Workers for calculations (default: true)
 * @returns The payroll calculation API
 */
export function getPayrollCalculationApi(useWorkers = true): PayrollCalculationApi {
  if (!payrollApi) {
    payrollApi = new PayrollCalculationApi(useWorkers);
  }
  
  return payrollApi;
}
