import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

interface ResultEmployerModalDialogProps {
  open: boolean;
  type: "success" | "error";
  title?: string;
  message: string;
  onConfirm: () => void;
}

export function ResultEmployerModalDialog({
  open,
  type,
  title,
  message,
  onConfirm,
}: ResultEmployerModalDialogProps) {
  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {title || (type === "success" ? "Employer Created" : "Error Creating Employer")}
          </AlertDialogTitle>
          <AlertDialogDescription className={type === "error" ? "text-red-600" : "text-green-700"}>
            {message}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onConfirm} autoFocus>
            OK
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
