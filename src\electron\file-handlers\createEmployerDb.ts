import fs from "fs";
import path from "path";
import { EMPLOYER_DB_EXTENSION } from "../../constants/file";
import { createClient } from "@libsql/client";
import { v4 as uuidv4 } from "uuid";
import { employerSchema, type Employer } from "../../lib/schemas/employer";
import { runMigrations } from "../utils/runMigrations";
import { getMasterDb } from "../../drizzle/adapters/master";
import { employers } from "../../drizzle/schema/master";

export interface CreateEmployerDbParams {
  filePath: string;
  employerData: Partial<Employer>; // Allow partial data initially
}

export interface CreateEmployerDbResult {
  success: boolean;
  error?: string;
  employerId?: string;
}

export async function createEmployerDb({
  filePath,
  employerData,
}: CreateEmployerDbParams): Promise<CreateEmployerDbResult> {
  try {
    // Always resolve to absolute path (user can pick any location)
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.resolve(process.cwd(), filePath);
    console.log(`[createEmployerDb] Using employer DB path:`, absolutePath);

    // 1. Create empty SQLite file (runMigrations will initialize it)
    fs.writeFileSync(absolutePath, "");

    // 2. Run migrations to create the schema
    await runMigrations(absolutePath);
    console.log(`Migrations run successfully for ${absolutePath}`);

    // 4. Minimal validation: ensure name is present, generate id if needed
    const employerId = employerData.id || uuidv4();
    const employerName = employerData.name;
    if (!employerName) {
      fs.unlinkSync(filePath);
      return {
        success: false,
        error: "Employer name is required.",
      };
    }

    // 5. Insert minimal employer data using libsql
    const client = createClient({
      url: `file:${absolutePath}`,
    });

    try {
      await client.execute({
        sql: "INSERT INTO employer (id, name) VALUES (?, ?)",
        args: [employerId, employerName],
      });
      console.log(`Employer data inserted into ${filePath}`);
    } finally {
      await client.close();
    }

    // 6. Register employer in the master DB
    try {
      const masterDb = getMasterDb();

      // Extract file name from path (without extension) to use as display name
      const fileName = path.basename(filePath, EMPLOYER_DB_EXTENSION);

      await masterDb.insert(employers).values({
        id: employerId,
        display_name: fileName, // Use file name instead of employer name
        file_path: absolutePath,
        status: "open",
        created_at: new Date(),
        updated_at: new Date(),
        last_opened_at: new Date(),
      });
      console.log(`Employer ${employerId} registered in master DB`);
    } catch (masterDbError) {
      console.error("Error registering employer in master DB:", masterDbError);
      // Don't fail the whole operation if master DB registration fails
      // We'll just log the error and continue
    }

    return { success: true, employerId: employerId };
  } catch (error: any) {
    console.error("Error creating employer database:", error);
    // Attempt to clean up the file if it exists and an error occurred
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (cleanupError) {
        console.error("Error cleaning up file:", cleanupError);
      }
    }
    return { success: false, error: error?.message || String(error) };
  }
}
