import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";

export const employers = sqliteTable("employers", {
  id: text("id").primaryKey(),
  display_name: text("display_name").notNull(), // Keep as display_name initially
  file_path: text("file_path").notNull(),
  status: text("status").notNull().default("open"),
  created_at: integer("created_at", { mode: "timestamp" }).notNull(),
  updated_at: integer("updated_at", { mode: "timestamp" }).notNull(),
  last_opened_at: integer("last_opened_at", { mode: "timestamp" }),
  paye_ref: text("paye_ref"),
  accounts_ref: text("accounts_ref"),
  next_pay_date: text("next_pay_date"),
  employees: integer("employees"),
  tax_code_notices: integer("tax_code_notices"),
  rti_submissions: integer("rti_submissions"),
  notifications: integer("notifications"),
});

export const appSettings = sqliteTable("app_settings", {
  id: text("id").primaryKey().default("singleton"),
  defaultEmployerDirectory: text("default_employer_directory").notNull(),
});
