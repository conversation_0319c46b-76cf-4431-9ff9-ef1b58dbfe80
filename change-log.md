# UK Payroll Software Change Log

## 2025-04-22

### Fixed
- **Payroll Overview Table Scrolling**: Resolved issue where payroll overview table did not scroll horizontally/vertically due to parent container change from route to state-based navigation. Added `overflow-auto` to the main dashboard container ([src/app/page.tsx]) to restore correct scroll context.
- **Toolbar Visibility**: Ensured ActionToolbar is always visible across all sections, including dashboard, using state-based navigation.

### Added
- **payPeriod.ts Schema**: Added payPeriod.ts schema for pay_periods table in employer DB (`src/drizzle/schema/employer/payPeriod.ts`).
- **TanStack Query Hooks and Payroll Wizard Modal Scaffold**: Added TanStack Query hooks for pay periods and employees. Added PayrollWizardModal and PayrollSection scaffolds for pay frequency detection and wizard launch.

### Next
- Begin connecting all employer section UIs to the employer database (removing mock data, using Drizzle ORM with better-sqlite3).


## 2025-04-04

### Added
- **Enhanced Tab Navigation in Batch Editor**: Implemented dual-mode tab navigation for improved data entry
  - Added toggle button to switch between column-wise and row-wise navigation
  - Column-wise mode (default): Tab moves horizontally across columns, then to the next row
  - Row-wise mode: Tab moves vertically down rows, then to the top of the next column
  - Properly handles disabled cells (for closed payslips) by skipping them
  - Automatically selects cell content when tabbing for easy replacement
  - Persists navigation mode preference using localStorage
  - Provides visual indication of current navigation mode

## 2025-04-03

### Added
- **Persistent UI State Management**: Implemented localStorage-based state persistence for the payroll page
  - Persists active view (overview, payslip, batch) between navigation events
  - Maintains selected pay period and employee across page navigations
  - Preserves filter settings (all, open, closed) in both overview and batch edit views
  - Remembers search terms in the overview screen
  - Maintains column selections in the batch edit view
  - Preserves sorting preferences across all views
  - Follows desktop application UX best practices by maintaining user's context
