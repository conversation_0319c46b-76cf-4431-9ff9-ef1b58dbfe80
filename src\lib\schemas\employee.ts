import { z } from "zod";

// Define validation schema for employee data using Zod
export const employeeSchema = z.object({
  id: z.string(), // ID is required, aligns with DB primary key
  // Personal Information
  title: z.string().min(1, "Title is required"),
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  gender: z.enum(["Male", "Female"]).default("Male"),
  address1: z.string().optional(),
  address2: z.string().optional(),
  address3: z.string().optional(),
  address4: z.string().optional(),
  postcode: z.string().optional(),
  country: z.string().default("England"),
  email: z.string().email().optional(),
  emailType: z.string().default("Personal"),
  phone: z.string().optional(),
  phoneType: z.string().default("Mobile"),

  // Additional contact entries
  additionalEmails: z
    .array(
      z.object({
        type: z.string().default("Personal"),
        value: z.string().email().optional(),
      }),
    )
    .optional()
    .default([]),

  additionalPhones: z
    .array(
      z.object({
        type: z.string().default("Mobile"),
        value: z.string().optional(),
      }),
    )
    .optional()
    .default([]),

  // Employment Information
  worksNumber: z.string().optional(),
  startDate: z.string().min(1, "Start date is required"),
  departments: z.array(z.string()).optional(),
  leaveYearStarts: z.string().optional(),
  annualLeaveCalculationMethod: z.string().optional(),
  annualLeaveEntitlement: z.number().optional(),
  annualLeaveCarryOver: z.boolean().default(false),
  annualLeaveCarryOverValue: z.number().default(0),
  annualLeaveCarryOverUnit: z.enum(["days", "hours"]).default("days"),
  annualLeaveAdjustment: z.boolean().default(false),
  annualLeaveAdjustmentValue: z.number().default(0),
  annualLeaveAdjustmentUnit: z.enum(["days", "hours"]).default("days"),
  workingDays: z.object({
    monday: z.boolean().default(true),
    tuesday: z.boolean().default(true),
    wednesday: z.boolean().default(true),
    thursday: z.boolean().default(true),
    friday: z.boolean().default(true),
    saturday: z.boolean().default(false),
    sunday: z.boolean().default(false),
  }),
  minimumWageProfile: z.string().optional(),
  typicalHoursWorked: z.number().optional(),

  // Starter/Leaver Information
  overseasEmployer: z.boolean().default(false),
  starterDeclaration: z.string().optional(),
  previousEmploymentPay: z.number().optional(),
  previousEmploymentTax: z.number().optional(),
  isProtectedUnderTUPE: z.boolean().default(false),
  leaveDate: z.string().optional(),

  // Payment Information
  paymentFrequency: z.enum([
    "2-Weekly",
    "4-Weekly",
    "Monthly",
    "Weekly",
    "Yearly",
    "Quarterly",
  ]),
  payCalculationMethod: z.string().optional(),
  periodPayRate: z.number().optional(), // Rate based on the selected payment period
  annualSalary: z.number().optional(),
  hourlyRates: z
    .array(
      z.object({
        name: z.string(),
        rate: z.number(),
        endDate: z.string().optional(),
      }),
    )
    .optional(),
  dailyRates: z
    .array(
      z.object({
        name: z.string(),
        rate: z.number(),
        endDate: z.string().optional(),
      }),
    )
    .optional(),
  paymentMethod: z.enum(["Bank transfer", "Cheque", "Cash"]).optional(),
  paymentDetails: z
    .object({
      bankName: z.string().optional(),
      sortCode: z.string().optional(),
      accountNumber: z.string().optional(),
      accountName: z.string().optional(),
      reference: z.string().optional(),
    })
    .optional(),

  // Tax, NI, RTI Information
  taxCode: z.string().optional(),
  week1Month1: z.boolean().default(false),
  niTable: z.string().optional(),
  employerNIContributions: z.string().optional(),
  studentLoan: z.string().optional(),
  studentLoanStartDate: z.string().optional(),
  studentLoanEndDate: z.string().optional(),
  postgraduateLoan: z.string().optional(),
  pgLoanStartDate: z.string().optional(),
  pgLoanEndDate: z.string().optional(),
  niNumber: z.string().optional(),
  isDirector: z.boolean().default(false),
  directorStartDate: z.string().optional(),
  directorEndDate: z.string().optional(),
  directorNICalcMethod: z.enum(["standard", "alternate"]).default("standard"),
  isOffPayrollWorker: z.boolean().default(false),
  payrollId: z.string().optional(),
  changeOfPayrollId: z.string().optional(),
  zeroPayHandling: z.string().optional(),
  contractedHours: z.string().optional(),
  irregularPaymentPattern: z.boolean().default(false),
  nonIndividual: z.boolean().default(false),
  trivialCommutation: z.boolean().default(false),
  flexibleDrawdown: z.boolean().default(false),

  // HR, Other Information
  nationality: z.string().optional(),
  passportNumber: z.string().optional(),
  maritalStatus: z.string().optional(),
  jobTitle: z.string().optional(),
  pdfPassword: z.string().optional(),
  emergencyContacts: z
    .array(
      z.object({
        name: z.string(),
        relationship: z.string(),
        phone: z.string(),
      }),
    )
    .optional(),
  enableSelfService: z.boolean().default(false),
  isConfidential: z.boolean().default(false),
  startingSalary: z.number().optional(),
  nextReviewDate: z.string().optional(),
  medical: z.string().optional(),
  notes: z.string().optional(),
});

// Extract the TypeScript type from the Zod schema
export type Employee = z.infer<typeof employeeSchema>;

// Default values for new employee
export const defaultEmployee: Employee = {
  id: "",
  title: "Mr",
  firstName: "",
  middleName: "",
  lastName: "",
  dateOfBirth: "",
  gender: "Male",
  address1: "",
  address2: "",
  address3: "",
  address4: "",
  postcode: "",
  country: "England",
  email: "",
  emailType: "Personal",
  phone: "",
  phoneType: "Mobile",
  additionalEmails: [],
  additionalPhones: [],

  worksNumber: "",
  startDate: "",
  departments: [],
  leaveYearStarts: "",
  annualLeaveCalculationMethod: "",
  annualLeaveEntitlement: 0,
  annualLeaveCarryOver: false,
  annualLeaveCarryOverValue: 0,
  annualLeaveCarryOverUnit: "days",
  annualLeaveAdjustment: false,
  annualLeaveAdjustmentValue: 0,
  annualLeaveAdjustmentUnit: "days",
  workingDays: {
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: false,
    sunday: false,
  },
  minimumWageProfile: "",
  typicalHoursWorked: 0,

  overseasEmployer: false,
  starterDeclaration: "A",
  previousEmploymentPay: undefined,
  previousEmploymentTax: undefined,
  isProtectedUnderTUPE: false,
  leaveDate: "",

  paymentFrequency: "Monthly",
  payCalculationMethod: "",
  periodPayRate: undefined,
  annualSalary: undefined,
  hourlyRates: [{ name: "Standard hourly rate", rate: 0 }],
  dailyRates: [{ name: "Standard daily rate", rate: 0 }],
  paymentMethod: "Bank transfer",
  paymentDetails: {
    bankName: "",
    sortCode: "",
    accountNumber: "",
    accountName: "",
    reference: "",
  },

  taxCode: "1257L",
  week1Month1: false,
  niTable: "A",
  employerNIContributions: "",
  studentLoan: "",
  studentLoanStartDate: "",
  studentLoanEndDate: "",
  postgraduateLoan: "",
  pgLoanStartDate: "",
  pgLoanEndDate: "",
  niNumber: "",
  isDirector: false,
  directorStartDate: "",
  directorEndDate: "",
  directorNICalcMethod: "standard",
  isOffPayrollWorker: false,
  payrollId: "",
  changeOfPayrollId: "",
  zeroPayHandling: "",
  contractedHours: "",
  irregularPaymentPattern: false,
  nonIndividual: false,
  trivialCommutation: false,
  flexibleDrawdown: false,

  nationality: "",
  passportNumber: "",
  maritalStatus: "",
  jobTitle: "",
  pdfPassword: "",
  emergencyContacts: [],
  enableSelfService: false,
  isConfidential: false,
  startingSalary: 0,
  nextReviewDate: "",
  medical: "",
  notes: "",
};
