import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";
import { migrate } from "drizzle-orm/libsql/migrator";
import path from "path";

/**
 * Runs all employer migrations against a SQLite file.
 */
export async function runMigrations(filePath: string): Promise<void> {
  const pathResolved = path.isAbsolute(filePath) ? filePath : path.resolve(process.cwd(), filePath);
  console.log(`[runMigrations] Using DB path:`, pathResolved);
  const client = createClient({
    url: `file:${pathResolved}`,
  });

  const db = drizzle(client);

  try {
    const migrationsFolder = path.resolve(
      __dirname,
      "../../../src/drizzle/migrations/employer",
    );
    await migrate(db, { migrationsFolder });
  } finally {
    await client.close();
  }
}
