/**
 * Types for UK tax year configuration
 */

/**
 * Tax Band definition
 */
export interface TaxBand {
  /**
   * Unique identifier for the band
   */
  id: string;
  
  /**
   * Display name of the tax band
   */
  name: string;
  
  /**
   * Tax rate as a decimal (e.g., 0.20 for 20%)
   */
  rate: number;
  
  /**
   * Annual income threshold where this band starts
   */
  threshold: number;
}

/**
 * National Insurance Category
 */
export interface NiCategory {
  /**
   * Category letter (e.g., "A", "B", "H")
   */
  category: string;
  
  /**
   * Description of this NI category
   */
  description: string;
  
  /**
   * Primary threshold (employee contribution)
   */
  primaryThreshold: number;
  
  /**
   * Secondary threshold (employer contribution)
   */
  secondaryThreshold: number;
  
  /**
   * Upper earnings limit
   */
  upperEarningsLimit: number;
  
  /**
   * Rate below upper earnings limit (as decimal)
   */
  primaryRate: number;
  
  /**
   * Rate above upper earnings limit (as decimal)
   */
  upperRate: number;
  
  /**
   * Employer contribution rate (as decimal)
   */
  employerRate: number;
}

/**
 * PAYE configuration for a tax year
 */
export interface PayeConfig {
  /**
   * Standard personal allowance
   */
  personalAllowance: number;
  
  /**
   * Threshold where personal allowance starts to reduce
   */
  personalAllowanceReductionThreshold: number;
  
  /**
   * Maximum regulatory limit for tax deduction (as decimal)
   */
  maxRegulatoryLimit: number;
  
  /**
   * G pointer - index to the band containing the basic rate (BR)
   * As per PAYErout Appendix A/B/C
   */
  G: number;
  
  /**
   * G1 pointer - index to the band containing the higher rate (D0)
   * As per PAYErout Appendix A/B/C
   */
  G1: number;
  
  /**
   * G2 pointer - index to the band containing the additional rate (D1)
   * As per PAYErout Appendix A/B/C
   */
  G2: number;
  
  /**
   * Standard UK tax rates
   */
  standardRates: TaxBand[];
  
  /**
   * Scottish tax rates
   */
  scottishRates: TaxBand[];
  
  /**
   * Welsh tax rates
   */
  welshRates: TaxBand[];
}

/**
 * Student loan configuration
 */
export interface StudentLoanConfig {
  /**
   * Plan 1 threshold
   */
  plan1Threshold: number;
  
  /**
   * Plan 2 threshold
   */
  plan2Threshold: number;
  
  /**
   * Plan 4 threshold
   */
  plan4Threshold: number;
  
  /**
   * Plan 5 threshold
   */
  plan5Threshold: number;
  
  /**
   * Postgraduate loan threshold
   */
  postgraduateThreshold: number;
  
  /**
   * Repayment rate as decimal
   */
  repaymentRate: number;
  
  /**
   * Postgraduate loan repayment rate as decimal
   */
  postgraduateRepaymentRate: number;
}

/**
 * Statutory payment configuration
 */
export interface StatutoryPaymentConfig {
  /**
   * Weekly rate
   */
  weeklyRate: number;
  
  /**
   * Lower earnings limit
   */
  lowerEarningsLimit: number;
  
  /**
   * Higher rate percentage (as decimal)
   */
  higherRatePercentage?: number;
  
  /**
   * Number of higher rate weeks
   */
  higherRateWeeks?: number;
  
  /**
   * Maximum weeks payable
   */
  maxWeeksPayable?: number;
  
  /**
   * Waiting days
   */
  waitingDays?: number;
  
  /**
   * Standard weekly rate (used in some payment types)
   */
  standardWeeklyRate?: number;
  
  /**
   * Standard rate weeks (used in some payment types)
   */
  standardRateWeeks?: number;
  
  /**
   * Maximum weeks (alias for maxWeeksPayable for backward compatibility)
   */
  maxWeeks?: number;
}

/**
 * Statutory payments configuration
 */
export interface StatutoryConfig {
  /**
   * Statutory Sick Pay configuration
   */
  ssp: StatutoryPaymentConfig;
  
  /**
   * Statutory Maternity Pay configuration
   */
  smp: StatutoryPaymentConfig;
  
  /**
   * Statutory Paternity Pay configuration
   */
  spp: StatutoryPaymentConfig;
  
  /**
   * Statutory Adoption Pay configuration
   */
  sap: StatutoryPaymentConfig;
  
  /**
   * Statutory Parental Bereavement Pay configuration
   */
  spbp: StatutoryPaymentConfig;
  
  /**
   * Shared Parental Pay configuration
   */
  shpp: StatutoryPaymentConfig;
}

/**
 * National Insurance configuration for a tax year
 */
export interface NationalInsurance {
  /**
   * Lower Earnings Limit (LEL)
   */
  lowerEarningsLimit: number;

  /**
   * Freeports Upper Secondary Threshold (FUST)
   */
  freeportsUpperSecondaryThreshold: number;

  /**
   * Investment Zones Upper Secondary Threshold (IZUST)
   */
  investmentZonesUpperSecondaryThreshold: number;

  /**
   * Termination Award Threshold (for Class 1A NICs)
   * NICs due on amounts exceeding this threshold
   */
  terminationAwardThreshold: number;

  /**
   * Sporting Testimonial Threshold (for Class 1A NICs)
   * NICs due on amounts exceeding this threshold
   */
  sportingTestimonialThreshold: number;

  /**
   * Class 1A NICs rate for termination awards and sporting testimonials
   * Expressed as a decimal (e.g., 0.138 for 13.8%)
   */
  class1ARate: number;

  /**
   * NI category configurations
   */
  categories: NiCategory[];
}

/**
 * Pension configuration for a tax year
 */
export interface PensionConfig {
  /**
   * Lower earnings threshold for auto-enrollment
   */
  lowerEarningsThreshold: number;
  
  /**
   * Upper earnings threshold for qualifying earnings
   */
  upperEarningsThreshold: number;
  
  /**
   * Earnings trigger for auto-enrollment
   */
  earningsTrigger: number;
  
  /**
   * Minimum employer contribution percentage
   */
  minimumEmployerContribution: number;
  
  /**
   * Minimum total contribution percentage
   */
  minimumTotalContribution: number;
}

/**
 * Complete tax year configuration
 */
export interface TaxYearConfig {
  /**
   * Unique identifier for the tax year (e.g., "2023-2024")
   */
  id: string;
  
  /**
   * Display name for the tax year (e.g., "2023/24")
   */
  name: string;
  
  /**
   * Start date of the tax year (ISO format)
   */
  startDate: string;
  
  /**
   * End date of the tax year (ISO format)
   */
  endDate: string;
  
  /**
   * PAYE configuration
   */
  paye: PayeConfig;
  
  /**
   * National Insurance configuration
   */
  nationalInsurance: NationalInsurance;
  
  /**
   * Student loan configuration
   */
  studentLoan: StudentLoanConfig;
  
  /**
   * Statutory payments configuration
   */
  statutory: StatutoryConfig;
  
  /**
   * Pension configuration
   */
  pension: PensionConfig;
}
