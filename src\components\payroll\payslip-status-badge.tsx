import React from "react";
import { Badge } from "@/components/ui/badge";

export type PayslipStatus = "open" | "closed";

interface PayslipStatusBadgeProps {
  status: PayslipStatus | string;
  className?: string;
}

/**
 * A reusable badge component for displaying payslip status
 * Provides consistent styling for open and closed payslips across the application
 */
const PayslipStatusBadge: React.FC<PayslipStatusBadgeProps> = ({
  status,
  className = "",
}) => {
  // Normalize status to handle both string and PayslipStatus type
  const normalizedStatus = status.toLowerCase() as PayslipStatus;

  // Determine badge variant and additional styling based on status
  const getVariantAndClass = (): { variant: "default" | "outline"; additionalClass: string } => {
    switch (normalizedStatus) {
      case "closed":
        return {
          variant: "default",
          additionalClass: "bg-slate-400 dark:bg-slate-700",
        };
      case "open":
        return {
          variant: "default",
          additionalClass: "bg-sky-400 dark:bg-sky-600",
        };
      default:
        return {
          variant: "outline",
          additionalClass: "",
        };
    }
  };

  const { variant, additionalClass } = getVariantAndClass();

  return (
    <Badge variant={variant} className={`${additionalClass} ${className}`}>
      {normalizedStatus === "closed" ? "Closed" : "Open"}
    </Badge>
  );
};

export default PayslipStatusBadge;
