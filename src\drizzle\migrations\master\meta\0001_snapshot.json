{"version": "6", "dialect": "sqlite", "id": "9ce25f67-f99c-4ce6-8cb6-c89996e8b3fd", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"app_settings": {"name": "app_settings", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false, "default": "'singleton'"}, "default_employer_directory": {"name": "default_employer_directory", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "employers": {"name": "employers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'open'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_opened_at": {"name": "last_opened_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "paye_ref": {"name": "paye_ref", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accounts_ref": {"name": "accounts_ref", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "next_pay_date": {"name": "next_pay_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "employees": {"name": "employees", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "tax_code_notices": {"name": "tax_code_notices", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "rti_submissions": {"name": "rti_submissions", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "notifications": {"name": "notifications", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}