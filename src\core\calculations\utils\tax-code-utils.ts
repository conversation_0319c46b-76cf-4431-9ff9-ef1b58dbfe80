/**
 * Tax code utilities for UK PAYE tax calculations
 * Following HMRC PAYErout guidance for proper tax code handling
 */

import { getCurrentTaxYearConfig } from "../tax-years";

/**
 * Get the standard tax code based on the personal allowance
 * @returns The standard tax code (e.g., "1257L")
 */
export function getStandardTaxCode(): string {
  const currentTaxYear = getCurrentTaxYearConfig();
  // Convert personal allowance to tax code format (divide by 10)
  // Personal allowance is in whole pounds, so divide by 10 to get the tax code number
  const taxCodeNumber = Math.floor(currentTaxYear.paye.personalAllowance / 10);
  return `${taxCodeNumber}L`;
}

/**
 * Determine the appropriate tax code and Week1/Month1 status based on starter declaration
 * 
 * @param starterDeclaration Starter declaration (A, B, or C)
 * @param hasPreviousEmploymentPay Whether previous employment pay is filled
 * @returns Object containing tax code and week1Month1 flag
 */
export function getTaxCodeForStarterDeclaration(
  starterDeclaration: string | undefined, 
  hasPreviousEmploymentPay: boolean
): { taxCode: string; week1Month1: boolean } {
  const standardTaxCode = getStandardTaxCode();
  
  // Default values
  let taxCode = standardTaxCode;
  let week1Month1 = false;
  
  // Set based on starter declaration
  switch (starterDeclaration) {
    case "A":
      // First job since 6th April - standard tax code, not week1Month1
      taxCode = standardTaxCode;
      week1Month1 = false;
      break;
      
    case "B":
      // Had another job or benefits - standard tax code, but week1Month1 depends on previous pay
      taxCode = standardTaxCode;
      week1Month1 = !hasPreviousEmploymentPay; // If no previous pay info, use week1Month1
      break;
      
    case "C":
      // Second job or pension - BR code, not week1Month1
      taxCode = "BR";
      week1Month1 = false;
      break;
      
    default:
      // Default to standard tax code
      taxCode = standardTaxCode;
      week1Month1 = false;
  }
  
  return { taxCode, week1Month1 };
}
