"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { PayslipNumberInput } from "@/components/payroll/payslip-controls/payslip-number-input";
import {
  RepeatingControl,
  ZeroizeControl,
  SectionHeader,
} from "@/components/payroll/payslip-controls";

interface AdditionItem {
  id: string;
  type: string;
  name: string;
  amount: number;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface AdditionsSectionData {
  items: AdditionItem[];
}

interface PayslipAdditionsSectionProps {
  data: AdditionsSectionData;
  onChange: (data: AdditionsSectionData) => void;
}

const PayslipAdditionsSection: React.FC<PayslipAdditionsSectionProps> = ({
  data,
  onChange,
}) => {
  // If data.items is undefined, initialize with an empty array
  const items = data.items || [];

  // Handle input changes for a specific item
  const handleItemChange = (
    index: number,
    field: string,
    value: string | boolean | number,
  ) => {
    const newItems = [...items];
    const item = { ...newItems[index] };

    // Type checking and conversion
    if (typeof value === "string" && field === "amount") {
      (item as any)[field] = parseFloat(value) || 0;
    } else {
      (item as any)[field] = value;
    }

    // If isRepeating is set to false, also set zeroizeNext to false
    if (field === "isRepeating" && value === false) {
      item.zeroizeNext = false;
    }

    newItems[index] = item;
    onChange({ ...data, items: newItems });
  };

  // Add a new addition item
  const addBonusItem = () => {
    const newItem: AdditionItem = {
      id: `bonus-${Date.now()}`,
      type: "bonus",
      name: "Bonus",
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    onChange({ ...data, items: [...items, newItem] });
  };

  const addCommissionItem = () => {
    const newItem: AdditionItem = {
      id: `commission-${Date.now()}`,
      type: "commission",
      name: "Commission",
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    onChange({ ...data, items: [...items, newItem] });
  };

  const addAdditionalPayItem = () => {
    const newItem: AdditionItem = {
      id: `additional-${Date.now()}`,
      type: "additional",
      name: "Additional Pay",
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    onChange({ ...data, items: [...items, newItem] });
  };

  const addHolidayPayItem = () => {
    const newItem: AdditionItem = {
      id: `holiday-${Date.now()}`,
      type: "holiday",
      name: "Holiday Pay",
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    onChange({ ...data, items: [...items, newItem] });
  };

  const addExpensesItem = () => {
    const newItem: AdditionItem = {
      id: `expenses-${Date.now()}`,
      type: "expenses",
      name: "Expenses",
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    onChange({ ...data, items: [...items, newItem] });
  };

  // Remove an item
  const removeItem = (id: string) => {
    const newItems = items.filter((item) => item.id !== id);
    onChange({ ...data, items: newItems });
  };

  // Clear all values (set to zero but keep items)
  const clearAllValues = () => {
    const clearedItems = items.map((item) => {
      return { ...item, amount: 0 };
    });
    onChange({ ...data, items: clearedItems });
  };

  // Remove all items
  const removeAllItems = () => {
    onChange({ ...data, items: [] });
  };

  return (
    <div>
      <SectionHeader
        title="Additions"
        sectionType="additions"
        addButtons={[
          { label: "Bonus", onClick: addBonusItem },
          { label: "Commission", onClick: addCommissionItem },
          { label: "Additional Pay", onClick: addAdditionalPayItem },
          { label: "Holiday Pay", onClick: addHolidayPayItem },
          { label: "Expenses", onClick: addExpensesItem },
        ]}
        actionButtons={[
          {
            label: "Clear Values",
            onClick: clearAllValues,
            variant: "outline",
          },
          {
            label: "Delete All",
            onClick: removeAllItems,
            variant: "destructive",
          },
        ]}
      />

      {/* Additions container */}
      <div>
        <div className="mb-3 min-h-[28px] space-y-0">
          {items.map((item) => {
            const itemIndex = items.findIndex((i) => i.id === item.id);
            return (
              <div key={item.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeItem(item.id)}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[120px_90px_auto] items-center gap-1">
                        <span className="text-foreground text-sm whitespace-nowrap">
                          {item.name}
                        </span>
                        <PayslipNumberInput
                          id={`amount-${item.id}`}
                          className="h-7 w-full text-sm"
                          value={item.amount}
                          onChange={(value) =>
                            handleItemChange(itemIndex, "amount", value ?? 0)
                          }
                          placeholder=""
                          decimalPlaces={2}
                          allowCalculations={true}
                        />
                        <div className="ml-4 flex items-center">
                          <RepeatingControl
                            id={item.id}
                            isChecked={item.isRepeating}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ZeroizeControl
                            id={item.id}
                            isChecked={item.zeroizeNext}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!item.isRepeating}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PayslipAdditionsSection;
