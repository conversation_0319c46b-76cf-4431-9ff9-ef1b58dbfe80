"use client";

import React, { useEffect, useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { PayslipNumberInput } from "@/components/payroll/payslip-controls/payslip-number-input";
import {
  RepeatingControl,
  ZeroizeControl,
  SectionHeader,
} from "@/components/payroll/payslip-controls";
import {
  usePayslip,
  useCreatePayslipMutation,
} from "@/hooks/tanstack-query/usePayslip";
import {
  usePayslipAdditionsItemTypes,
  useUpsertPayslipAdditionsLineItemMutation,
  useDeletePayslipAdditionsLineItemMutation,
  useClearAllPayslipAdditionsLineItemsMutation,
  useDeleteAllPayslipAdditionsLineItemsMutation,
} from "@/hooks/tanstack-query/usePayslipAdditions";
import { useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";

interface AdditionItem {
  id: string;
  type: string;
  name: string;
  amount: number;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface AdditionsSectionData {
  items: AdditionItem[];
}

interface PayslipAdditionsSectionProps {
  employeeId: string;
  periodId: string;
}

const PayslipAdditionsSection: React.FC<PayslipAdditionsSectionProps> = ({
  employeeId,
  periodId,
}) => {
  const initRef = useRef(false);
  const itemsRef = useRef<AdditionItem[]>([]);
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const { data, isLoading } = usePayslip(employeeId, periodId);
  const { data: itemTypes } = usePayslipAdditionsItemTypes();
  const upsert = useUpsertPayslipAdditionsLineItemMutation(
    employeeId,
    periodId,
  );
  const del = useDeletePayslipAdditionsLineItemMutation(employeeId, periodId);
  const clearAll = useClearAllPayslipAdditionsLineItemsMutation(
    employeeId,
    periodId,
  );
  const deleteAll = useDeleteAllPayslipAdditionsLineItemsMutation(
    employeeId,
    periodId,
  );
  const createPayslip = useCreatePayslipMutation(employeeId, periodId);
  const queryClient = useQueryClient();

  const [additionsData, setAdditionsData] = useState<AdditionsSectionData>({
    items: [],
  });

  const payId = (data as any)?.payslip?.id;
  const items = additionsData.items || [];

  // Keep ref in sync with state
  useEffect(() => {
    itemsRef.current = items;
  }, [items]);

  // Reset when switching employees/periods
  useEffect(() => {
    setAdditionsData({ items: [] });
    initRef.current = false;
  }, [employeeId, periodId]);

  // Load data from database whenever data or itemTypes change
  useEffect(() => {
    if (data && itemTypes) {
      const additions = (data as any).additions || [];

      // Always load data from database, regardless of initRef state
      const items = additions
        .map((item: any) => {
          const typeMeta = itemTypes.find((t) => t.id === item.item_type_id);
          if (!typeMeta) {
            return null;
          }
          const mappedItem = {
            id: item.id, // Use the real database ID
            type: typeMeta.code,
            name: typeMeta.display_label,
            amount: item.amount,
            isRepeating: item.is_repeating,
            zeroizeNext: item.zeroise_next,
          } as AdditionItem;
          return mappedItem;
        })
        .filter(Boolean) as AdditionItem[];

      setAdditionsData({ items });
      initRef.current = true; // Mark as initialized after loading data
    }
  }, [data, itemTypes]);

  // Reset when switching employees/periods
  useEffect(() => {
    setAdditionsData({ items: [] });
    initRef.current = false;
  }, [employeeId, periodId]);

  // Auto-save when items change (with debouncing)
  useEffect(() => {
    if (initRef.current && items.length > 0) {
      const timeoutId = setTimeout(() => {
        saveChanges();
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [items]);

  // Handle input changes for a specific item
  const handleItemChange = (
    index: number,
    field: string,
    value: string | boolean | number,
  ) => {
    const newItems = [...items];
    const item = { ...newItems[index] };

    // Type checking and conversion
    if (typeof value === "string" && field === "amount") {
      (item as any)[field] = parseFloat(value) || 0;
    } else {
      (item as any)[field] = value;
    }

    // If isRepeating is set to false, also set zeroizeNext to false
    if (field === "isRepeating" && value === false) {
      item.zeroizeNext = false;
    }

    newItems[index] = item;
    setAdditionsData({ items: newItems });

    // Save immediately when user changes values
    setTimeout(() => saveChanges(), 100);
  };

  // Database operations
  const saveChanges = () => {
    const currentItems = itemsRef.current;
    if (!payId) {
      // Only create a payslip once: only invoke when idle
      if (createPayslip.status === "idle") {
        createPayslip.mutate(undefined, {
          onSuccess: (slip) => upsertAll(slip.id, currentItems),
        });
      }
    } else {
      upsertAll(payId, currentItems);
    }
  };

  const upsertAll = (
    payslipId: string,
    itemsToSave: AdditionItem[] = itemsRef.current,
  ) => {
    itemsToSave.forEach((item) => {
      const itemType = itemTypes?.find((t) => t.code === item.type);
      if (!itemType) return;

      const payload: any = {
        payslip_id: payslipId,
        item_type_id: itemType.id,
        amount: item.amount,
        is_repeating: item.isRepeating,
        zeroise_next: item.zeroizeNext,
      };

      // Only include ID if it's a real database ID (not a temporary one)
      // Real database IDs are UUIDs, temporary ones start with "temp-"
      const isRealDbId = item.id && !item.id.startsWith("temp-");
      if (isRealDbId) {
        payload.id = item.id;
      }

      const elementTempId = item.id;
      upsert.mutate(payload, {
        onSuccess: (lineItem) => {
          // On create, remap the UI element id to the real DB id
          if (!isRealDbId) {
            setAdditionsData((current) => ({
              items: current.items.map((e) =>
                e.id === elementTempId ? { ...e, id: lineItem.id } : e,
              ),
            }));
          }
          if (dbPath)
            queryClient.invalidateQueries({
              queryKey: ["payslip", dbPath, employeeId, periodId],
            });
        },
      });
    });
  };

  // Add a new addition item
  const addAdditionItem = (type: string, name: string) => {
    // Check if an item of this type already exists
    const existingItem = items.find((item) => item.type === type);
    if (existingItem) {
      console.log(`Item of type ${type} already exists, not adding duplicate`);
      return;
    }

    const newItem: AdditionItem = {
      id: `temp-${type}-${Date.now()}`,
      type,
      name,
      amount: 0,
      isRepeating: true,
      zeroizeNext: true,
    };
    setAdditionsData({ items: [...items, newItem] });
    // Save the new item immediately
    setTimeout(() => saveChanges(), 100);
  };

  const addBonusItem = () => addAdditionItem("bonus", "Bonus");
  const addCommissionItem = () => addAdditionItem("commission", "Commission");
  const addAdditionalPayItem = () =>
    addAdditionItem("additional", "Additional Pay");
  const addHolidayPayItem = () => addAdditionItem("holiday", "Holiday Pay");
  const addExpensesItem = () => addAdditionItem("expenses", "Expenses");

  // Remove an item
  const removeItem = (id: string) => {
    // detect and delete removed items
    const prevIds = items.map((e) => e.id);
    const newItems = items.filter((item) => item.id !== id);
    setAdditionsData({ items: newItems });
    const newIds = newItems.map((e) => e.id);
    const removedIds = prevIds.filter((id) => !newIds.includes(id));
    removedIds.forEach((id) => {
      // Only delete if it's a real DB id (not temporary)
      const isRealDbId = !id.startsWith("temp-");
      if (isRealDbId) {
        del.mutate(id, {
          onSuccess: () => {
            if (dbPath)
              queryClient.invalidateQueries({
                queryKey: ["payslip", dbPath, employeeId, periodId],
              });
          },
        });
      }
    });
  };

  // Clear all values (set to zero but keep items)
  const clearAllValues = () => {
    if (payId) {
      clearAll.mutate(payId, {
        onSuccess: () => {
          const clearedItems = items.map((item) => {
            return { ...item, amount: 0 };
          });
          setAdditionsData({ items: clearedItems });
        },
      });
    } else {
      const clearedItems = items.map((item) => {
        return { ...item, amount: 0 };
      });
      setAdditionsData({ items: clearedItems });
    }
  };

  // Remove all items
  const removeAllItems = () => {
    if (payId) {
      deleteAll.mutate(payId, {
        onSuccess: () => {
          setAdditionsData({ items: [] });
        },
      });
    } else {
      setAdditionsData({ items: [] });
    }
  };

  return (
    <div>
      <SectionHeader
        title="Additions"
        sectionType="additions"
        addButtons={[
          { label: "Bonus", onClick: addBonusItem },
          { label: "Commission", onClick: addCommissionItem },
          { label: "Additional Pay", onClick: addAdditionalPayItem },
          { label: "Holiday Pay", onClick: addHolidayPayItem },
          { label: "Expenses", onClick: addExpensesItem },
        ]}
        actionButtons={[
          {
            label: "Clear Values",
            onClick: clearAllValues,
            variant: "outline",
          },
          {
            label: "Delete All",
            onClick: removeAllItems,
            variant: "destructive",
          },
        ]}
      />

      {/* Additions container */}
      <div>
        <div className="mb-3 min-h-[28px] space-y-0">
          {items.map((item) => {
            const itemIndex = items.findIndex((i) => i.id === item.id);
            return (
              <div key={item.id} className="bg-card rounded-sm px-1.5">
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground mr-3 h-5 w-5"
                    onClick={() => removeItem(item.id)}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                  <div className="flex-grow">
                    <div className="mb-1 flex items-center">
                      <div className="ml-4 grid grid-cols-[120px_90px_auto] items-center gap-1">
                        <span className="text-foreground text-sm whitespace-nowrap">
                          {item.name}
                        </span>
                        <PayslipNumberInput
                          id={`amount-${item.id}`}
                          className="h-7 w-full text-sm"
                          value={item.amount}
                          onChange={(value) =>
                            handleItemChange(itemIndex, "amount", value ?? 0)
                          }
                          placeholder=""
                          decimalPlaces={2}
                          allowCalculations={true}
                        />
                        <div className="ml-4 flex items-center">
                          <RepeatingControl
                            id={item.id}
                            isChecked={item.isRepeating}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "isRepeating",
                                checked,
                              )
                            }
                          />

                          <ZeroizeControl
                            id={item.id}
                            isChecked={item.zeroizeNext}
                            onChange={(checked) =>
                              handleItemChange(
                                itemIndex,
                                "zeroizeNext",
                                checked,
                              )
                            }
                            disabled={!item.isRepeating}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PayslipAdditionsSection;
