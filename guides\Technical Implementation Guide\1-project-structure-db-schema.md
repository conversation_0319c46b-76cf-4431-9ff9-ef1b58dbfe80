# UK Payroll Application - Technical Guide Part 1: Project Structure & Database Schema

## Project Structure

```
├── src/
│   ├── app/                      # Next.js app directory
│   │   ├── dashboard/            # Main dashboard
│   │   ├── employers/            # Employer management
│   │   ├── employees/            # Employee management
│   │   ├── payroll/              # Payroll processing
│   │   ├── reports/              # Reports generation
│   │   ├── settings/             # Application settings
│   │   └── api/                  # API routes
│   │       ├── hmrc/             # HMRC API integration
│   │       └── webhooks/         # Webhook handlers
│   ├── components/               # Shared React components
│   │   ├── ui/                   # Shadcn UI components
│   │   ├── forms/                # Form components
│   │   ├── payroll/              # Payroll-specific components
│   │   ├── data-tables/          # Table components (TanStack)
│   │   ├── reports/              # Report components (React-PDF, Excel.js)
│   │   └── layouts/              # Layout components
│   ├── core/                     # Core business logic
│   │   ├── calculations/         # Payroll calculation engine
│   │   │   ├── tax-years/        # Tax year configurations
│   │   │   ├── paye/             # PAYE calculations
│   │   │   ├── ni/               # National Insurance calculations
│   │   │   ├── pensions/         # Pension calculations
│   │   │   ├── statutory/        # Statutory payments (SSP, SMP, etc.)
│   │   │   └── workers/          # Web Workers implementations
│   │   ├── hmrc/                 # HMRC API integration
│   │   │   ├── api/              # API clients
│   │   │   ├── auth/             # OAuth implementation
│   │   │   └── rti/              # RTI submission
│   │   └── documents/            # Document generation
│   │       ├── pdf/              # React-PDF templates
│   │       └── excel/            # Excel.js templates
│   ├── db/                       # Database implementation
│   │   ├── schema/               # Drizzle schema definitions
│   │   │   ├── master.ts         # Master database schema
│   │   │   └── employer.ts       # Employer database schema
│   │   ├── migrations/           # Drizzle migrations
│   │   ├── adapters/             # Database adapters
│   │   │   ├── master.ts         # Master database adapter
│   │   │   └── employer.ts       # Employer database adapter
│   │   ├── queries/              # Common database queries
│   │   └── utils/                # Database utilities
│   ├── lib/                      # Shared utilities
│   │   ├── validation/           # Form validation (Zod)
│   │   ├── formatters/           # Data formatting utilities
│   │   ├── encryption/           # Encryption utilities
│   │   └── helpers/              # General helper functions
│   ├── hooks/                    # Custom React hooks
│   │   ├── use-database.ts       # Database access hook
│   │   ├── use-electron.ts       # Electron features hook
│   │   ├── use-employer.ts       # Employer database hook
│   │   └── use-query/            # TanStack Query hooks
│   │       ├── employers.ts      # Employer data query hooks
│   │       ├── employees.ts      # Employee data query hooks
│   │       ├── payroll.ts        # Payroll data query hooks
│   │       └── reports.ts        # Report data query hooks
│   ├── providers/                # Context providers
│   │   ├── database-provider.tsx # Database context
│   │   └── query-provider.tsx    # TanStack Query provider
│   ├── styles/                   # Global styles
│   ├── types/                    # TypeScript type definitions
│   ├── workers/                  # Web Workers
│   │   ├── calculation-worker.ts # Payroll calculation worker
│   │   └── report-worker.ts      # Report generation worker
│   └── electron/                 # Electron-specific code
│       ├── main/                 # Main process
│       ├── preload/              # Preload scripts
│       ├── ipc/                  # IPC handlers
│       ├── file-handlers/        # File association handlers
│       └── updater/              # Auto-update mechanism
├── drizzle/                      # Drizzle ORM configuration
├── electron-builder.json         # Electron build config
├── next.config.js                # Next.js configuration
└── tailwind.config.js            # Tailwind configuration
```

## Database Implementation

### Database Architecture Overview

Our application uses a dual-database architecture:

1. **Master Database**: Contains application-level data like employer references, recently accessed employers, and application settings.
   
2. **Employer Databases**: One SQLite file per employer containing all employer-specific data, including employees, payroll, etc.

This architecture provides excellent isolation, portability, and performance for desktop users.

### Drizzle Schema Implementation

#### Master Database Schema

Start with a minimal schema focusing on employer file tracking:

```typescript
// src/db/schema/master.ts
import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Employers table (metadata, not actual employer data)
export const employers = sqliteTable('employers', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  filePath: text('file_path').notNull(),
  lastAccessed: integer('last_accessed', { mode: 'timestamp' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().defaultNow(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().defaultNow(),
});

// Application settings
export const appSettings = sqliteTable('app_settings', {
  id: text('id').primaryKey(),
  key: text('key').notNull().unique(),
  value: text('value', { mode: 'json' }),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().defaultNow(),
});

// Zod schemas for validation
export const insertEmployerSchema = createInsertSchema(employers);
export const selectEmployerSchema = createSelectSchema(employers);

// Extra validation
export const createEmployerSchema = insertEmployerSchema.extend({
  name: z.string().min(1, "Employer name is required"),
  filePath: z.string().min(1, "File path is required"),
});
```

#### Employer Database Schema

Start with a minimal schema covering basic employer and employee data:

```typescript
// src/db/schema/employer.ts
import { sqliteTable, text, integer, primaryKey, index } from 'drizzle-orm/sqlite-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Employer details
export const employer = sqliteTable('employer', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  tradingName: text('trading_name'),
  companyNumber: text('company_number'),
  taxReference: text('tax_reference'),
  address: text('address', { mode: 'json' }),
  contactDetails: text('contact_details', { mode: 'json' }),
  defaultPaymentMethod: text('default_payment_method'),
  taxOffice: text('tax_office'),
  taxOfficeReference: text('tax_office_reference'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().defaultNow(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().defaultNow(),
});

// Departments
export const departments = sqliteTable('departments', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  code: text('code'),
  costCenter: text('cost_center'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().defaultNow(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().defaultNow(),
});

// Employees
export const employees = sqliteTable('employees', {
  id: text('id').primaryKey(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  dateOfBirth: integer('date_of_birth', { mode: 'timestamp' }),
  niNumber: text('ni_number'),
  startDate: integer('start_date', { mode: 'timestamp' }).notNull(),
  leavingDate: integer('leaving_date', { mode: 'timestamp' }),
  taxCode: text('tax_code'),
  payrollNumber: text('payroll_number'),
  departmentId: text('department_id').references(() => departments.id),
  address: text('address', { mode: 'json' }),
  bankDetails: text('bank_details'), // Will be encrypted
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().defaultNow(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().defaultNow(),
}, (table) => {
  return {
    nameIdx: index("name_idx").on(table.lastName, table.firstName),
    niIdx: index("ni_idx").on(table.niNumber),
  };
});

// Zod schemas for validation
export const insertEmployeeSchema = createInsertSchema(employees);
export const selectEmployeeSchema = createSelectSchema(employees);

// Extra validation
export const createEmployeeSchema = insertEmployeeSchema.extend({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  niNumber: z.string().optional().refine(
    (val) => !val || /^[A-CEGHJ-PR-TW-Z]{1}[A-CEGHJ-NPR-TW-Z]{1}[0-9]{6}[A-D]{1}$/.test(val),
    { message: "Invalid National Insurance number format" }
  ),
});
```
