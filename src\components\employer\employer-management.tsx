"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Employer } from "@/lib/schemas/employer";
import { useThemeSafeNavigation } from "@/hooks/use-theme-safe-navigation";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  useEmployerQuery,
  useUpdateEmployerMutation,
} from "@/hooks/tanstack-query/useEmployerQuery";

// Import dialog components for unsaved changes warning
import { UnsavedChangesDialog } from "@/components/misc/unsaved-changes-dialog";

// Import employer section components
import BasicInformationSection from "@/components/employer/sections/basic-information-section";
import RegistrationDetailsSection from "@/components/employer/sections/registration-details-section";
import RTISubmissionsSection from "@/components/employer/sections/rti-submissions-section";
import TypicalEmployeeSection from "@/components/employer/sections/typical-employee-section";
import ClientDetailsSection from "@/components/employer/sections/client-details-section";

const EmployerManagement: React.FC = () => {
  const { openEmployers, activeEmployerId } = useEmployerDBContext();
  // Find the active employer's dbPath
  const activeEmployer = openEmployers.find((e) => e.id === activeEmployerId);
  const dbPath = activeEmployer?.dbPath;
  const employerQuery = useEmployerQuery(dbPath);
  const updateEmployerMutation = useUpdateEmployerMutation(dbPath);

  // Local state for editing
  const [employer, setEmployer] = useState<Employer | null>(null);
  const [activeSection, setActiveSection] = useState<string>("basic");
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );
  const [originalData, setOriginalData] = useState<Employer | null>(null);

  // Use our custom hook for theme-safe navigation
  const { navigateTo } = useThemeSafeNavigation();

  // Reset local state when switching employers
  useEffect(() => {
    setEmployer(null);
    setOriginalData(null);
    setHasUnsavedChanges(false);
  }, [dbPath]);

  // Initialise local state from DB result
  useEffect(() => {
    if (employerQuery.data && !employer) {
      setEmployer(employerQuery.data);
      setOriginalData(employerQuery.data);
      setHasUnsavedChanges(false);
    }
  }, [employerQuery.data, employer]);

  // Track changes to enable/disable save/cancel buttons
  useEffect(() => {
    // Deep comparison using JSON stringify
    if (!employer || !originalData) {
      setHasUnsavedChanges(false);
      return;
    }
    const jsonOriginal = JSON.stringify(originalData);
    const jsonCurrent = JSON.stringify(employer);
    const currentHasChanges = jsonOriginal !== jsonCurrent;
    setHasUnsavedChanges(currentHasChanges);
  }, [employer, originalData]);

  // Define section navigation items
  const navigationSections = [
    { id: "basic", label: "Company Details" },
    { id: "registration", label: "Registration" },
    { id: "rti", label: "RTI" },
    { id: "typical", label: "Default Settings" },
    { id: "client", label: "Client Management" },
  ];

  // Add event listener to intercept navigation away from the page
  useEffect(() => {
    // Only set up the listener if there are unsaved changes
    if (!hasUnsavedChanges) return;

    // Function to intercept navigation link clicks
    const interceptNavigation = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Look for clicks on navbar links or buttons (excluding the employer tabs)
      const navItem = target.closest("a[href], button[data-nav]");

      if (navItem) {
        // Skip if the clicked element is within our employer tabs
        const isEmployerTab = navItem.closest(".employer-tabs");
        if (isEmployerTab) return;

        // If it's a main nav item (from the top navigation)
        const isMainNav = navItem.closest("nav, header");

        if (isMainNav) {
          // Get the destination URL if it's a link
          const navUrl =
            navItem.getAttribute("href") ||
            navItem.getAttribute("data-href") ||
            "/";

          // Skip if navigating to the current page
          if (navUrl === "#" || navUrl === "/employer") return;

          // Prevent default navigation
          event.preventDefault();
          event.stopPropagation();

          // Show the dialog and store pending navigation
          setPendingNavigation(navUrl);
          setShowUnsavedDialog(true);
        }
      }
    };

    // Add the event listener
    document.addEventListener("click", interceptNavigation, true);

    // Clean up
    return () => {
      document.removeEventListener("click", interceptNavigation, true);
    };
  }, [hasUnsavedChanges]);

  // Handle section change - now simplified since we're not showing dialog for tab changes
  const handleSectionChange = (sectionId: string) => {
    setActiveSection(sectionId);
  };

  // Function to handle proceed action from warning dialog
  const handleProceedWithNavigation = () => {
    setShowUnsavedDialog(false);

    // Navigate to the pending URL using our theme-safe navigation
    if (pendingNavigation) {
      navigateTo(pendingNavigation);
    }

    // Reset flags
    setHasUnsavedChanges(false);
    setPendingNavigation(null);
  };

  // Function to continue editing without navigation
  const handleContinueEditing = () => {
    // Just close the dialog without other actions
    setShowUnsavedDialog(false);
  };

  // Function to handle save action from warning dialog
  const handleSaveBeforeNavigation = () => {
    // Close the dialog
    setShowUnsavedDialog(false);

    // Save changes
    handleSave();

    // Then navigate to the pending URL using our theme-safe navigation
    if (pendingNavigation) {
      navigateTo(pendingNavigation);
    }

    // Reset pending navigation
    setPendingNavigation(null);
  };

  const handleInputChange = (field: string, value: any) => {
    setEmployer((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  // Handler for nested object properties (e.g., typicalWorkingDays.monday, filingTypes.eps)
  const handleNestedChange = (
    parentField: string,
    field: string,
    value: any,
  ) => {
    setEmployer((prev) => {
      if (!prev) return prev;
      // Handle known nested object fields
      if (parentField === "typicalWorkingDays") {
        let parentValue = prev.typicalWorkingDays;
        if (!parentValue || typeof parentValue !== "object" || Array.isArray(parentValue)) {
          parentValue = {
            monday: false,
            tuesday: false,
            wednesday: false,
            thursday: false,
            friday: false,
            saturday: false,
            sunday: false,
          };
        }
        return {
          ...prev,
          typicalWorkingDays: {
            ...parentValue,
            [field]: value,
          },
        };
      } else if (parentField === "filingTypes") {
        let parentValue = prev.filingTypes;
        if (!parentValue || typeof parentValue !== "object" || Array.isArray(parentValue)) {
          parentValue = {
            finalPays: false,
            eps: false,
            p60s: false,
            p11ds: false,
          };
        }
        return {
          ...prev,
          filingTypes: {
            ...parentValue,
            [field]: value,
          },
        };
      } else {
        // For any other field, just set the value directly (no spreading)
        return {
          ...prev,
          [parentField]: { [field]: value },
        };
      }
    });
  };

  const handleSave = async () => {
    if (!employer) return;
    try {
      await updateEmployerMutation.mutateAsync(employer);
      setOriginalData({ ...employer });
      setHasUnsavedChanges(false);
    } catch (error) {
      // Optionally, show a notification or error message
      console.error("Failed to save employer:", error);
    }
  };

  const handleCancel = () => {
    // Reset data to original state
    setEmployer(originalData);

    // Reset unsaved changes flag
    setHasUnsavedChanges(false);
  };

  // Function to render the active section
  const renderActiveSection = () => {
    switch (activeSection) {
      case "basic":
        return (
          employer && (
            <BasicInformationSection
              employer={employer}
              onChange={handleInputChange}
            />
          )
        );
      case "registration":
        return (
          employer && (
            <RegistrationDetailsSection
              employer={employer}
              onChange={handleInputChange}
            />
          )
        );
      case "rti":
        return (
          employer && (
            <RTISubmissionsSection
              employer={employer}
              onChange={handleInputChange}
            />
          )
        );
      case "typical":
        return (
          employer && (
            <TypicalEmployeeSection
              employer={employer}
              onChange={handleInputChange}
              onNestedChange={handleNestedChange}
            />
          )
        );
      case "client":
        return (
          employer && (
            <ClientDetailsSection
              employer={employer}
              onChange={handleInputChange}
              onNestedChange={handleNestedChange}
            />
          )
        );
      default:
        return (
          employer && (
            <BasicInformationSection
              employer={employer}
              onChange={handleInputChange}
            />
          )
        );
    }
  };

  if (employerQuery.isLoading || !employer) {
    return <div className="p-6 text-lg">Loading employer data…</div>;
  }
  if (employerQuery.error) {
    return (
      <div className="p-6 text-red-600">
        Error loading employer: {employerQuery.error.message}
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="mx-auto w-full max-w-7xl px-2">
        <div className="flex h-[calc(100vh-180px)] flex-col rounded-xl border">
          {/* Horizontal navigation */}
          <div className="flex gap-1 border-b-2 px-4 pt-4 pb-2">
            {navigationSections.map((section) => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "ghost"}
                size="sm"
                onClick={() => handleSectionChange(section.id)}
                className="h-10 px-4 hover:bg-slate-200"
              >
                {section.label}
              </Button>
            ))}
          </div>

          {/* Content area - shows active section */}
          <div className="flex-1 overflow-y-auto px-4 py-6">
            {renderActiveSection()}
          </div>

          {/* Bottom buttons */}
          <div className="flex items-center justify-center gap-3 border-t p-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              size="sm"
              disabled={!hasUnsavedChanges}
              className={
                !hasUnsavedChanges
                  ? "cursor-not-allowed opacity-50"
                  : "hover:bg-red-500/90 hover:text-white dark:hover:bg-red-500/90"
              }
            >
              Cancel Changes
            </Button>
            <Button
              onClick={handleSave}
              size="sm"
              disabled={!hasUnsavedChanges}
              className={
                !hasUnsavedChanges
                  ? "cursor-not-allowed opacity-50"
                  : "hover:bg-emerald-500"
              }
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      {/* Unsaved changes warning dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onOpenChange={setShowUnsavedDialog}
        onContinue={handleContinueEditing}
        onDiscard={handleProceedWithNavigation}
        onSave={handleSaveBeforeNavigation}
      />
    </div>
  );
};

export default EmployerManagement;
