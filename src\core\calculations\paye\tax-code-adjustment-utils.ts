/**
 * Tax Code Adjustment Utilities
 * 
 * Helper functions for applying tax code adjustments based on P7X and P9X forms.
 * These functions are designed to be used in the application to make it easier
 * to apply tax code adjustments in bulk according to HMRC guidance.
 */

import {
  AdjustmentFormType,
  TaxCodeAdjustmentInstruction,
  TaxCodeAdjustmentRecord,
  EmployeeTaxCodeInfo,
  TaxCodeAdjustmentResult,
  applyTaxCodeAdjustments,
  resetTaxCodeForNewTaxYear,
  createAdjustmentRecord
} from './tax-code-adjustments';

/**
 * Process a P7X form for current tax year adjustments
 * 
 * @param employees List of employee tax code information
 * @param effectiveDate Date from which changes take effect
 * @param taxYear Current tax year (e.g. 2023 for 2023-2024)
 * @param suffixAdjustments Adjustments to make to each suffix code
 * @param newEmployeeInstructions Optional instructions for new employees
 * @param previousAdjustments Previous adjustment records to prevent duplicate adjustments
 * @returns Results of tax code adjustments and new adjustment record
 */
export function processP7XForm(
  employees: EmployeeTaxCodeInfo[],
  effectiveDate: Date,
  taxYear: number,
  suffixAdjustments: Array<{suffix: string, adjustmentAmount: number}>,
  newEmployeeInstructions?: string,
  previousAdjustments: TaxCodeAdjustmentRecord[] = []
): {
  adjustmentResults: TaxCodeAdjustmentResult[];
  adjustmentRecord: TaxCodeAdjustmentRecord;
} {
  // Create P7X instruction
  const p7xInstruction: TaxCodeAdjustmentInstruction = {
    formType: AdjustmentFormType.P7X,
    effectiveDate,
    taxYear,
    suffixAdjustments,
    newEmployeesInstructions: newEmployeeInstructions
  };
  
  // Apply adjustments
  const adjustmentResults = applyTaxCodeAdjustments(
    employees,
    p7xInstruction,
    previousAdjustments
  );
  
  // Create adjustment record
  const adjustedEmployeeIds = adjustmentResults.map(result => result.employeeId);
  const adjustmentRecord = createAdjustmentRecord(p7xInstruction, adjustedEmployeeIds);
  
  return {
    adjustmentResults,
    adjustmentRecord
  };
}

/**
 * Process a P9X form for new tax year adjustments
 * 
 * @param employees List of employee tax code information
 * @param newTaxYear The new tax year (e.g. 2024 for 2024-2025)
 * @param suffixAdjustments Adjustments to make to each suffix code
 * @param newEmployeeInstructions Optional instructions for new employees
 * @returns Results of tax code adjustments and new adjustment record
 */
export function processP9XForm(
  employees: EmployeeTaxCodeInfo[],
  newTaxYear: number,
  suffixAdjustments: Array<{suffix: string, adjustmentAmount: number}>,
  newEmployeeInstructions?: string
): {
  adjustmentResults: TaxCodeAdjustmentResult[];
  adjustmentRecord: TaxCodeAdjustmentRecord;
} {
  // For P9X, effective date is always April 6 of the new tax year
  const effectiveDate = new Date(newTaxYear, 3, 6); // Month is 0-indexed, so 3 = April
  
  // Create P9X instruction
  const p9xInstruction: TaxCodeAdjustmentInstruction = {
    formType: AdjustmentFormType.P9X,
    effectiveDate,
    taxYear: newTaxYear,
    suffixAdjustments,
    newEmployeesInstructions: newEmployeeInstructions
  };
  
  // Apply adjustments with no previous adjustments check
  // (P9X is applied at the start of a new tax year)
  const adjustmentResults = applyTaxCodeAdjustments(
    employees,
    p9xInstruction,
    []
  );
  
  // Create adjustment record
  const adjustedEmployeeIds = adjustmentResults.map(result => result.employeeId);
  const adjustmentRecord = createAdjustmentRecord(p9xInstruction, adjustedEmployeeIds);
  
  return {
    adjustmentResults,
    adjustmentRecord
  };
}

/**
 * Process tax codes for new tax year rollover
 * 
 * According to spec 15.6: "All suffix codes, including codes NT, are to be set 
 * to operate on a cumulative basis when they are carried forward to a new tax year."
 * 
 * @param employees List of employee tax code information
 * @returns Updated tax codes for each employee
 */
export function processNewTaxYearTaxCodes(
  employees: EmployeeTaxCodeInfo[]
): Array<{employeeId: string, originalTaxCode: string, newTaxCode: string}> {
  return employees.map(employee => {
    const newTaxCode = resetTaxCodeForNewTaxYear(employee.taxCode);
    
    return {
      employeeId: employee.employeeId,
      originalTaxCode: employee.taxCode,
      newTaxCode
    };
  });
}

/**
 * Detect and flag potential refunds due after tax code adjustments
 * 
 * According to spec 15.4: "Any refunds arising from the uplifting of codes of 
 * existing employees under P7X authority may be made without further authority
 * from the HMRC Office. No refunds will be due for those employees whose
 * suffix code is operated on a Week 1 or Month 1 basis."
 * 
 * @param adjustmentResults Results from a tax code adjustment
 * @returns List of employees who may be due a refund
 */
export function identifyPotentialRefunds(
  adjustmentResults: TaxCodeAdjustmentResult[]
): Array<{employeeId: string, oldTaxCode: string, newTaxCode: string}> {
  return adjustmentResults
    .filter(result => result.refundMayBeDue)
    .map(result => ({
      employeeId: result.employeeId,
      oldTaxCode: result.originalTaxCode,
      newTaxCode: result.newTaxCode
    }));
}
