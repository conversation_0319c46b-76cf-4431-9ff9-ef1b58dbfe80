/**
 * Attachment Order Calculator
 * 
 * This module provides functions for calculating UK attachment orders
 * according to regulations. It supports all UK attachment order types:
 * - Attachment of Earnings Orders (AEOs)
 * - Council Tax Attachment Orders (CTAOs)
 * - Direct Earnings Attachments (DEAs)
 * - Child Maintenance Service (CMS)
 * - Student Loan Attachment Orders (SLAOs)
 */

import {
  AttachmentOrderCalculationInput,
  AttachmentOrderCalculationResult,
  AttachmentOrderType,
  AttachmentOrderPriority,
  AttachmentOrderCalculationMethod,
  PayPeriodType,
  DEARateType
} from './types';

/**
 * Calculate attachment order deduction for the given input
 * 
 * @param input Attachment order calculation input parameters
 * @returns Attachment order calculation result with detailed breakdown
 */
export function calculateAttachmentOrder(
  input: AttachmentOrderCalculationInput
): AttachmentOrderCalculationResult {
  const {
    orderType,
    priority,
    calculationMethod,
    payPeriod,
    taxYearConfig,
    grossPay,
    netPay,
    totalDeductions,
    fixedAmount,
    percentage,
    deaRateType,
    protectedEarnings,
    outstandingAmount,
    previousDeductionsInTaxYear = 0,
    maximumDeductionPerPeriod,
    cmsRate,
    cmsCollectionFee,
    councilTaxBand,
    studentLoanPlanType,
    higherPriorityDeductions = 0
  } = input;

  // Initialize result
  const result: AttachmentOrderCalculationResult = {
    deduction: 0,
    limitedByProtectedEarnings: false,
    limitedByMaximumDeduction: false,
    limitedByOutstandingAmount: false,
    unlimitedDeduction: 0,
    cumulativeDeductions: previousDeductionsInTaxYear,
    netPayAfterDeduction: netPay
  };

  // Calculate available pay after higher priority deductions
  const availablePay = netPay - higherPriorityDeductions;

  // Calculate protected earnings (if provided)
  const protectedEarningsAmount = protectedEarnings ?? 0;
  result.protectedEarningsUsed = protectedEarningsAmount;

  // Calculate deduction based on order type and calculation method
  let calculatedDeduction = 0;

  switch (orderType) {
    case AttachmentOrderType.AEO:
      calculatedDeduction = calculateAEO(input, availablePay);
      break;
    case AttachmentOrderType.CTAO:
      calculatedDeduction = calculateCTAO(input, availablePay);
      break;
    case AttachmentOrderType.DEA:
      calculatedDeduction = calculateDEA(input, availablePay, result);
      break;
    case AttachmentOrderType.CMS:
      calculatedDeduction = calculateCMS(input, availablePay, result);
      break;
    case AttachmentOrderType.SLAO:
      calculatedDeduction = calculateSLAO(input, availablePay);
      break;
    default:
      throw new Error(`Unsupported attachment order type: ${orderType}`);
  }

  // Store the unlimited deduction amount
  result.unlimitedDeduction = calculatedDeduction;

  // Apply protected earnings limit
  if (protectedEarningsAmount > 0 && (availablePay - calculatedDeduction) < protectedEarningsAmount) {
    const maxDeduction = Math.max(0, availablePay - protectedEarningsAmount);
    if (maxDeduction < calculatedDeduction) {
      calculatedDeduction = maxDeduction;
      result.limitedByProtectedEarnings = true;
    }
  }

  // Apply maximum deduction per period limit
  if (maximumDeductionPerPeriod !== undefined && calculatedDeduction > maximumDeductionPerPeriod) {
    calculatedDeduction = maximumDeductionPerPeriod;
    result.limitedByMaximumDeduction = true;
  }

  // Apply outstanding amount limit
  if (outstandingAmount !== undefined && calculatedDeduction > outstandingAmount) {
    calculatedDeduction = outstandingAmount;
    result.limitedByOutstandingAmount = true;
    result.remainingAmount = 0;
  } else if (outstandingAmount !== undefined) {
    result.remainingAmount = outstandingAmount - calculatedDeduction;
  }

  // Set final deduction amount
  result.deduction = Math.round(calculatedDeduction * 100) / 100;
  
  // Update cumulative deductions
  result.cumulativeDeductions += result.deduction;
  
  // Calculate net pay after deduction
  result.netPayAfterDeduction = availablePay - result.deduction;

  return result;
}

/**
 * Calculate Attachment of Earnings Order (AEO)
 */
function calculateAEO(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const {
    calculationMethod,
    fixedAmount,
    percentage
  } = input;

  switch (calculationMethod) {
    case AttachmentOrderCalculationMethod.FIXED_AMOUNT:
      if (fixedAmount === undefined) {
        throw new Error('Fixed amount is required for FIXED_AMOUNT calculation method');
      }
      return fixedAmount;

    case AttachmentOrderCalculationMethod.PERCENTAGE:
      if (percentage === undefined) {
        throw new Error('Percentage is required for PERCENTAGE calculation method');
      }
      return availablePay * (percentage / 100);

    case AttachmentOrderCalculationMethod.TABLE_BASED:
      // AEOs use a table-based calculation based on the "normal deduction rate" tables
      return calculateAEOTableBased(input, availablePay);

    default:
      throw new Error(`Unsupported calculation method for AEO: ${calculationMethod}`);
  }
}

/**
 * Calculate AEO using table-based method (Normal Deduction Rate)
 */
function calculateAEOTableBased(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const { payPeriod } = input;
  
  // Get the appropriate NDR table based on pay period
  const ndrTable = getNDRTable(payPeriod);
  
  // Find the appropriate band for the available pay
  for (let i = 0; i < ndrTable.length; i++) {
    const band = ndrTable[i];
    if (availablePay <= (band.upperLimit ?? Number.MAX_VALUE)) {
      // Calculate deduction based on band percentage
      return (availablePay - (band.lowerLimit - 0.01)) * (band.percentage / 100);
    }
  }
  
  // If no band matches (shouldn't happen with proper table), use default
  return availablePay * 0.4; // 40% is the default maximum
}

/**
 * Get Normal Deduction Rate (NDR) table for the given pay period
 */
function getNDRTable(payPeriod: PayPeriodType): { lowerLimit: number; upperLimit: number | null; percentage: number }[] {
  // These tables are based on the County Court AEO rates
  // They would typically be stored in the tax year config, but for simplicity we're defining them here
  
  switch (payPeriod) {
    case PayPeriodType.WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 75, percentage: 0 },
        { lowerLimit: 75, upperLimit: 135, percentage: 3 },
        { lowerLimit: 135, upperLimit: 185, percentage: 5 },
        { lowerLimit: 185, upperLimit: 225, percentage: 7 },
        { lowerLimit: 225, upperLimit: 355, percentage: 12 },
        { lowerLimit: 355, upperLimit: 505, percentage: 17 },
        { lowerLimit: 505, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.TWO_WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 150, percentage: 0 },
        { lowerLimit: 150, upperLimit: 270, percentage: 3 },
        { lowerLimit: 270, upperLimit: 370, percentage: 5 },
        { lowerLimit: 370, upperLimit: 450, percentage: 7 },
        { lowerLimit: 450, upperLimit: 710, percentage: 12 },
        { lowerLimit: 710, upperLimit: 1010, percentage: 17 },
        { lowerLimit: 1010, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.FOUR_WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 300, percentage: 0 },
        { lowerLimit: 300, upperLimit: 540, percentage: 3 },
        { lowerLimit: 540, upperLimit: 740, percentage: 5 },
        { lowerLimit: 740, upperLimit: 900, percentage: 7 },
        { lowerLimit: 900, upperLimit: 1420, percentage: 12 },
        { lowerLimit: 1420, upperLimit: 2020, percentage: 17 },
        { lowerLimit: 2020, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.MONTHLY:
      return [
        { lowerLimit: 0, upperLimit: 325, percentage: 0 },
        { lowerLimit: 325, upperLimit: 585, percentage: 3 },
        { lowerLimit: 585, upperLimit: 805, percentage: 5 },
        { lowerLimit: 805, upperLimit: 975, percentage: 7 },
        { lowerLimit: 975, upperLimit: 1540, percentage: 12 },
        { lowerLimit: 1540, upperLimit: 2190, percentage: 17 },
        { lowerLimit: 2190, upperLimit: null, percentage: 40 }
      ];
      
    default:
      throw new Error(`Unsupported pay period for NDR table: ${payPeriod}`);
  }
}

/**
 * Calculate Council Tax Attachment Order (CTAO)
 */
function calculateCTAO(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const {
    calculationMethod,
    fixedAmount,
    percentage,
    councilTaxBand
  } = input;

  switch (calculationMethod) {
    case AttachmentOrderCalculationMethod.FIXED_AMOUNT:
      if (fixedAmount === undefined) {
        throw new Error('Fixed amount is required for FIXED_AMOUNT calculation method');
      }
      return fixedAmount;

    case AttachmentOrderCalculationMethod.PERCENTAGE:
      if (percentage === undefined) {
        throw new Error('Percentage is required for PERCENTAGE calculation method');
      }
      return availablePay * (percentage / 100);

    case AttachmentOrderCalculationMethod.TABLE_BASED:
      // CTAOs use a table-based calculation similar to AEOs
      return calculateCTAOTableBased(input, availablePay);

    default:
      throw new Error(`Unsupported calculation method for CTAO: ${calculationMethod}`);
  }
}

/**
 * Calculate CTAO using table-based method
 */
function calculateCTAOTableBased(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const { payPeriod } = input;
  
  // Get the appropriate CTAO table based on pay period
  const ctaoTable = getCTAOTable(payPeriod);
  
  // Find the appropriate band for the available pay
  for (let i = 0; i < ctaoTable.length; i++) {
    const band = ctaoTable[i];
    if (availablePay <= (band.upperLimit ?? Number.MAX_VALUE)) {
      // Calculate deduction based on band percentage
      return (availablePay - (band.lowerLimit - 0.01)) * (band.percentage / 100);
    }
  }
  
  // If no band matches (shouldn't happen with proper table), use default
  return availablePay * 0.4; // 40% is the default maximum
}

/**
 * Get Council Tax Attachment Order table for the given pay period
 */
function getCTAOTable(payPeriod: PayPeriodType): { lowerLimit: number; upperLimit: number | null; percentage: number }[] {
  // These tables are based on the Council Tax Attachment Order rates
  // They would typically be stored in the tax year config, but for simplicity we're defining them here
  
  switch (payPeriod) {
    case PayPeriodType.WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 75, percentage: 0 },
        { lowerLimit: 75, upperLimit: 135, percentage: 3 },
        { lowerLimit: 135, upperLimit: 185, percentage: 5 },
        { lowerLimit: 185, upperLimit: 225, percentage: 7 },
        { lowerLimit: 225, upperLimit: 355, percentage: 12 },
        { lowerLimit: 355, upperLimit: 505, percentage: 17 },
        { lowerLimit: 505, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.TWO_WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 150, percentage: 0 },
        { lowerLimit: 150, upperLimit: 270, percentage: 3 },
        { lowerLimit: 270, upperLimit: 370, percentage: 5 },
        { lowerLimit: 370, upperLimit: 450, percentage: 7 },
        { lowerLimit: 450, upperLimit: 710, percentage: 12 },
        { lowerLimit: 710, upperLimit: 1010, percentage: 17 },
        { lowerLimit: 1010, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.FOUR_WEEKLY:
      return [
        { lowerLimit: 0, upperLimit: 300, percentage: 0 },
        { lowerLimit: 300, upperLimit: 540, percentage: 3 },
        { lowerLimit: 540, upperLimit: 740, percentage: 5 },
        { lowerLimit: 740, upperLimit: 900, percentage: 7 },
        { lowerLimit: 900, upperLimit: 1420, percentage: 12 },
        { lowerLimit: 1420, upperLimit: 2020, percentage: 17 },
        { lowerLimit: 2020, upperLimit: null, percentage: 40 }
      ];
      
    case PayPeriodType.MONTHLY:
      return [
        { lowerLimit: 0, upperLimit: 325, percentage: 0 },
        { lowerLimit: 325, upperLimit: 585, percentage: 3 },
        { lowerLimit: 585, upperLimit: 805, percentage: 5 },
        { lowerLimit: 805, upperLimit: 975, percentage: 7 },
        { lowerLimit: 975, upperLimit: 1540, percentage: 12 },
        { lowerLimit: 1540, upperLimit: 2190, percentage: 17 },
        { lowerLimit: 2190, upperLimit: null, percentage: 40 }
      ];
      
    default:
      throw new Error(`Unsupported pay period for CTAO table: ${payPeriod}`);
  }
}

/**
 * Calculate Direct Earnings Attachment (DEA)
 */
function calculateDEA(
  input: AttachmentOrderCalculationInput,
  availablePay: number,
  result: AttachmentOrderCalculationResult
): number {
  const {
    calculationMethod,
    fixedAmount,
    percentage,
    deaRateType = DEARateType.STANDARD
  } = input;

  switch (calculationMethod) {
    case AttachmentOrderCalculationMethod.FIXED_AMOUNT:
      if (fixedAmount === undefined) {
        throw new Error('Fixed amount is required for FIXED_AMOUNT calculation method');
      }
      return fixedAmount;

    case AttachmentOrderCalculationMethod.PERCENTAGE:
      if (percentage === undefined) {
        throw new Error('Percentage is required for PERCENTAGE calculation method');
      }
      return availablePay * (percentage / 100);

    case AttachmentOrderCalculationMethod.TABLE_BASED:
      // DEAs use a table-based calculation with standard and higher rates
      return calculateDEATableBased(input, availablePay, deaRateType, result);

    default:
      throw new Error(`Unsupported calculation method for DEA: ${calculationMethod}`);
  }
}

/**
 * Calculate DEA using table-based method
 */
function calculateDEATableBased(
  input: AttachmentOrderCalculationInput,
  availablePay: number,
  rateType: DEARateType,
  result: AttachmentOrderCalculationResult
): number {
  const { payPeriod } = input;
  
  // Get the appropriate DEA table based on pay period and rate type
  const deaTable = getDEATable(payPeriod, rateType);
  
  // Find the appropriate band for the available pay
  for (let i = 0; i < deaTable.length; i++) {
    const band = deaTable[i];
    if (availablePay <= (band.upperLimit ?? Number.MAX_VALUE)) {
      // Store the band and rate used
      result.deaBandUsed = `${band.lowerLimit}-${band.upperLimit ?? 'unlimited'}`;
      result.deaRateUsed = band.percentage;
      
      // Calculate deduction based on band percentage
      return availablePay * (band.percentage / 100);
    }
  }
  
  // If no band matches (shouldn't happen with proper table), use default
  return availablePay * 0.4; // 40% is the default maximum
}

/**
 * Get Direct Earnings Attachment table for the given pay period and rate type
 */
function getDEATable(
  payPeriod: PayPeriodType, 
  rateType: DEARateType
): { lowerLimit: number; upperLimit: number | null; percentage: number }[] {
  // These tables are based on the DWP DEA rates
  // They would typically be stored in the tax year config, but for simplicity we're defining them here
  
  if (rateType === DEARateType.STANDARD) {
    // Standard rate tables
    switch (payPeriod) {
      case PayPeriodType.WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 100, percentage: 0 },
          { lowerLimit: 100, upperLimit: 160, percentage: 3 },
          { lowerLimit: 160, upperLimit: 220, percentage: 5 },
          { lowerLimit: 220, upperLimit: 270, percentage: 7 },
          { lowerLimit: 270, upperLimit: 375, percentage: 11 },
          { lowerLimit: 375, upperLimit: 520, percentage: 15 },
          { lowerLimit: 520, upperLimit: null, percentage: 20 }
        ];
        
      case PayPeriodType.TWO_WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 200, percentage: 0 },
          { lowerLimit: 200, upperLimit: 320, percentage: 3 },
          { lowerLimit: 320, upperLimit: 440, percentage: 5 },
          { lowerLimit: 440, upperLimit: 540, percentage: 7 },
          { lowerLimit: 540, upperLimit: 750, percentage: 11 },
          { lowerLimit: 750, upperLimit: 1040, percentage: 15 },
          { lowerLimit: 1040, upperLimit: null, percentage: 20 }
        ];
        
      case PayPeriodType.FOUR_WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 400, percentage: 0 },
          { lowerLimit: 400, upperLimit: 640, percentage: 3 },
          { lowerLimit: 640, upperLimit: 880, percentage: 5 },
          { lowerLimit: 880, upperLimit: 1080, percentage: 7 },
          { lowerLimit: 1080, upperLimit: 1500, percentage: 11 },
          { lowerLimit: 1500, upperLimit: 2080, percentage: 15 },
          { lowerLimit: 2080, upperLimit: null, percentage: 20 }
        ];
        
      case PayPeriodType.MONTHLY:
        return [
          { lowerLimit: 0, upperLimit: 430, percentage: 0 },
          { lowerLimit: 430, upperLimit: 690, percentage: 3 },
          { lowerLimit: 690, upperLimit: 950, percentage: 5 },
          { lowerLimit: 950, upperLimit: 1160, percentage: 7 },
          { lowerLimit: 1160, upperLimit: 1615, percentage: 11 },
          { lowerLimit: 1615, upperLimit: 2240, percentage: 15 },
          { lowerLimit: 2240, upperLimit: null, percentage: 20 }
        ];
        
      default:
        throw new Error(`Unsupported pay period for DEA standard rate table: ${payPeriod}`);
    }
  } else {
    // Higher rate tables
    switch (payPeriod) {
      case PayPeriodType.WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 100, percentage: 0 },
          { lowerLimit: 100, upperLimit: 160, percentage: 5 },
          { lowerLimit: 160, upperLimit: 220, percentage: 10 },
          { lowerLimit: 220, upperLimit: 270, percentage: 15 },
          { lowerLimit: 270, upperLimit: 375, percentage: 20 },
          { lowerLimit: 375, upperLimit: 520, percentage: 25 },
          { lowerLimit: 520, upperLimit: null, percentage: 30 }
        ];
        
      case PayPeriodType.TWO_WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 200, percentage: 0 },
          { lowerLimit: 200, upperLimit: 320, percentage: 5 },
          { lowerLimit: 320, upperLimit: 440, percentage: 10 },
          { lowerLimit: 440, upperLimit: 540, percentage: 15 },
          { lowerLimit: 540, upperLimit: 750, percentage: 20 },
          { lowerLimit: 750, upperLimit: 1040, percentage: 25 },
          { lowerLimit: 1040, upperLimit: null, percentage: 30 }
        ];
        
      case PayPeriodType.FOUR_WEEKLY:
        return [
          { lowerLimit: 0, upperLimit: 400, percentage: 0 },
          { lowerLimit: 400, upperLimit: 640, percentage: 5 },
          { lowerLimit: 640, upperLimit: 880, percentage: 10 },
          { lowerLimit: 880, upperLimit: 1080, percentage: 15 },
          { lowerLimit: 1080, upperLimit: 1500, percentage: 20 },
          { lowerLimit: 1500, upperLimit: 2080, percentage: 25 },
          { lowerLimit: 2080, upperLimit: null, percentage: 30 }
        ];
        
      case PayPeriodType.MONTHLY:
        return [
          { lowerLimit: 0, upperLimit: 430, percentage: 0 },
          { lowerLimit: 430, upperLimit: 690, percentage: 5 },
          { lowerLimit: 690, upperLimit: 950, percentage: 10 },
          { lowerLimit: 950, upperLimit: 1160, percentage: 15 },
          { lowerLimit: 1160, upperLimit: 1615, percentage: 20 },
          { lowerLimit: 1615, upperLimit: 2240, percentage: 25 },
          { lowerLimit: 2240, upperLimit: null, percentage: 30 }
        ];
        
      default:
        throw new Error(`Unsupported pay period for DEA higher rate table: ${payPeriod}`);
    }
  }
}

/**
 * Calculate Child Maintenance Service (CMS) deduction
 */
function calculateCMS(
  input: AttachmentOrderCalculationInput,
  availablePay: number,
  result: AttachmentOrderCalculationResult
): number {
  const {
    calculationMethod,
    fixedAmount,
    percentage,
    cmsRate,
    cmsCollectionFee = 20 // Default collection fee is 20%
  } = input;

  let deduction = 0;

  switch (calculationMethod) {
    case AttachmentOrderCalculationMethod.FIXED_AMOUNT:
      if (fixedAmount === undefined) {
        throw new Error('Fixed amount is required for FIXED_AMOUNT calculation method');
      }
      deduction = fixedAmount;
      break;

    case AttachmentOrderCalculationMethod.PERCENTAGE:
      if (percentage === undefined && cmsRate === undefined) {
        throw new Error('Percentage or CMS rate is required for PERCENTAGE calculation method');
      }
      const rateToUse = cmsRate ?? percentage ?? 0;
      deduction = availablePay * (rateToUse / 100);
      break;

    case AttachmentOrderCalculationMethod.TABLE_BASED:
      // CMS uses a table-based calculation based on the number of children
      deduction = calculateCMSTableBased(input, availablePay);
      break;

    default:
      throw new Error(`Unsupported calculation method for CMS: ${calculationMethod}`);
  }

  // Calculate collection fee
  const collectionFeeAmount = deduction * (cmsCollectionFee / 100);
  result.collectionFeeAmount = Math.round(collectionFeeAmount * 100) / 100;
  
  // Calculate amount sent to CMS
  result.amountSentToCMS = Math.round((deduction - collectionFeeAmount) * 100) / 100;

  return deduction;
}

/**
 * Calculate CMS using table-based method
 */
function calculateCMSTableBased(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  // This is a simplified implementation
  // In a real system, this would use the CMS calculation tables based on
  // number of children, income bands, etc.
  
  // For now, we'll use a simple percentage-based approach
  return availablePay * 0.15; // 15% is a common rate for one child
}

/**
 * Calculate Student Loan Attachment Order (SLAO)
 */
function calculateSLAO(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const {
    calculationMethod,
    fixedAmount,
    percentage,
    studentLoanPlanType
  } = input;

  switch (calculationMethod) {
    case AttachmentOrderCalculationMethod.FIXED_AMOUNT:
      if (fixedAmount === undefined) {
        throw new Error('Fixed amount is required for FIXED_AMOUNT calculation method');
      }
      return fixedAmount;

    case AttachmentOrderCalculationMethod.PERCENTAGE:
      if (percentage === undefined) {
        throw new Error('Percentage is required for PERCENTAGE calculation method');
      }
      return availablePay * (percentage / 100);

    case AttachmentOrderCalculationMethod.TABLE_BASED:
      // SLAOs typically use a fixed percentage based on the plan type
      return calculateSLAOTableBased(input, availablePay);

    default:
      throw new Error(`Unsupported calculation method for SLAO: ${calculationMethod}`);
  }
}

/**
 * Calculate SLAO using table-based method
 */
function calculateSLAOTableBased(
  input: AttachmentOrderCalculationInput,
  availablePay: number
): number {
  const { studentLoanPlanType } = input;
  
  // Determine percentage based on plan type
  let percentage = 9; // Default for Plan 1 and 2
  
  if (studentLoanPlanType === 'plan4') {
    percentage = 9; // Plan 4 (Scotland)
  } else if (studentLoanPlanType === 'postgraduate') {
    percentage = 6; // Postgraduate Loan
  }
  
  return availablePay * (percentage / 100);
}
