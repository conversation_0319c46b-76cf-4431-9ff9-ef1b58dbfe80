"use client";

import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { NumberInput } from "@/components/ui/number-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Trash2 } from "lucide-react";
import { Employee } from "@/lib/schemas/employee";

interface OtherSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
}

const OtherSection: React.FC<OtherSectionProps> = ({ employee, onChange }) => {
  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    onChange("other", field, value);
  };

  // Function to handle emergency contact changes
  const handleEmergencyContactChange = (index: number, field: string, value: string) => {
    const newContacts = [...(employee.emergencyContacts || [])];
    newContacts[index] = { ...newContacts[index], [field]: value };
    handleChange("emergencyContacts", newContacts);
  };

  // Function to add emergency contact
  const addEmergencyContact = () => {
    const newContacts = [...(employee.emergencyContacts || []), { name: "", relationship: "", phone: "" }];
    handleChange("emergencyContacts", newContacts);
  };

  // Function to remove emergency contact
  const removeEmergencyContact = (index: number) => {
    const newContacts = [...(employee.emergencyContacts || [])];
    newContacts.splice(index, 1);
    handleChange("emergencyContacts", newContacts);
  };

  return (
    <div className="space-y-1 pt-4">
      {/* Nationality */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="nationality" className="text-right font-medium col-span-2 justify-self-end mx-4">Nationality</Label>
        <div className="col-span-3">
          <Input
            id="nationality"
            className="text-sm w-full"
            placeholder="Nationality"
            value={employee.nationality || ""}
            onChange={(e) => handleChange("nationality", e.target.value)}
          />
        </div>
      </div>

      {/* Passport Number */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="passportNumber" className="text-right font-medium col-span-2 justify-self-end mx-4">Passport</Label>
        <div className="col-span-3">
          <Input
            id="passportNumber"
            className="text-sm w-full"
            placeholder="Passport number"
            value={employee.passportNumber || ""}
            onChange={(e) => handleChange("passportNumber", e.target.value)}
          />
        </div>
      </div>

      {/* Marital Status */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="maritalStatus" className="text-right font-medium col-span-2 justify-self-end mx-4">Marital status</Label>
        <div className="col-span-3">
          <Select 
            value={employee.maritalStatus || ""}
            onValueChange={(value) => handleChange("maritalStatus", value)}
          >
            <SelectTrigger id="maritalStatus" className="text-sm">
              <SelectValue placeholder="Select marital status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Single">Single</SelectItem>
              <SelectItem value="Married">Married</SelectItem>
              <SelectItem value="Civil Partnership">Civil Partnership</SelectItem>
              <SelectItem value="Separated">Separated</SelectItem>
              <SelectItem value="Divorced">Divorced</SelectItem>
              <SelectItem value="Widowed">Widowed</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Job Title */}
      <div className="grid grid-cols-10 gap-2 items-center pt-6">
        <Label htmlFor="jobTitle" className="text-right font-medium col-span-2 justify-self-end mx-4">Job title</Label>
        <div className="col-span-3">
          <Input
            id="jobTitle"
            className="text-sm w-full"
            placeholder="Job title"
            value={employee.jobTitle || ""}
            onChange={(e) => handleChange("jobTitle", e.target.value)}
          />
        </div>
      </div>

      {/* Starting Salary */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="startingSalary" className="text-right font-medium col-span-2 justify-self-end mx-4">Salary</Label>
        <div className="col-span-2">
          <div className="flex">
            <span className="flex items-center justify-center border rounded-l-md bg-muted px-3 text-sm">£</span>
            <NumberInput
              id="startingSalary"
              className="text-sm rounded-l-none"
              placeholder="0.00"
              decimalPlaces={2}
              useThousandSeparator={true}
              value={employee.startingSalary }
              onChange={(value) => handleChange("startingSalary", value ?? 0)}

            />
          </div>
        </div>
      </div>

      {/* Next Review Date */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="nextReviewDate" className="text-right font-medium col-span-2 justify-self-end mx-4">Review date</Label>
        <div className="col-span-3">
          <Input
            id="nextReviewDate"
            type="date"
            className="text-sm w-full"
            value={employee.nextReviewDate || ""}
            onChange={(e) => handleChange("nextReviewDate", e.target.value)}
          />
        </div>
      </div>

      {/* Medical Information */}
      <div className="grid grid-cols-10 gap-2 items-start pt-6">
        <Label htmlFor="medical" className="text-right font-medium pt-1 col-span-2 justify-self-end mx-4">Medical</Label>
        <div className="col-span-6">
          <Textarea
            id="medical"
            className="text-sm resize-none min-h-[80px]"
            placeholder="Medical information"
            value={employee.medical || ""}
            onChange={(e) => handleChange("medical", e.target.value)}
          />
        </div>
      </div>

      {/* Notes */}
      <div className="grid grid-cols-10 gap-2 items-start">
        <Label htmlFor="notes" className="text-right font-medium pt-1 col-span-2 justify-self-end mx-4">Notes</Label>
        <div className="col-span-6">
          <Textarea
            id="notes"
            className="text-sm resize-none min-h-[80px]"
            placeholder="Additional notes"
            value={employee.notes || ""}
            onChange={(e) => handleChange("notes", e.target.value)}
          />
        </div>
      </div>

      {/* Confidentiality */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Access</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="isConfidential" 
              checked={employee.isConfidential}
              onCheckedChange={(checked) => handleChange("isConfidential", Boolean(checked))}
            />
            <Label htmlFor="isConfidential" className="text-sm">
              Mark as confidential (only accessible to administrators)
            </Label>
          </div>
        </div>
      </div>

      {/* Enable Self Service */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Self service</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="enableSelfService" 
              checked={employee.enableSelfService}
              onCheckedChange={(checked) => handleChange("enableSelfService", Boolean(checked))}
            />
            <Label htmlFor="enableSelfService" className="text-sm">
              Enable employee portal access (employee self-service)
            </Label>
          </div>
        </div>
      </div>

      {/* Emergency Contacts */}
      <div className="grid grid-cols-10 gap-2 items-start pt-6">
        <Label className="text-right font-medium pt-1 col-span-2 justify-self-end mx-4">Emergency</Label>
        <div className="col-span-8 space-y-3">
          {(employee.emergencyContacts || []).map((contact, index) => (
            <div key={index} className="grid grid-cols-6 gap-2 items-center bg-muted/30 rounded-md p-3">
              <div className="col-span-2">
                <Label htmlFor={`contact-${index}-name`} className="text-xs mb-1 block">Name</Label>
                <Input
                  id={`contact-${index}-name`}
                  className="text-sm"
                  placeholder="Contact name"
                  value={contact.name}
                  onChange={(e) => handleEmergencyContactChange(index, "name", e.target.value)}
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor={`contact-${index}-relationship`} className="text-xs mb-1 block">Relationship</Label>
                <Input
                  id={`contact-${index}-relationship`}
                  className="text-sm"
                  placeholder="Relationship"
                  value={contact.relationship}
                  onChange={(e) => handleEmergencyContactChange(index, "relationship", e.target.value)}
                />
              </div>
              <div className="col-span-1">
                <Label htmlFor={`contact-${index}-phone`} className="text-xs mb-1 block">Phone</Label>
                <Input
                  id={`contact-${index}-phone`}
                  className="text-sm"
                  placeholder="Phone number"
                  value={contact.phone}
                  onChange={(e) => handleEmergencyContactChange(index, "phone", e.target.value)}
                />
              </div>
              <div className="col-span-1 flex items-end justify-end">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8 text-destructive"
                  onClick={() => removeEmergencyContact(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
          <Button 
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={addEmergencyContact}
          >
            <Plus className="h-3 w-3 mr-1" /> Add emergency contact
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OtherSection;
