"use client";

import React from "react";
import Link from "next/link";
import { useNavigationStore } from "@/store/navigation-store";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/providers/theme-provider";
import { Sun, Moon, User, Settings, HelpCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import EmployerSwitcherMenu from "@/components/employer/EmployerSwitcherMenu";

// Navigation items - these could be moved to a configuration file later
const navItems = [
  { label: "PAYROLL", section: "payroll" },
  { label: "RTI", section: "rti" },
  { label: "REPORTS", section: "reports" },
  { label: "PENSIONS", section: "pensions" },
  { label: "EMPLOYEES", section: "employees" },
  { label: "EMPLOYER", section: "employer" },
  { label: "HMRC PAYMENTS", section: "hmrc" },
  { label: "CIS", section: "cis" },
];

export function MainNavbar() {
  const { theme, setTheme } = useTheme();
  const globalSection = useNavigationStore((s) => s.globalSection);
  const employerSections = useNavigationStore((s) => s.employerSections);
  const activeEmployerId = useNavigationStore((s) => s.activeEmployerId);
  const setGlobalSection = useNavigationStore((s) => s.setGlobalSection);
  const setActiveEmployer = useNavigationStore((s) => s.setActiveEmployer);

  return (
    <header className="bg-background sticky top-0 z-50 w-full pt-4">
      <div className="flex h-8 w-full items-center px-4">
        {/* Logo & Brand */}
        <div className="mr-4 flex items-center">
          <Link
            href="/"
            className={cn(
              "flex items-center space-x-2 text-xl font-bold",
              globalSection === "dashboard"
                ? "text-pink-600"
                : "text-foreground",
            )}
            onClick={(e) => {
              e.preventDefault();
              setGlobalSection("dashboard");
              // Do NOT clear active employer here; dashboard should not deactivate it
            }}
          >
            Dashboard
          </Link>
          <EmployerSwitcherMenu />
        </div>

        {/* Main Navigation */}
        <nav className="flex flex-1 items-center justify-center">
          <ul className="flex space-x-2">
            {/* Show employer section buttons whenever an employer is open */}
            {activeEmployerId &&
              navItems.map((item) => (
                <li key={item.section}>
                  <button
                    type="button"
                    className={cn(
                      "rounded-md px-2 py-2 text-sm font-normal tracking-wide transition-colors hover:bg-sky-300 hover:text-white dark:hover:bg-zinc-600",
                      employerSections[activeEmployerId] === item.section &&
                        globalSection !== "dashboard"
                        ? "bg-sky-400 text-white hover:bg-sky-300 dark:border-zinc-500 dark:bg-zinc-600"
                        : "text-slate-700 dark:border-zinc-500 dark:text-slate-200",
                    )}
                    onClick={() => {
                      useNavigationStore
                        .getState()
                        .setEmployerSection(
                          activeEmployerId,
                          item.section as import("@/store/navigation-store").SectionId,
                        );
                      if (
                        useNavigationStore.getState().globalSection ===
                        "dashboard"
                      ) {
                        useNavigationStore
                          .getState()
                          .setGlobalSection(undefined as any);
                      }
                    }}
                  >
                    {item.label}
                  </button>
                </li>
              ))}
          </ul>
        </nav>

        {/* Right side controls - Theme toggle & User */}
        <div className="flex space-x-1">
          {/* Theme Switch */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="rounded-full"
            aria-label="Toggle theme"
          >
            {theme === "dark" ? <Sun size={18} /> : <Moon size={18} />}
          </Button>

          {/* Help button */}
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            aria-label="Help"
          >
            <HelpCircle size={18} />
          </Button>

          {/* User dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full"
                aria-label="User menu"
              >
                <User size={18} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Link href="/profile" className="flex w-full">
                  Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/settings" className="flex w-full">
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <button className="flex w-full">Sign out</button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
