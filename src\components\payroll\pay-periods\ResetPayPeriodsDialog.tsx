import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog";

interface ScheduleOption {
  id: string;
  label: string;
  type?: string;
}

interface ResetPayPeriodsDialogProps {
  open: boolean;
  schedules: ScheduleOption[];
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onReset: (scheduleId: string) => void;
}

import React, { useState } from "react";

export function ResetPayPeriodsDialog({
  open,
  schedules = [],
  onOpenChange,
  onCancel,
  onReset
}: ResetPayPeriodsDialogProps) {
  const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(null);
  const [confirmStep, setConfirmStep] = useState(false);

  const safeSchedules = Array.isArray(schedules) ? schedules : [];
  const selectedSchedule = safeSchedules.find(s => s.id === selectedScheduleId);

  const handleResetClick = () => {
    setConfirmStep(true);
  };

  const handleFinalConfirm = () => {
    if (selectedScheduleId) {
      onReset(selectedScheduleId);
      setConfirmStep(false);
      setSelectedScheduleId(null);
    }
  };

  const handleCancel = () => {
    setConfirmStep(false);
    setSelectedScheduleId(null);
    onCancel();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-red-600">Reset Pay Periods?</DialogTitle>
          <DialogDescription>
            Select the pay period schedule to reset. This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-2 mb-4">
          {safeSchedules.length === 0 && (
            <span className="italic text-muted-foreground">No pay schedules available.</span>
          )}
          {safeSchedules.map(schedule => (
            <label
              key={schedule.id}
              className={
                "flex items-center gap-2 p-2 rounded cursor-pointer " +
                (selectedScheduleId === schedule.id ? "bg-red-50 border border-red-300" : "hover:bg-muted")
              }
            >
              <input
                type="radio"
                name="schedule"
                value={schedule.id}
                checked={selectedScheduleId === schedule.id}
                onChange={() => setSelectedScheduleId(schedule.id)}
                className="accent-red-600"
              />
              <span className="font-medium">{schedule.label}</span>
              <span className="ml-2 text-xs text-muted-foreground">{schedule.type || "Unknown"}</span>
            </label>
          ))}
        </div>
        {selectedSchedule && !confirmStep && (
          <div className="block mt-2">
            <span className="font-medium">Period Type:</span> {selectedSchedule.type || <span className="italic text-muted-foreground">Unknown</span>}<br />
            <span className="font-medium">Schedule Label:</span> {selectedSchedule.label || <span className="italic text-muted-foreground">Unnamed</span>}
          </div>
        )}
        <span className="block mt-4 text-sm text-muted-foreground">
          This action cannot be undone. All pay period data for this period will be permanently deleted.
        </span>
        {confirmStep && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 rounded text-red-800">
            <strong>Are you sure? Last chance!</strong> This will reset <span className="font-semibold">all data</span> for the <span className="font-semibold">{selectedSchedule?.label}</span> schedule.
          </div>
        )}
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={handleCancel} className="border-2 border-sky-500">
            Cancel
          </Button>
          {!confirmStep && (
            <Button
              variant="destructive"
              onClick={handleResetClick}
              disabled={!selectedScheduleId}
              autoFocus
            >
              Reset Pay Periods
            </Button>
          )}
          {confirmStep && (
            <Button
              variant="destructive"
              onClick={handleFinalConfirm}
              autoFocus
            >
              Confirm Reset
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
