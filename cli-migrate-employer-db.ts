// cli-migrate-employer-db.ts
import * as fs from "fs";
import * as path from "path";
import sqlite3 from "sqlite3";
import { open } from "sqlite";

// Fixed employer DB directory
const EMPLOYER_DB_DIR = "A:/WEBSITES/A1 Payroll Software V2/uk-payroll/test-employers";
// Adjust this path to your migrations folder
const MIGRATIONS_DIR = path.resolve(process.cwd(), "dist-electron/migrations/employer");

async function getMigrationFiles(): Promise<string[]> {
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(f => f.endsWith(".sql"))
    .sort(); // Ensure migrations are applied in order
  return files;
}

async function ensureMigrationsTable(db: any) {
  await db.exec(`
    CREATE TABLE IF NOT EXISTS _drizzle_migrations (
      id TEXT PRIMARY KEY NOT NULL,
      hash TEXT,
      created_at INTEGER
    );
  `);
}

async function getAppliedMigrations(db: any): Promise<Set<string>> {
  const rows = await db.all("SELECT id FROM _drizzle_migrations");
  return new Set(rows.map((r: any) => r.id));
}

async function applyMigration(db: any, file: string, sql: string) {
  // Split on statement-breakpoint for drizzle-style migration splitting
  const statements = sql.split(/-->\s*statement-breakpoint/).map(s => s.trim()).filter(Boolean);
  for (const stmt of statements) {
    if (stmt) await db.exec(stmt);
  }
  await db.run(
    "INSERT INTO _drizzle_migrations (id, hash, created_at) VALUES (?, NULL, strftime('%s','now'))",
    file
  );
}

async function runMigrationsForDb(dbPath: string) {
  const db = await open({ filename: dbPath, driver: sqlite3.Database });
  await ensureMigrationsTable(db);

  const applied = await getAppliedMigrations(db);
  const files = await getMigrationFiles();

  for (const file of files) {
    if (!applied.has(file)) {
      const fullPath = path.join(MIGRATIONS_DIR, file);
      const sql = fs.readFileSync(fullPath, "utf-8");
      console.log(`Applying migration: ${file} to ${dbPath}`);
      await applyMigration(db, file, sql);
    } else {
      console.log(`Already applied: ${file} to ${dbPath}`);
    }
  }

  await db.close();
  console.log(`All migrations applied to ${dbPath}!`);
}

async function runMigrationsForAllEmployerDbs() {
  const files = fs.readdirSync(EMPLOYER_DB_DIR)
    .filter(f => f.endsWith(".ukpayroll"));
  if (files.length === 0) {
    console.log("No employer .ukpayroll files found in:", EMPLOYER_DB_DIR);
    return;
  }
  for (const file of files) {
    const dbPath = path.join(EMPLOYER_DB_DIR, file);
    try {
      await runMigrationsForDb(dbPath);
    } catch (err) {
      console.error(`Migration failed for ${dbPath}:`, err);
    }
  }
}

runMigrationsForAllEmployerDbs().catch(err => {
  console.error("Migration runner failed:", err);
  process.exit(1);
});
