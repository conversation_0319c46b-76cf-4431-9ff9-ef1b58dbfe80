import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Clock, Bell, Building } from "lucide-react";

export function AnalyticsTab() {
  return (
    <div className="space-y-4 p-2">
      <h2 className="text-xl font-semibold">Analytics</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Employers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              +1 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">76</div>
            <p className="text-xs text-muted-foreground">
              +5 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Payslips Finalised</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">153</div>
            <p className="text-xs text-muted-foreground">
              Current tax year
            </p>
          </CardContent>
        </Card>
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Active Pay Frequencies</CardTitle>
            <CardDescription>
              Distribution of employers by pay frequency.
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center">
            <div className="text-muted-foreground text-sm">
              Pay frequency chart will appear here
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>RTI Submissions</CardTitle>
            <CardDescription>
              RTI submissions status overview.
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center">
            <div className="text-muted-foreground text-sm">
              RTI submissions chart will appear here
            </div>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
          <CardDescription>Recent payroll activity across all employers.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex">
              <div className="mr-4 flex flex-col items-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                  <Clock className="h-5 w-5 text-blue-700 dark:text-blue-300" />
                </div>
                <div className="h-full w-[1px] bg-border" />
              </div>
              <div className="space-y-1 pt-1">
                <p className="text-sm font-medium">Payroll Processed</p>
                <p className="text-sm text-muted-foreground">ABC Company Ltd - Monthly Payroll</p>
                <p className="text-xs text-muted-foreground">Today at 10:23</p>
              </div>
            </div>
            <div className="flex">
              <div className="mr-4 flex flex-col items-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                  <Building className="h-5 w-5 text-green-700 dark:text-green-300" />
                </div>
                <div className="h-full w-[1px] bg-border" />
              </div>
              <div className="space-y-1 pt-1">
                <p className="text-sm font-medium">Employer Added</p>
                <p className="text-sm text-muted-foreground">Smith & Sons - New employer created</p>
                <p className="text-xs text-muted-foreground">Yesterday at 16:45</p>
              </div>
            </div>
            <div className="flex">
              <div className="mr-4 flex flex-col items-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900">
                  <Bell className="h-5 w-5 text-yellow-700 dark:text-yellow-300" />
                </div>
              </div>
              <div className="space-y-1 pt-1">
                <p className="text-sm font-medium">Tax Code Notice</p>
                <p className="text-sm text-muted-foreground">XYZ Enterprises - 2 new tax code notices</p>
                <p className="text-xs text-muted-foreground">23/03/2025</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
