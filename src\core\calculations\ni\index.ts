/**
 * National Insurance Calculation Module
 * 
 * This module exports functions for calculating UK National Insurance contributions
 * according to HMRC guidelines.
 */

import { calculateNationalInsurance } from './calculator';
import { NiCalculationInput, NiCalculationResult, NiBreakdown, PayPeriodType } from './types';

// Export the main calculation function
export { calculateNationalInsurance };

// Export types
export type { NiCalculationInput, NiCalculationResult, NiBreakdown, PayPeriodType };