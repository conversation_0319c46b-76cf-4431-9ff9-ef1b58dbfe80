"use client";

import React from "react";
import { ClickableCheckbox } from "@/components/ui/clickable-checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RefreshCw } from "lucide-react";

interface RepeatingNoteControlProps {
  id: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const RepeatingNoteControl: React.FC<RepeatingNoteControlProps> = ({
  id,
  isChecked,
  onChange,
  disabled = false,
  className = "",
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={className}>
          <ClickableCheckbox
            id={`repeating-note-${id}`}
            className="h-4 w-4 data-[state=checked]:border-emerald-600 data-[state=checked]:bg-emerald-400"
            checked={isChecked}
            onCheckedChange={(checked) => onChange(checked === true)}
            disabled={disabled}
            label={
              <RefreshCw
                className={`h-5 w-5 ${
                  isChecked && !disabled
                    ? "text-emerald-500 dark:text-emerald-300"
                    : "text-muted-foreground"
                }`}
              />
            }
            wrapperClassName="flex items-center"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Repeat on Future Payslips</p>
      </TooltipContent>
    </Tooltip>
  );
};
