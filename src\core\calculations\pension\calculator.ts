/**
 * Pension Calculator
 * 
 * This module provides functions for calculating UK pension contributions
 * according to HMRC guidelines. It supports all major UK pension schemes:
 * - Relief at Source
 * - Net Pay Arrangement
 * - Salary Sacrifice
 * 
 * It also handles auto-enrollment rules and qualifying earnings calculations.
 */

import { 
  PensionCalculationInput, 
  PensionCalculationResult, 
  PensionSchemeType,
  PayPeriodType
} from './types';

/**
 * Calculate pension contributions for the given input
 * 
 * @param input Pension calculation input parameters
 * @returns Pension calculation result with detailed breakdown
 */
export function calculatePension(
  input: PensionCalculationInput
): PensionCalculationResult {
  const { 
    grossPay, 
    payPeriod, 
    taxYearConfig, 
    periodNumber, 
    schemeType,
    employeeContribution: initialEmployeeContribution,
    employeeContributionIsPercentage,
    employerContribution: initialEmployerContribution,
    employerContributionIsPercentage,
    pensionableSalary = grossPay,
    previousPensionableEarningsInTaxYear = 0,
    previousEmployeeContributionsInTaxYear = 0,
    previousEmployerContributionsInTaxYear = 0,
    applyAutoEnrollment = false,
    hasOptedOut = false,
    employeeAge = 0,
    useQualifyingEarnings = false,
    employeeTaxRate = 0.2 // Default to basic rate
  } = input;

  // Create mutable copies of contribution rates/amounts
  let employeeContribution = initialEmployeeContribution;
  let employerContribution = initialEmployerContribution;

  // Initialize result
  const result: PensionCalculationResult = {
    employeeContribution: 0,
    employerContribution: 0,
    totalContribution: 0,
    pensionableEarnings: pensionableSalary,
    autoEnrollmentApplied: false,
    cumulativePensionableEarnings: previousPensionableEarningsInTaxYear + pensionableSalary,
    cumulativeEmployeeContribution: previousEmployeeContributionsInTaxYear,
    cumulativeEmployerContribution: previousEmployerContributionsInTaxYear,
    cumulativeTotalContribution: previousEmployeeContributionsInTaxYear + previousEmployerContributionsInTaxYear,
    schemeTypeUsed: schemeType
  };

  // Get pension config from tax year config
  const pensionConfig = taxYearConfig.pension;
  if (!pensionConfig) {
    throw new Error('Pension configuration not found in tax year config');
  }

  // Calculate period factor based on pay period
  const periodFactor = getPeriodFactor(payPeriod);

  // Calculate qualifying earnings if applicable
  let earningsForCalculation = pensionableSalary;
  if (useQualifyingEarnings) {
    const lowerThreshold = pensionConfig.lowerEarningsThreshold / periodFactor;
    const upperThreshold = pensionConfig.upperEarningsThreshold / periodFactor;
    
    // Qualifying earnings are between lower and upper thresholds
    const qualifyingEarnings = Math.min(
      Math.max(pensionableSalary - lowerThreshold, 0),
      upperThreshold - lowerThreshold
    );
    
    result.qualifyingEarnings = qualifyingEarnings;
    earningsForCalculation = qualifyingEarnings;
  }

  // Apply auto-enrollment rules if applicable
  if (applyAutoEnrollment && !hasOptedOut) {
    const isEligibleAge = employeeAge >= 22 && employeeAge < 66;
    const earningsTrigger = pensionConfig.earningsTrigger / periodFactor;
    const isEligibleEarnings = pensionableSalary >= earningsTrigger;
    
    if (isEligibleAge && isEligibleEarnings) {
      result.autoEnrollmentApplied = true;
      result.autoEnrollmentStatus = 'Eligible - auto enrolled';
      
      // Apply minimum contributions if current are lower
      if (employerContributionIsPercentage && 
          employerContribution < pensionConfig.minimumEmployerContribution) {
        // Employer contribution must be at least minimum
        employerContribution = pensionConfig.minimumEmployerContribution;
      }
      
      const totalMinimum = pensionConfig.minimumTotalContribution;
      if (employeeContributionIsPercentage && employerContributionIsPercentage) {
        // Ensure total contribution meets minimum
        if (employeeContribution + employerContribution < totalMinimum) {
          employeeContribution = totalMinimum - employerContribution;
        }
      }
    } else if (pensionableSalary >= pensionConfig.lowerEarningsThreshold / periodFactor) {
      // Non-eligible jobholder
      result.autoEnrollmentStatus = 'Non-eligible jobholder - can opt in';
    } else {
      // Entitled worker
      result.autoEnrollmentStatus = 'Entitled worker - can join';
    }
  }

  // Calculate employer contribution
  if (employerContributionIsPercentage) {
    result.employerContribution = Math.round(earningsForCalculation * employerContribution * 100) / 100;
  } else {
    result.employerContribution = employerContribution;
  }

  // Calculate employee contribution based on scheme type
  if (schemeType === PensionSchemeType.SALARY_SACRIFICE) {
    // For salary sacrifice, the employee contribution is already accounted for in reduced gross pay
    result.employeeContribution = 0;
  } else {
    // For relief at source and net pay, calculate based on percentage or fixed amount
    if (employeeContributionIsPercentage) {
      result.employeeContribution = Math.round(earningsForCalculation * employeeContribution * 100) / 100;
    } else {
      result.employeeContribution = employeeContribution;
    }

    // For relief at source, calculate tax relief
    if (schemeType === PensionSchemeType.RELIEF_AT_SOURCE) {
      // Basic rate tax relief is added by the pension provider
      result.taxRelief = Math.round(result.employeeContribution * 0.25 * 100) / 100;
    }
  }

  // Calculate total contribution
  result.totalContribution = result.employeeContribution + result.employerContribution;

  // Update cumulative values
  result.cumulativeEmployeeContribution += result.employeeContribution;
  result.cumulativeEmployerContribution += result.employerContribution;
  result.cumulativeTotalContribution += result.totalContribution;

  return result;
}

/**
 * Get the period factor for converting annual values to period values
 * 
 * @param payPeriod The pay period type
 * @returns The period factor (number of periods in a year)
 */
function getPeriodFactor(payPeriod: PayPeriodType): number {
  switch (payPeriod) {
    case PayPeriodType.WEEKLY:
      return 52;
    case PayPeriodType.TWO_WEEKLY:
      return 26;
    case PayPeriodType.FOUR_WEEKLY:
      return 13;
    case PayPeriodType.MONTHLY:
      return 12;
    case PayPeriodType.QUARTERLY:
      return 4;
    case PayPeriodType.BI_ANNUALLY:
      return 2;
    case PayPeriodType.ANNUALLY:
      return 1;
    default:
      throw new Error(`Unsupported pay period: ${payPeriod}`);
  }
}
