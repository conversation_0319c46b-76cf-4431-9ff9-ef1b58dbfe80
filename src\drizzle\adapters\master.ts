import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";
import path from "path";
import { app } from "electron";

let db: ReturnType<typeof drizzle> | null = null;
let client: ReturnType<typeof createClient> | null = null;

export function getMasterDb() {
  if (db) return db;

  const dbPath = path.join(app.getPath("userData"), "master.db");
  client = createClient({
    url: `file:${dbPath}`,
  });
  db = drizzle(client);
  return db;
}

export function getMasterDbClient() {
  return client;
}
