/**
 * Types for National Insurance calculations
 * These types define the structure of inputs and outputs for UK National Insurance calculations
 */

import { TaxYearConfig } from '../tax-years/types';

/**
 * Input data for National Insurance calculation
 */
export interface NiCalculationInput {
  // Gross pay for the period
  grossPay: number;
  
  // NI category letter (A, B, C, etc.)
  category: string;
  
  // Pay period type
  payPeriod: PayPeriodType;
  
  // Tax year configuration
  taxYearConfig: TaxYearConfig;
  
  // Week or month number in the tax year (1-based)
  periodNumber: number;
  
  // Previous earnings in this tax year (for annual maximum calculations)
  previousEarningsInTaxYear?: number;
  
  // Previous employee NI contributions in this tax year
  previousEmployeeNiInTaxYear?: number;
  
  // Previous employer NI contributions in this tax year
  previousEmployerNiInTaxYear?: number;
  
  // Whether this is a director's calculation (uses annual earnings period)
  isDirector?: boolean;
  
  // Employee age (for age-related calculations)
  employeeAge?: number;
  
  // Whether the employee is an apprentice
  isApprentice?: boolean;
  
  // Whether the employee is a veteran in first 12 months of civilian employment
  isVeteran?: boolean;
  
  // Whether the employee is in a freeport
  isInFreeport?: boolean;
  
  // Whether the employee is in an investment zone
  isInInvestmentZone?: boolean;
  
  // For pro-rata directors: number of weeks remaining in tax year
  weeksRemainingInTaxYear?: number;
  
  // Whether the employee is a mariner (0.5% rate reduction applies)
  isMariner?: boolean;
  
  // Termination award amount (Class 1A NICs apply on amount over £30,000)
  terminationAward?: number;
  
  // Sporting testimonial payment amount (Class 1A NICs apply on amount over £100,000)
  sportingTestimonial?: number;
}

/**
 * Result of National Insurance calculation
 */
export interface NiCalculationResult {
  // Employee NI contribution for the period
  employeeNi: number;
  
  // Employer NI contribution for the period
  employerNi: number;
  
  // Breakdown of employee NI by threshold
  employeeNiBreakdown: NiBreakdown[];
  
  // Breakdown of employer NI by threshold
  employerNiBreakdown: NiBreakdown[];
  
  // Earnings subject to NI
  earningsSubjectToNi: number;
  
  // Earnings above upper earnings limit
  earningsAboveUel: number;
  
  // Earnings between primary threshold and upper earnings limit
  earningsBetweenPtAndUel: number;
  
  // Earnings below primary threshold
  earningsBelowPt: number;
  
  // Cumulative values
  cumulativeGrossPay: number;
  cumulativeEmployeeNi: number;
  cumulativeEmployerNi: number;
  
  // NI category used for calculation
  categoryUsed: string;

  // Indicates if this was a directors' calculation
  isDirectorsCalculation?: boolean;
  
  // Class 1A NICs on termination awards (13.8% on amount over £30,000)
  class1aTerminationNi?: number;
  
  // Termination award amount subject to Class 1A NICs (amount over £30,000)
  terminationAwardSubjectToNi?: number;
  
  // Class 1A NICs on sporting testimonials (13.8% on amount over £100,000)
  class1aSportingTestimonialNi?: number;
  
  // Sporting testimonial amount subject to Class 1A NICs (amount over £100,000)
  sportingTestimonialSubjectToNi?: number;
}

/**
 * National Insurance breakdown by threshold
 */
export interface NiBreakdown {
  // Name of the threshold
  thresholdName: string;
  
  // Lower threshold
  lowerThreshold: number;
  
  // Upper threshold (undefined for the highest band)
  upperThreshold: number | undefined;
  
  // NI rate as a decimal
  rate: number;
  
  // Amount of earnings in this band
  amount: number;
  
  // NI contribution for this band
  contribution: number;
}

/**
 * Pay period types
 */
export enum PayPeriodType {
  WEEKLY = 'weekly',
  TWO_WEEKLY = 'two_weekly',
  FOUR_WEEKLY = 'four_weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BI_ANNUALLY = 'bi_annually',
  ANNUALLY = 'annually'
}
