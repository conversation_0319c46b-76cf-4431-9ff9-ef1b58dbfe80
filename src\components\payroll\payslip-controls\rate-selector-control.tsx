"use client";

import React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { ChevronDown, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { NumberInput } from "@/components/ui/number-input";

interface RateSelectorControlProps {
  id: string;
  currentRate: number;
  onChange: (rate: number, label?: string) => void;
  className?: string;
  /** Base rate for multipliers */
  standardRate?: number;
  /** Saved rates pulled from employee record */
  savedRates?: { label: string; value: number }[];
  /** Callback to persist a newly added saved rate */
  onSaveRate?: (label: string, value: number) => void;
}

export const RateSelectorControl: React.FC<RateSelectorControlProps> = ({
  id,
  currentRate,
  onChange,
  className = "",
  standardRate,
  savedRates,
  onSaveRate,
}) => {
  // Base rate for multipliers
  const standardRateValue = standardRate ?? 20.0;

  // State for custom rate input and popover control
  const [customRate, setCustomRate] = React.useState<string>("");
  const [parsedRateValue, setParsedRateValue] = React.useState<
    number | undefined
  >(undefined);
  const [customRateName, setCustomRateName] = React.useState<string>("");
  const [showCustomInput, setShowCustomInput] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [isCalculation, setIsCalculation] = React.useState(false);

  // Preset multipliers
  const presetMultipliers = [
    { label: "Standard", value: 1 },
    { label: "1.25×", value: 1.25 },
    { label: "1.5×", value: 1.5 },
    { label: "2×", value: 2 },
    { label: "3×", value: 3 },
  ];

  // Saved rates (from employee record or defaults), exclude base standard rate
  const defaultSaved = [
    { label: "Weekend", value: 25.0 },
    { label: "Holiday", value: 30.0 },
  ];
  const rawSaved = savedRates ?? defaultSaved;
  const savedRatesList = rawSaved.filter((r) => r.value !== standardRateValue);

  const handlePresetClick = (preset: { label: string; value: number }) => {
    // Pass both the rate value and the label
    const rateValue = standardRateValue * preset.value;
    onChange(rateValue, preset.label);
    setOpen(false); // Close popover after selection
  };

  const handleSavedRateClick = (savedRate: { label: string; value: number }) => {
    // Pass both the rate value and the label
    onChange(savedRate.value, savedRate.label);
    setOpen(false); // Close popover after selection
  };

  const handleCustomRateSubmit = () => {
    if (parsedRateValue && parsedRateValue > 0) {
      // Persist new saved rate
      onSaveRate?.(customRateName || "Custom", parsedRateValue);
      // Update the rate selection
      onChange(parsedRateValue, customRateName || "Custom");
      setCustomRate("");
      setParsedRateValue(undefined);
      setCustomRateName("");
      setIsCalculation(false);
      setShowCustomInput(false);
      setOpen(false); // Close popover after submission
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "ml-2 size-6 border-slate-400 hover:bg-slate-200",
            className,
          )}
        >
          <ChevronDown className="h-4 w-4 text-slate-500" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="b-2 w-80 border-1 border-zinc-500 p-3 shadow-md"
        align="end"
      >
        <div className="space-y-2">
          <div className="text-sm font-medium">Multipliers</div>
          <div className="grid grid-cols-2 gap-2">
            {presetMultipliers.map((preset) => (
              <Button
                key={preset.label}
                variant="outline"
                size="sm"
                className="h-9 w-full justify-between px-3 text-sm"
                onClick={() => handlePresetClick(preset)}
              >
                {preset.label}
                <span className="text-sm text-slate-500">
                  £{(standardRateValue * preset.value).toFixed(2)}
                </span>
              </Button>
            ))}
          </div>

          {savedRatesList.length > 0 && (
            <>
              <div className="pt-2 text-sm font-medium">Saved Rates</div>
              <div className="grid grid-cols-2 gap-2">
                {savedRatesList.map((rate) => (
                  <Button
                    key={rate.label}
                    variant="outline"
                    size="sm"
                    className="h-9 w-full justify-between px-3 text-sm"
                    onClick={() => handleSavedRateClick(rate)}
                  >
                    {rate.label}
                    <span className="text-muted-foreground text-sm">
                      £{rate.value.toFixed(2)}
                    </span>
                  </Button>
                ))}
              </div>
            </>
          )}

          <div className="pt-2">
            {showCustomInput ? (
              <div className="space-y-2">
                <div className="text-sm font-medium">New Rate</div>
                <div className="space-y-2">
                  <Input
                    value={customRateName}
                    onChange={(e) => setCustomRateName(e.target.value)}
                    className="h-9 bg-white"
                    placeholder="Name (optional)"
                  />
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <div className="absolute top-1/2 left-2 -translate-y-1/2 text-sm text-slate-500">
                        £
                      </div>
                      <NumberInput
                        id={`custom-rate-${id}`}
                        value={customRate ? parseFloat(customRate) : undefined}
                        onChange={(value) => {
                          setCustomRate(value ? value.toString() : "");
                          setParsedRateValue(value);
                          // Always reset calculation state when value changes
                          setIsCalculation(false);
                        }}
                        className="h-9 bg-white pl-6"
                        decimalPlaces={2}
                        allowCalculations={true}
                        min={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Tab') return;
                          // Check if input contains calculation operators
                          const inputValue = e.currentTarget.value;
                          const isCalc = /[+\-*/()]/.test(inputValue);
                          setIsCalculation(isCalc);

                          if (e.key === "Enter") {
                            if (isCalc) {
                              // If it's a calculation, wait for the calculation to complete
                              setTimeout(() => {
                                setIsCalculation(false);
                              }, 100);
                            } else {
                              handleCustomRateSubmit();
                            }
                          }
                        }}
                      />
                    </div>
                    <Button
                      className="h-9 px-4"
                      onClick={handleCustomRateSubmit}
                      disabled={
                        isCalculation ||
                        !parsedRateValue ||
                        parsedRateValue <= 0
                      }
                    >
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="h-9 w-full justify-start"
                onClick={() => setShowCustomInput(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add New Rate
              </Button>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
