/**
 * Tax Code Adjustments Handler
 * 
 * Implements section 15 of HMRC's PAYErout specification for handling
 * tax code suffix changes through P7X and P9X forms.
 * 
 * P7X: Current year changes
 * P9X: Changes from 6 April (new tax year)
 */

import { parseTaxCode, TaxCodeComponents } from './tax-code-parser';

/**
 * Types of tax code adjustment forms
 */
export enum AdjustmentFormType {
  P7X = 'P7X', // Current year adjustments
  P9X = 'P9X'  // New tax year adjustments
}

/**
 * Tax code adjustment instruction
 */
export interface TaxCodeAdjustmentInstruction {
  /** Type of adjustment form (P7X or P9X) */
  formType: AdjustmentFormType;
  
  /** Date from which the changes are to take effect */
  effectiveDate: Date;
  
  /** Tax year the adjustment applies to (e.g. 2023 for 2023-2024) */
  taxYear: number;
  
  /** Adjustments to make for each suffix */
  suffixAdjustments: {
    /** Suffix letter (L, M or N) */
    suffix: string;
    
    /** Amount to adjust the numeric part by (can be positive or negative) */
    adjustmentAmount: number;
  }[];
  
  /** Instructions for new employees starting between specified dates */
  newEmployeesInstructions?: string;
}

/**
 * Record of which tax codes have been adjusted in a given tax year
 * to prevent inadvertent second changes
 */
export interface TaxCodeAdjustmentRecord {
  /** Tax year of the adjustment */
  taxYear: number;
  
  /** Form type used for the adjustment */
  formType: AdjustmentFormType;
  
  /** Date the adjustment was applied */
  dateApplied: Date;
  
  /** List of employee IDs whose tax codes were adjusted */
  adjustedEmployeeIds: string[];
}

/**
 * Employee tax code information needed for adjustments
 */
export interface EmployeeTaxCodeInfo {
  /** Unique employee ID */
  employeeId: string;
  
  /** Current tax code */
  taxCode: string;
  
  /** Whether employee is on Week 1/Month 1 basis */
  isNonCumulative: boolean;
  
  /** Date employee started */
  startDate: Date;
}

/**
 * Result of a tax code adjustment
 */
export interface TaxCodeAdjustmentResult {
  /** Employee ID */
  employeeId: string;
  
  /** Original tax code */
  originalTaxCode: string;
  
  /** New tax code after adjustment */
  newTaxCode: string;
  
  /** Whether a refund may be due */
  refundMayBeDue: boolean;
}

/**
 * Apply tax code adjustments based on P7X/P9X form instructions
 * 
 * @param employees List of employee tax code information
 * @param adjustment The adjustment instructions
 * @param adjustmentRecords Previous adjustment records to prevent duplicate adjustments
 * @returns List of adjustment results
 */
export function applyTaxCodeAdjustments(
  employees: EmployeeTaxCodeInfo[],
  adjustment: TaxCodeAdjustmentInstruction,
  adjustmentRecords: TaxCodeAdjustmentRecord[]
): TaxCodeAdjustmentResult[] {
  const results: TaxCodeAdjustmentResult[] = [];
  const currentDate = new Date();
  
  // Check if the adjustment should be applied (based on effective date)
  if (currentDate < adjustment.effectiveDate) {
    return results; // Skip if before effective date
  }
  
  // Check previous adjustments to prevent duplicate adjustments
  const previousAdjustments = adjustmentRecords.filter(
    record => record.taxYear === adjustment.taxYear && 
              record.formType === adjustment.formType
  );
  
  // Create a set of already adjusted employee IDs
  const adjustedEmployeeIds = new Set<string>();
  previousAdjustments.forEach(record => {
    record.adjustedEmployeeIds.forEach(id => adjustedEmployeeIds.add(id));
  });
  
  // Process each employee
  employees.forEach(employee => {
    // Skip if already adjusted
    if (adjustedEmployeeIds.has(employee.employeeId)) {
      return;
    }
    
    // Get tax code components
    const taxCodeComponents = parseTaxCode(employee.taxCode);
    
    // Check if employee is a new starter during relevant period for P9X
    const isNewEmployee = adjustment.formType === AdjustmentFormType.P9X && 
                         isNewEmployeeForP9X(employee, adjustment);
    
    // Skip if prefix code or suffix T (these require individual P6/P9 forms per spec 15.5)
    if (isPrefixCode(employee.taxCode) || taxCodeComponents.suffix === 'T') {
      return;
    }
    
    // Only process employees with adjustable suffix codes
    if (!isNewEmployee && !isSuffixAdjustable(taxCodeComponents, adjustment)) {
      return;
    }
    
    // Apply the adjustment
    const result = adjustTaxCode(employee, taxCodeComponents, adjustment);
    if (result) {
      results.push(result);
      adjustedEmployeeIds.add(employee.employeeId);
    }
  });
  
  return results;
}

/**
 * Check if an employee is considered a new starter for P9X purposes
 */
function isNewEmployeeForP9X(
  employee: EmployeeTaxCodeInfo,
  adjustment: TaxCodeAdjustmentInstruction
): boolean {
  // P9X typically has instructions for new employees starting between certain dates
  // This would need business-specific implementation based on the newEmployeesInstructions
  // For now, we'll return false as this requires parsing the text instructions
  return false;
}

/**
 * Check if a tax code is a prefix code
 */
function isPrefixCode(taxCode: string): boolean {
  // Prefix codes include K-codes but exclude S and C prefixes
  // (Scottish and Welsh tax codes, which follow normal suffix rules)
  const normalizedCode = taxCode.toUpperCase().trim();
  const isKCode = normalizedCode.startsWith('K');
  const isScottishCode = normalizedCode.startsWith('S');
  const isWelshCode = normalizedCode.startsWith('C');
  
  return isKCode && !isScottishCode && !isWelshCode;
}

/**
 * Check if the suffix is adjustable under the current instruction
 */
function isSuffixAdjustable(
  taxCodeComponents: TaxCodeComponents,
  adjustment: TaxCodeAdjustmentInstruction
): boolean {
  // Get the applicable suffixes from the adjustment
  const applicableSuffixes = adjustment.suffixAdjustments.map(adj => adj.suffix);
  
  // Check if the employee's suffix is in the list of adjustable suffixes
  return applicableSuffixes.includes(taxCodeComponents.suffix);
}

/**
 * Adjust the tax code based on the adjustment instruction
 */
function adjustTaxCode(
  employee: EmployeeTaxCodeInfo,
  taxCodeComponents: TaxCodeComponents,
  adjustment: TaxCodeAdjustmentInstruction
): TaxCodeAdjustmentResult | null {
  // Find the adjustment for this suffix
  const suffixAdjustment = adjustment.suffixAdjustments.find(
    adj => adj.suffix === taxCodeComponents.suffix
  );
  
  if (!suffixAdjustment) {
    return null;
  }
  
  // Calculate new numeric value
  const newNumericValue = taxCodeComponents.numericValue + suffixAdjustment.adjustmentAmount;
  
  // Construct new tax code
  let newTaxCode = '';
  
  // Add Scottish or Welsh prefix if present
  if (taxCodeComponents.isScottishCode) {
    newTaxCode += 'S';
  } else if (taxCodeComponents.isWelshCode) {
    newTaxCode += 'C';
  }
  
  // Add K prefix if present
  if (taxCodeComponents.isKCode) {
    newTaxCode += 'K';
  }
  
  // Add adjusted numeric value and suffix
  newTaxCode += `${newNumericValue}${taxCodeComponents.suffix}`;
  
  // Add non-cumulative indicator if present (for P7X only; P9X sets all to cumulative per spec 15.6)
  if (adjustment.formType === AdjustmentFormType.P7X && taxCodeComponents.isNonCumulative) {
    if (taxCodeComponents.isEmergencyCode) {
      newTaxCode += ' X'; // Emergency non-cumulative
    } else {
      newTaxCode += ' W1/M1'; // Regular non-cumulative
    }
  }
  
  // Determine if refund may be due per spec 15.4
  // - Refunds possible for P7X only
  // - No refunds for Week 1/Month 1 basis
  // - Only for uplifting of codes (positive adjustment)
  const refundMayBeDue = adjustment.formType === AdjustmentFormType.P7X &&
                          !employee.isNonCumulative &&
                          suffixAdjustment.adjustmentAmount > 0;
  
  return {
    employeeId: employee.employeeId,
    originalTaxCode: employee.taxCode,
    newTaxCode,
    refundMayBeDue
  };
}

/**
 * Reset non-cumulative indicators for new tax year per spec 15.6
 * "All suffix codes, including codes NT, are to be set to operate on
 * a cumulative basis when they are carried forward to a new tax year."
 * 
 * @param taxCode The tax code to reset
 * @returns Tax code with non-cumulative indicators removed
 */
export function resetTaxCodeForNewTaxYear(taxCode: string): string {
  const components = parseTaxCode(taxCode);
  
  // Remove non-cumulative indicators
  if (components.isNonCumulative) {
    let newTaxCode = '';
    
    // Add Scottish or Welsh prefix if present
    if (components.isScottishCode) {
      newTaxCode += 'S';
    } else if (components.isWelshCode) {
      newTaxCode += 'C';
    }
    
    // Add K prefix if present
    if (components.isKCode) {
      newTaxCode += 'K';
    }
    
    // Add numeric value and suffix
    if (components.suffix === 'BR' || components.suffix === 'D0' || 
        components.suffix === 'D1' || components.suffix === 'NT') {
      newTaxCode += components.suffix;
    } else {
      newTaxCode += `${components.numericValue}${components.suffix}`;
    }
    
    return newTaxCode;
  }
  
  // Return original code if not non-cumulative
  return taxCode;
}

/**
 * Create an adjustment record to prevent inadvertent second changes
 * for the same Income Tax year (per spec 15.2)
 * 
 * @param adjustment The adjustment instruction that was applied
 * @param adjustedEmployeeIds IDs of employees whose tax codes were adjusted
 * @returns Adjustment record for future reference
 */
export function createAdjustmentRecord(
  adjustment: TaxCodeAdjustmentInstruction,
  adjustedEmployeeIds: string[]
): TaxCodeAdjustmentRecord {
  return {
    taxYear: adjustment.taxYear,
    formType: adjustment.formType,
    dateApplied: new Date(),
    adjustedEmployeeIds
  };
}
