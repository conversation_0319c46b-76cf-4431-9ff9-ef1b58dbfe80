"use client";

import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { NumberInput } from "@/components/ui/number-input";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Trash2 } from "lucide-react";
import { Employee } from "@/lib/schemas/employee";

interface PaymentSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
}

const PaymentSection: React.FC<PaymentSectionProps> = ({
  employee,
  onChange,
}) => {
  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    onChange("payment", field, value);
  };

  // Function to format sort code in XX-XX-XX pattern
  const formatSortCode = (input: string): string => {
    // Remove all non-digit characters
    const digitsOnly = input.replace(/\D/g, "");

    // Don't add dashes if we have less than 2 digits
    if (digitsOnly.length < 2) return digitsOnly;

    // Don't add second dash if we have less than 4 digits
    if (digitsOnly.length < 4) {
      return `${digitsOnly.slice(0, 2)}-${digitsOnly.slice(2)}`;
    }

    // Format as XX-XX-XX, truncate if more than 6 digits
    const firstPair = digitsOnly.slice(0, 2);
    const secondPair = digitsOnly.slice(2, 4);
    const thirdPair = digitsOnly.slice(4, 6);

    return `${firstPair}-${secondPair}${thirdPair ? "-" + thirdPair : ""}`;
  };

  // Handle sort code changes with formatting
  const handleSortCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    // We allow the user to type with or without dashes
    // but we'll format it properly when we save the value
    const formatted = formatSortCode(rawValue);

    handleChange("paymentDetails", {
      ...employee.paymentDetails,
      sortCode: formatted,
    });
  };

  // Function to handle hourly rate changes
  const handleHourlyRateChange = (index: number, field: string, value: any) => {
    const newHourlyRates = [...(employee.hourlyRates || [])];
    newHourlyRates[index] = { ...newHourlyRates[index], [field]: value };
    handleChange("hourlyRates", newHourlyRates);
  };

  // Function to add a new hourly rate
  const addHourlyRate = () => {
    const newHourlyRates = [
      ...(employee.hourlyRates || []),
      { name: "", rate: 0 },
    ];
    handleChange("hourlyRates", newHourlyRates);
  };

  // Function to remove an hourly rate
  const removeHourlyRate = (index: number) => {
    // Don't allow removing the Standard hourly rate
    if (employee.hourlyRates?.[index].name === "Standard hourly rate") {
      return;
    }

    const newHourlyRates = [...(employee.hourlyRates || [])];
    newHourlyRates.splice(index, 1);
    handleChange("hourlyRates", newHourlyRates);
  };

  // Function to handle daily rate changes
  const handleDailyRateChange = (index: number, field: string, value: any) => {
    const newDailyRates = [...(employee.dailyRates || [])];
    newDailyRates[index] = { ...newDailyRates[index], [field]: value };
    handleChange("dailyRates", newDailyRates);
  };

  // Function to add a new daily rate
  const addDailyRate = () => {
    const newDailyRates = [
      ...(employee.dailyRates || []),
      { name: "", rate: 0 },
    ];
    handleChange("dailyRates", newDailyRates);
  };

  // Function to remove a daily rate
  const removeDailyRate = (index: number) => {
    // Don't allow removing the Standard daily rate
    if (employee.dailyRates?.[index].name === "Standard daily rate") {
      return;
    }

    const newDailyRates = [...(employee.dailyRates || [])];
    newDailyRates.splice(index, 1);
    handleChange("dailyRates", newDailyRates);
  };

  // Calculate conversions between period pay rate and annual salary
  const calculateEquivalentPay = (
    sourceField: "periodPayRate" | "annualSalary",
    value: number | undefined,
  ) => {
    // If the value is undefined or the payment frequency is not set, don't calculate
    if (value === undefined || !employee.paymentFrequency) {
      return;
    }

    // Get the appropriate multiplier based on payment frequency
    const getMultiplier = () => {
      switch (employee.paymentFrequency) {
        case "Weekly":
          return 52;
        case "2-Weekly":
          return 26;
        case "4-Weekly":
          return 13;
        case "Monthly":
          return 12;
        case "Quarterly":
          return 4;
        case "Yearly":
          return 1;
        default:
          return 0;
      }
    };

    const multiplier = getMultiplier();

    // Don't continue if we don't have a valid multiplier
    if (multiplier === 0) {
      return;
    }

    // Calculate the other field
    if (sourceField === "periodPayRate") {
      // Calculate annual salary from period pay rate
      const annualSalary = value * multiplier;
      handleChange("annualSalary", annualSalary);
    } else {
      // Calculate period pay rate from annual salary
      const periodPayRate = value / multiplier;
      handleChange("periodPayRate", periodPayRate);
    }
  };

  // Enhanced handle change function for pay rate fields
  const handlePayChange = (
    field: "periodPayRate" | "annualSalary",
    value: number | undefined,
  ) => {
    // First update the field
    handleChange(field, value);

    // Then calculate the equivalent in the other field
    if (value !== undefined) {
      calculateEquivalentPay(field, value);
    } else {
      // If the value is undefined (cleared), clear the other field too
      if (field === "periodPayRate") {
        handleChange("annualSalary", undefined);
      } else {
        handleChange("periodPayRate", undefined);
      }
    }
  };

  // Recalculate when payment frequency changes
  useEffect(
    () => {
      // Always keep annual salary constant and recalculate period pay rate
      if (employee.annualSalary !== undefined) {
        calculateEquivalentPay("annualSalary", employee.annualSalary);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [employee.paymentFrequency],
  );

  // Ensure we always have a standard hourly and daily rate
  useEffect(
    () => {
      const hasStandardHourlyRate = employee.hourlyRates?.some(
        (rate) => rate.name === "Standard hourly rate",
      );
      if (!hasStandardHourlyRate) {
        const newHourlyRates = [
          ...(employee.hourlyRates || []),
          { name: "Standard hourly rate", rate: 0 },
        ];
        handleChange("hourlyRates", newHourlyRates);
      }

      const hasStandardDailyRate = employee.dailyRates?.some(
        (rate) => rate.name === "Standard daily rate",
      );
      if (!hasStandardDailyRate) {
        const newDailyRates = [
          ...(employee.dailyRates || []),
          { name: "Standard daily rate", rate: 0 },
        ];
        handleChange("dailyRates", newDailyRates);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [employee],
  );

  return (
    <div className="space-y-1 pt-4">
      {/* Payment Frequency */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label
          htmlFor="paymentFrequency"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          When is employee paid?
        </Label>
        <div className="col-span-4">
          <Select
            value={employee.paymentFrequency}
            onValueChange={(value) => handleChange("paymentFrequency", value)}
          >
            <SelectTrigger id="paymentFrequency" className="text-sm">
              <SelectValue placeholder="Select payment frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Weekly">Weekly</SelectItem>
              <SelectItem value="2-Weekly">2-Weekly</SelectItem>
              <SelectItem value="4-Weekly">4-Weekly</SelectItem>
              <SelectItem value="Monthly">Monthly</SelectItem>
              <SelectItem value="Quarterly">Quarterly</SelectItem>
              <SelectItem value="Annually">Annually</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pay Calculation Method */}
      <div className="grid grid-cols-10 items-center gap-2">
        <Label
          htmlFor="payCalculationMethod"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          How is pay worked out?
        </Label>
        <div className="col-span-4">
          <Select
            value={employee.payCalculationMethod || ""}
            onValueChange={(value) =>
              handleChange("payCalculationMethod", value)
            }
          >
            <SelectTrigger id="payCalculationMethod" className="text-sm">
              <SelectValue placeholder="Select calculation method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Salary">Salary</SelectItem>
              <SelectItem value="Hourly">Hourly rate</SelectItem>
              <SelectItem value="Daily">Daily rate</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Weekly rate / Annual salary */}
      <div className="grid grid-cols-10 items-center gap-2 pt-4">
        <div className="col-span-2 mx-4 justify-self-end text-right">
          <Label htmlFor="periodPayRate" className="font-medium">
            {employee.paymentFrequency === "Weekly" && "Weekly rate"}
            {employee.paymentFrequency === "2-Weekly" && "2-Weekly rate"}
            {employee.paymentFrequency === "4-Weekly" && "4-Weekly rate"}
            {employee.paymentFrequency === "Monthly" && "Monthly rate"}
            {employee.paymentFrequency === "Quarterly" && "Quarterly rate"}
            {employee.paymentFrequency === "Yearly" && "Annual rate"}
            {!employee.paymentFrequency && "Payment rate"}
          </Label>
        </div>
        <div className="col-span-2">
          <div className="flex">
            <span className="bg-muted flex items-center justify-center rounded-l-md border px-3 text-sm">
              £
            </span>
            <NumberInput
              id="periodPayAmount"
              className="rounded-l-none text-sm"
              placeholder="0.00"
              decimalPlaces={2}
              useThousandSeparator={true}
              value={employee.periodPayRate ?? undefined}
              onChange={(value) => handlePayChange("periodPayRate", value)}
            />
          </div>
        </div>

        <div className="col-span-1 justify-self-end text-right">
          <Label htmlFor="annualSalary" className="font-medium">
            Annual salary
          </Label>
        </div>
        <div className="col-span-2">
          <div className="flex">
            <span className="bg-muted flex items-center justify-center rounded-l-md border px-3 text-sm">
              £
            </span>
            <NumberInput
              id="annualSalary"
              className="rounded-l-none text-sm"
              placeholder="0.00"
              decimalPlaces={2}
              useThousandSeparator={true}
              value={employee.annualSalary ?? undefined}
              onChange={(value) => handlePayChange("annualSalary", value)}
            />
          </div>
        </div>
      </div>

      {/* Hourly Rates */}
      <div className="grid grid-cols-10 items-start gap-2 pt-4">
        <Label className="col-span-2 mx-4 justify-self-end pt-1 text-right font-medium">
          Hourly rate(s)
        </Label>
        <div className="col-span-6 space-y-1">
          {(employee.hourlyRates || []).map((rate, index) => {
            // Check if this is the standard rate (should be the first item if it exists)
            const isStandardRate = rate.name === "Standard hourly rate";

            return (
              <div key={index} className="grid grid-cols-8 items-center gap-2">
                <div className="col-span-3">
                  <Input
                    type="text"
                    className={`text-sm ${isStandardRate ? "bg-slate-200" : ""}`}
                    placeholder="Rate name"
                    value={rate.name}
                    onChange={(e) =>
                      handleHourlyRateChange(index, "name", e.target.value)
                    }
                    readOnly={isStandardRate}
                  />
                </div>
                <div className="col-span-2">
                  <div className="flex">
                    <span className="bg-muted flex items-center justify-center rounded-l-md border px-3 text-sm">
                      £
                    </span>
                    <NumberInput
                      className="rounded-l-none text-sm"
                      placeholder="0.00"
                      min={0}
                      decimalPlaces={2}
                      useThousandSeparator={true}
                      value={rate.rate ?? undefined}
                      onChange={(value) =>
                        handleHourlyRateChange(index, "rate", value)
                      }
                    />
                  </div>
                </div>
                {isStandardRate ? (
                  <div className="col-span-1 flex justify-end">
                    <Button
                      variant="default"
                      size="sm"
                      className="h-8 w-10 text-sm font-normal"
                      onClick={addHourlyRate}
                    >
                      <Plus className="size-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="col-span-1 flex justify-end">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive h-8 w-8"
                      onClick={() => removeHourlyRate(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Daily Rates */}
      <div className="grid grid-cols-10 items-start gap-2 pt-2">
        <Label className="col-span-2 mx-4 justify-self-end pt-1 text-right font-medium">
          Daily rate(s)
        </Label>
        <div className="col-span-6 space-y-3">
          {(employee.dailyRates || []).map((rate, index) => {
            // Check if this is the standard rate (should be the first item if it exists)
            const isStandardRate = rate.name === "Standard daily rate";

            return (
              <div key={index} className="grid grid-cols-8 items-center gap-2">
                <div className="col-span-3">
                  <Input
                    type="text"
                    className={`text-sm ${isStandardRate ? "bg-slate-200" : ""}`}
                    placeholder="Rate name"
                    value={rate.name}
                    onChange={(e) =>
                      handleDailyRateChange(index, "name", e.target.value)
                    }
                    readOnly={isStandardRate}
                  />
                </div>
                <div className="col-span-2">
                  <div className="flex">
                    <span className="bg-muted flex items-center justify-center rounded-l-md border px-3 text-sm">
                      £
                    </span>
                    <NumberInput
                      className="rounded-l-none text-sm"
                      placeholder="0.00"
                      min={0}
                      decimalPlaces={2}
                      useThousandSeparator={true}
                      value={rate.rate ?? undefined}
                      onChange={(value) =>
                        handleDailyRateChange(index, "rate", value)
                      }
                    />
                  </div>
                </div>
                {isStandardRate ? (
                  <div className="col-span-1 flex justify-end">
                    <Button
                      variant="default"
                      size="sm"
                      className="h-8 w-10 text-sm font-normal"
                      onClick={addDailyRate}
                    >
                      <Plus className="size-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="col-span-1 flex justify-end">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive h-8 w-8"
                      onClick={() => removeDailyRate(index)}
                    >
                      <Trash2 className="size-4" />
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Payment Method */}
      <div className="grid grid-cols-10 items-center gap-2 pt-8">
        <Label className="col-span-2 mx-4 justify-self-end text-right font-medium">
          Payment method
        </Label>
        <div className="col-span-8 flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="bankTransfer"
              checked={employee.paymentMethod === "Bank transfer"}
              onCheckedChange={() => {
                handleChange("paymentMethod", "Bank transfer");
              }}
            />
            <Label
              htmlFor="bankTransfer"
              className="cursor-pointer text-sm font-normal"
            >
              Bank transfer
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="cheque"
              checked={employee.paymentMethod === "Cheque"}
              onCheckedChange={() => {
                handleChange("paymentMethod", "Cheque");
              }}
            />
            <Label
              htmlFor="cheque"
              className="cursor-pointer text-sm font-normal"
            >
              Cheque
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="cash"
              checked={employee.paymentMethod === "Cash"}
              onCheckedChange={() => {
                handleChange("paymentMethod", "Cash");
              }}
            />
            <Label
              htmlFor="cash"
              className="cursor-pointer text-sm font-normal"
            >
              Cash
            </Label>
          </div>
        </div>
      </div>

      {/* Bank Details - now showing regardless of payment method */}
      {/* Bank Name */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="bankName"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Bank name
        </Label>
        <div className="col-span-4">
          <Input
            id="bankName"
            className="text-sm"
            placeholder="Bank name"
            value={employee.paymentDetails?.bankName || ""}
            onChange={(e) => {
              handleChange("paymentDetails", {
                ...employee.paymentDetails,
                bankName: e.target.value,
              });
            }}
          />
        </div>
      </div>

      {/* Sort Code */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="sortCode"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Bank sort code
        </Label>
        <div className="col-span-2">
          <Input
            id="sortCode"
            className="text-sm"
            placeholder="XX-XX-XX"
            value={formatSortCode(employee.paymentDetails?.sortCode || "")}
            onChange={handleSortCodeChange}
          />
        </div>
      </div>

      {/* Account Number */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="accountNumber"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Bank account number
        </Label>
        <div className="col-span-2">
          <Input
            id="accountNumber"
            autoComplete="organization"
            className="text-sm"
            placeholder="Account number"
            value={employee.paymentDetails?.accountNumber || ""}
            onChange={(e) => {
              handleChange("paymentDetails", {
                ...employee.paymentDetails,
                accountNumber: e.target.value,
              });
            }}
          />
        </div>
      </div>

      {/* Account Name */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="accountName"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Bank account name
        </Label>
        <div className="col-span-4">
          <Input
            id="accountName"
            className="text-sm"
            placeholder="Account name"
            value={employee.paymentDetails?.accountName || ""}
            onChange={(e) => {
              handleChange("paymentDetails", {
                ...employee.paymentDetails,
                accountName: e.target.value,
              });
            }}
          />
        </div>
      </div>

      {/* Bank Reference */}
      <div className="grid grid-cols-10 items-center gap-2 pt-2">
        <Label
          htmlFor="bankReference"
          className="col-span-2 mx-4 justify-self-end text-right font-medium"
        >
          Bank reference
        </Label>
        <div className="col-span-4">
          <Input
            id="bankReference"
            className="text-sm"
            placeholder="payment reference"
            value={employee.paymentDetails?.reference || ""}
            onChange={(e) => {
              handleChange("paymentDetails", {
                ...employee.paymentDetails,
                reference: e.target.value,
              });
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default PaymentSection;
