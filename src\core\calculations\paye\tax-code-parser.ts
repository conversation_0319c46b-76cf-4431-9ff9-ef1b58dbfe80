/**
 * UK Tax Code Parser
 * 
 * Implements parsing of UK tax codes according to HMRC's PAYErout specification.
 * Handles standard codes, K codes, Scottish codes, Welsh codes, and special codes.
 * 
 * @see https://www.gov.uk/government/publications/rates-and-allowances-tax-codes
 */

/**
 * Tax code components interface
 */

import { PayPeriodType } from './types';
export interface TaxCodeComponents {
    /** Numeric part of the tax code (e.g., 1257 in "1257L") */
    numericValue: number;
    
    /** Suffix or letter component (e.g., "L" in "1257L") */
    suffix: string;
    
    /** Whether this is a K code (negative allowance) */
    isKCode: boolean;
    
    /** Whether this is a Scottish tax code (S prefix) */
    isScottishCode: boolean;
    
    /** Whether this is a Welsh tax code (C prefix) */
    isWelshCode: boolean;
    
    /** Whether this is using non-cumulative basis (Week 1/Month 1) */
    isNonCumulative: boolean;
    
    /** Whether this is an emergency tax code */
    isEmergencyCode: boolean;
    
    /** The original tax code string */
    rawCode: string;
  }
  
  /**
   * Parse a UK tax code and extract its components
   * @param code The tax code to parse (e.g., "1257L", "K500", "S1257L", "SK500", "1257L X")
   * @returns The parsed components of the tax code
   */
  export function parseTaxCode(code: string): TaxCodeComponents {
    // Normalize the code by removing spaces and converting to uppercase
    const normalizedCode = code.replace(/\s+/g, '').toUpperCase();
    
    // Initialize result with default values
    const result: TaxCodeComponents = {
      numericValue: 0,
      suffix: '',
      isKCode: false,
      isScottishCode: false,
      isWelshCode: false,
      isNonCumulative: false,
      isEmergencyCode: false,
      rawCode: normalizedCode
    };
    
    // Handle empty code
    if (!normalizedCode) {
      return result;
    }
    
    // Process the code, handling regional prefixes first
    let processedCode = normalizedCode;
    
    // Check for Scottish prefix
    if (processedCode.startsWith('S')) {
      result.isScottishCode = true;
      processedCode = processedCode.substring(1);
    }
    // Check for Welsh prefix
    else if (processedCode.startsWith('C')) {
      result.isWelshCode = true;
      processedCode = processedCode.substring(1);
    }
    
    // Now handle the code without regional prefix
    return parseCodeWithoutPrefix(processedCode, result);
  }
  
  /**
   * Parse a tax code after removing any regional prefix
   * @param code The tax code without S or C prefix
   * @param result The partially populated result object
   * @returns The fully populated tax code components
   */
  function parseCodeWithoutPrefix(code: string, result: TaxCodeComponents): TaxCodeComponents {
    // Handle special codes first
    if (code === 'NT') {
      result.suffix = 'NT';
      return result;
    }
    
    if (code === 'BR' || code === 'D0' || code === 'D1') {
      result.suffix = code;
      return result;
    }
    
    // Check for non-cumulative indicator
    const nonCumulativeRegex = /([XW1M])$/;
    const nonCumulativeMatch = code.match(nonCumulativeRegex);
    
    if (nonCumulativeMatch) {
      result.isNonCumulative = true;
      // X and M also indicate emergency tax codes
      if (nonCumulativeMatch[1] === 'X' || nonCumulativeMatch[1] === 'M') {
        result.isEmergencyCode = true;
        // Emergency codes must ALWAYS be treated as non-cumulative (Week 1/Month 1)
        // per Section 12 of PAYErout specification
        result.isNonCumulative = true;
      }
      // Remove the non-cumulative indicator for further processing
      code = code.replace(nonCumulativeRegex, '');
    }
    
    // Check for K code
    if (code.startsWith('K')) {
      result.isKCode = true;
      code = code.substring(1);
    }
    
    // Extract numeric part and suffix
    const numericMatch = code.match(/^(\d+)([A-Z0-9]*)$/);
    
    if (numericMatch) {
      result.numericValue = parseInt(numericMatch[1], 10);
      result.suffix = numericMatch[2] || '';
    } else {
      // Handle codes that don't fit the pattern
      // For example, 0T should have numericValue=0 and suffix=T
      const otherMatch = code.match(/^([A-Z]*)(\d*)([A-Z]*)$/);
      if (otherMatch) {
        const prefix = otherMatch[1] || '';
        const numeric = otherMatch[2] || '0';
        const suffix = otherMatch[3] || '';
        
        result.numericValue = parseInt(numeric, 10);
        result.suffix = prefix + suffix;
      }
    }
    
    return result;
  }
  
  /**
   * Calculate the annual free pay allowance based on a tax code
   * Implements the calculation according to HMRC PAYErout specification
   * @param components The parsed tax code components
   * @returns The annual free pay allowance (can be negative for K codes)
   */
  export function calculateTaxFreeAllowance(components: TaxCodeComponents): number {
    // Handle special tax codes
    if (components.suffix === 'BR' || components.suffix === 'D0' || 
        components.suffix === 'D1' || components.suffix === 'NT') {
      return 0;
    }
    
    // Handle K codes (negative allowance)
    if (components.isKCode) {
      return -(components.numericValue * 10);
    }
    
    // Calculate standard code allowance per HMRC rules
    if (components.numericValue === 0) {
      return 0;
    } else if (components.numericValue <= 500) {
      // For codes 1-500, allowance is (numeric part × £10) + £9
      return (components.numericValue * 10) + 9;
    } else {
      // For codes >500 (PAYErout 4.3.1c)
      let numericValue = components.numericValue;
      let totalAnnual = 0;
      
      // Implementation of spec note for codes >500
      numericValue -= 1;
      const quotient = Math.floor(numericValue / 500);
      const remainder = numericValue % 500 + 1;  // Add back the 1 we subtracted
      
      // Calculate remainder portion (1-500)
      const remainderAnnual = (remainder * 10) + 9;
      
      // Calculate quotient portion (multiples of 500)
      const quotientAnnual = quotient * 500 * 10; // £10 per unit for 500 units
      
      totalAnnual = remainderAnnual + quotientAnnual;
      
      return totalAnnual;
    }
  }
  
  /**
   * Calculate the periodic value of an annual amount
   * @param annualAmount The annual amount to calculate the periodic value for
   * @param payPeriod The pay period type (e.g., weekly, monthly)
   * @param periodNumber The period number (default is 1)
   * @param isCumulative Whether this is a cumulative calculation (default is false)
   * @returns The periodic value of the annual amount
   */
  export function calculatePeriodValue(
    annualAmount: number,
    payPeriod: PayPeriodType,
    periodNumber: number = 1,
    isCumulative: boolean = false
  ): number {
    // Get period factor first
    const periodFactor = getPeriodFactor(payPeriod);

    // Store intermediate calculation with 4 decimal precision (PAYErout Section 4.3.1c)
    const intermediateValue = parseFloat((annualAmount / periodFactor).toFixed(4));
    
    // SPEC ROUNDING RULE: Round UP to nearest penny if not exact (PAYErout Section 4.3.1c)
    const periodicValue = Math.ceil(intermediateValue * 100) / 100;

    // For cumulative calculations, multiply by period number AFTER rounding
    // But only if explicitly told this is for a cumulative calculation
    if (isCumulative) {
      return periodicValue * periodNumber;
    }
    
    // Otherwise just return the single period value
    return periodicValue;
  }

  // Helper function for period factors (extracted from calculator.ts)
  function getPeriodFactor(period: PayPeriodType): number {
    switch (period) {
      case PayPeriodType.WEEKLY: return 52;
      case PayPeriodType.TWO_WEEKLY: return 26;
      case PayPeriodType.FOUR_WEEKLY: return 13;
      case PayPeriodType.MONTHLY: return 12;
      case PayPeriodType.QUARTERLY: return 4;
      case PayPeriodType.ANNUALLY: return 1;
      default: throw new Error(`Invalid pay period: ${period}`);
    }
  }