/**
 * Statutory Payments Calculator
 * 
 * This module provides functions for calculating UK statutory payments
 * according to HMRC guidelines. It supports all UK statutory payments:
 * - Statutory Sick Pay (SSP)
 * - Statutory Maternity Pay (SMP)
 * - Statutory Paternity Pay (SPP)
 * - Statutory Adoption Pay (SAP)
 * - Statutory Parental Bereavement Pay (SPBP)
 * - Shared Parental Pay (ShPP)
 */

import { 
  StatutoryPaymentCalculationInput, 
  StatutoryPaymentCalculationResult, 
  StatutoryPaymentType,
  PayPeriodType
} from './types';
import { isStatutoryPaymentTypeAvailable } from './metadata';
import { StatutoryConfig, StatutoryPaymentConfig, TaxYearConfig } from '../tax-years/types';

/**
 * Calculate statutory payment for the given input
 */
export function calculateStatutoryPayment(
  input: StatutoryPaymentCalculationInput,
  taxYearConfig: TaxYearConfig
): StatutoryPaymentCalculationResult {
  // Initialize result
  const result: StatutoryPaymentCalculationResult = {
    isEligible: true,
    payment: 0,
    warnings: []
  };

  // Validate input
  if (!input.paymentType) {
    throw new Error('Payment type is required');
  }

  // Check if payment type is available
  if (!isStatutoryPaymentTypeAvailable(input.paymentType)) {
    result.isEligible = false;
    result.ineligibilityReason = `Payment type ${input.paymentType} is not available`;
    return result;
  }

  // Get statutory payments config from tax year config
  const statutoryConfig: StatutoryConfig = taxYearConfig.statutory;
  if (!statutoryConfig) {
    throw new Error('Statutory payments configuration not found in tax year config');
  }

  // Handle different payment types
  switch (input.paymentType) {
    case StatutoryPaymentType.SSP:
      return calculateSSP(input, result, statutoryConfig);
    case StatutoryPaymentType.SMP:
      return calculateSMP(input, result, statutoryConfig);
    case StatutoryPaymentType.SPP:
      return calculateSPP(input, result, statutoryConfig);
    case StatutoryPaymentType.SAP:
      return calculateSAP(input, result, statutoryConfig);
    case StatutoryPaymentType.SPBP:
      return calculateSPBP(input, result, statutoryConfig);
    case StatutoryPaymentType.SHPP:
      return calculateShPP(input, result, statutoryConfig);
    default:
      throw new Error(`Unsupported statutory payment type: ${input.paymentType}`);
  }
}

/**
 * Get the payment configuration for the specified statutory payment type
 */
function getPaymentConfig(
  paymentType: StatutoryPaymentType,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentConfig {
  switch (paymentType) {
    case StatutoryPaymentType.SSP:
      return statutoryConfig.ssp || {};
    case StatutoryPaymentType.SMP:
      return statutoryConfig.smp || {};
    case StatutoryPaymentType.SPP:
      return statutoryConfig.spp || {};
    case StatutoryPaymentType.SAP:
      return statutoryConfig.sap || {};
    case StatutoryPaymentType.SPBP:
      return statutoryConfig.spbp || {};
    case StatutoryPaymentType.SHPP:
      return statutoryConfig.shpp || {};
    default:
      throw new Error(`Unsupported statutory payment type: ${paymentType}`);
  }
}

/**
 * Get the lower earnings limit for the specified statutory payment type
 */
function getLowerEarningsLimit(
  paymentType: StatutoryPaymentType,
  statutoryConfig: StatutoryConfig
): number {
  const config = getPaymentConfig(paymentType, statutoryConfig);
  return config.lowerEarningsLimit || 0;
}

/**
 * Calculate the number of qualifying days
 */
function calculateQualifyingDays(qualifyingDays: Date[] | number, excludedDays?: Date[] | number): number {
  if (typeof qualifyingDays === 'number') {
    return qualifyingDays;
  }
  
  if (Array.isArray(qualifyingDays)) {
    return qualifyingDays.length;
  }
  
  return 0;
}

/**
 * Calculate Statutory Sick Pay (SSP)
 */
function calculateSSP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get SSP configuration
  const sspConfig = statutoryConfig.ssp || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = sspConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get weekly rate from config or use default
  const weeklyRate = sspConfig.weeklyRate || 0;
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Determine waiting days
  const waitingDays = sspConfig.waitingDays || 3;
  const waitingDaysServed = input.waitingDaysServed || 0;
  const waitingDaysInPeriod = Math.max(0, Math.min(waitingDays - waitingDaysServed, qualifyingDaysCount));
  const totalWaitingDaysServed = waitingDaysServed + waitingDaysInPeriod;
  
  // Calculate paid days
  const paidDays = Math.max(0, qualifyingDaysCount - waitingDaysInPeriod);
  
  // Calculate payment
  const payment = paidDays * dailyRate;
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = paidDays;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.waitingDaysApplied = waitingDaysInPeriod > 0;
  result.waitingDaysInPeriod = waitingDaysInPeriod;
  result.totalWaitingDaysServed = totalWaitingDaysServed;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.daysPaid = paidDays;
  
  // Calculate maximum weeks
  const maxWeeks = sspConfig.maxWeeks || 28;
  result.remainingEntitlementWeeks = maxWeeks - (input.weeksToCalculate || 0);
  
  return result;
}

/**
 * Calculate Statutory Maternity Pay (SMP)
 */
function calculateSMP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get SMP configuration
  const smpConfig = statutoryConfig.smp || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = smpConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get payment week (default to 1 if not provided)
  const paymentWeek = input.paymentWeek || 1;
  
  // Get higher rate weeks and standard rate weeks from config or use defaults
  const higherRateWeeks = smpConfig.higherRateWeeks || 6;
  const standardRateWeeks = smpConfig.standardRateWeeks || 33;
  const totalWeeks = higherRateWeeks + standardRateWeeks;
  
  // Check if in higher rate period
  const inHigherRatePeriod = paymentWeek <= higherRateWeeks;
  
  // Calculate rates
  const higherRate = smpConfig.higherRatePercentage ? input.averageWeeklyEarnings * (smpConfig.higherRatePercentage / 100) : 0;
  const standardRate = Math.min(smpConfig.weeklyRate || 0, input.averageWeeklyEarnings * 0.9);
  
  // Determine weekly rate based on period
  const weeklyRate = inHigherRatePeriod ? higherRate : standardRate;
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Calculate payment
  const payment = dailyRate * qualifyingDaysCount;
  
  // Calculate remaining weeks
  const weeksCalculated = input.weeksToCalculate || 0;
  const remainingHigherRateWeeks = Math.max(0, higherRateWeeks - (inHigherRatePeriod ? weeksCalculated : 0));
  const remainingStandardRateWeeks = Math.max(0, standardRateWeeks - (!inHigherRatePeriod ? weeksCalculated : 0));
  const remainingEntitlementWeeks = remainingHigherRateWeeks + remainingStandardRateWeeks;
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = qualifyingDaysCount;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.inHigherRatePeriod = inHigherRatePeriod;
  result.isHigherRate = inHigherRatePeriod;
  result.higherRate = higherRate;
  result.standardRate = standardRate;
  result.higherRateWeeks = higherRateWeeks;
  result.standardRateWeeks = standardRateWeeks;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.weeksCalculated = weeksCalculated;
  result.daysPaid = qualifyingDaysCount;
  result.remainingEntitlementWeeks = remainingEntitlementWeeks;
  result.remainingHigherRateWeeks = remainingHigherRateWeeks;
  result.remainingStandardRateWeeks = remainingStandardRateWeeks;
  
  return result;
}

/**
 * Calculate Statutory Paternity Pay (SPP)
 */
function calculateSPP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get SPP configuration
  const sppConfig = statutoryConfig.spp || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = sppConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get weekly rate from config or use default
  const weeklyRate = Math.min(sppConfig.weeklyRate || 0, input.averageWeeklyEarnings * 0.9);
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Calculate payment
  const payment = dailyRate * qualifyingDaysCount;
  
  // Calculate maximum weeks
  const maxWeeks = sppConfig.maxWeeks || 2;
  const weeksCalculated = input.weeksToCalculate || 0;
  const remainingEntitlementWeeks = Math.max(0, maxWeeks - weeksCalculated);
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = qualifyingDaysCount;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.weeksCalculated = weeksCalculated;
  result.daysPaid = qualifyingDaysCount;
  result.remainingEntitlementWeeks = remainingEntitlementWeeks;
  
  return result;
}

/**
 * Calculate Statutory Adoption Pay (SAP)
 */
function calculateSAP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get SAP configuration
  const sapConfig = statutoryConfig.sap || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = sapConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get payment week (default to 1 if not provided)
  const paymentWeek = input.paymentWeek || 1;
  
  // Get higher rate weeks and standard rate weeks from config or use defaults
  const higherRateWeeks = sapConfig.higherRateWeeks || 6;
  const standardRateWeeks = sapConfig.standardRateWeeks || 33;
  const totalWeeks = higherRateWeeks + standardRateWeeks;
  
  // Check if in higher rate period
  const inHigherRatePeriod = paymentWeek <= higherRateWeeks;
  
  // Calculate rates
  const higherRate = sapConfig.higherRatePercentage ? input.averageWeeklyEarnings * (sapConfig.higherRatePercentage / 100) : 0;
  const standardRate = Math.min(sapConfig.weeklyRate || 0, input.averageWeeklyEarnings * 0.9);
  
  // Determine weekly rate based on period
  const weeklyRate = inHigherRatePeriod ? higherRate : standardRate;
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Calculate payment
  const payment = dailyRate * qualifyingDaysCount;
  
  // Calculate remaining weeks
  const weeksCalculated = input.weeksToCalculate || 0;
  const remainingHigherRateWeeks = Math.max(0, higherRateWeeks - (inHigherRatePeriod ? weeksCalculated : 0));
  const remainingStandardRateWeeks = Math.max(0, standardRateWeeks - (!inHigherRatePeriod ? weeksCalculated : 0));
  const remainingEntitlementWeeks = remainingHigherRateWeeks + remainingStandardRateWeeks;
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = qualifyingDaysCount;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.inHigherRatePeriod = inHigherRatePeriod;
  result.isHigherRate = inHigherRatePeriod;
  result.higherRate = higherRate;
  result.standardRate = standardRate;
  result.higherRateWeeks = higherRateWeeks;
  result.standardRateWeeks = standardRateWeeks;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.weeksCalculated = weeksCalculated;
  result.daysPaid = qualifyingDaysCount;
  result.remainingEntitlementWeeks = remainingEntitlementWeeks;
  result.remainingHigherRateWeeks = remainingHigherRateWeeks;
  result.remainingStandardRateWeeks = remainingStandardRateWeeks;
  
  return result;
}

/**
 * Calculate Statutory Parental Bereavement Pay (SPBP)
 */
function calculateSPBP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get SPBP configuration
  const spbpConfig = statutoryConfig.spbp || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = spbpConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get weekly rate from config or use default
  const weeklyRate = Math.min(spbpConfig.weeklyRate || 0, input.averageWeeklyEarnings * 0.9);
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Calculate payment
  const payment = dailyRate * qualifyingDaysCount;
  
  // Calculate maximum weeks
  const maxWeeks = spbpConfig.maxWeeks || 2;
  const weeksCalculated = input.weeksToCalculate || 0;
  const remainingEntitlementWeeks = Math.max(0, maxWeeks - weeksCalculated);
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = qualifyingDaysCount;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.weeksCalculated = weeksCalculated;
  result.daysPaid = qualifyingDaysCount;
  result.remainingEntitlementWeeks = remainingEntitlementWeeks;
  
  return result;
}

/**
 * Calculate Shared Parental Pay (ShPP)
 */
function calculateShPP(
  input: StatutoryPaymentCalculationInput,
  result: StatutoryPaymentCalculationResult,
  statutoryConfig: StatutoryConfig
): StatutoryPaymentCalculationResult {
  // Get ShPP configuration
  const shppConfig = statutoryConfig.shpp || {};
  
  // Check if average weekly earnings are provided and meet the lower earnings limit
  if (input.averageWeeklyEarnings === undefined) {
    result.isEligible = false;
    result.ineligibilityReason = 'Average weekly earnings not provided';
    return result;
  }
  
  const lowerEarningsLimit = shppConfig.lowerEarningsLimit || 0;
  if (input.averageWeeklyEarnings < lowerEarningsLimit) {
    result.isEligible = false;
    result.ineligibilityReason = `Average weekly earnings (£${input.averageWeeklyEarnings.toFixed(2)}) below lower earnings limit (£${lowerEarningsLimit.toFixed(2)})`;
    return result;
  }
  
  // Calculate qualifying days
  const qualifyingDaysCount = calculateQualifyingDays(input.qualifyingDays, input.excludedDays);
  if (qualifyingDaysCount === 0) {
    result.isEligible = false;
    result.ineligibilityReason = 'No qualifying days';
    return result;
  }
  
  // Get weekly rate from config or use default
  const weeklyRate = Math.min(shppConfig.weeklyRate || 0, input.averageWeeklyEarnings * 0.9);
  
  // Calculate daily rate
  const dailyRate = weeklyRate / 7;
  
  // Calculate payment
  const payment = dailyRate * qualifyingDaysCount;
  
  // Calculate maximum weeks
  const maxWeeks = shppConfig.maxWeeks || 37;
  const weeksCalculated = input.weeksToCalculate || 0;
  const remainingEntitlementWeeks = Math.max(0, maxWeeks - weeksCalculated);
  
  // Populate result
  result.payment = payment;
  result.dailyRate = dailyRate;
  result.weeklyRate = weeklyRate;
  result.paidDays = qualifyingDaysCount;
  result.qualifyingDays = qualifyingDaysCount;
  result.excludedDays = input.excludedDays ? (typeof input.excludedDays === 'number' ? input.excludedDays : input.excludedDays.length) : 0;
  result.averageWeeklyEarnings = input.averageWeeklyEarnings;
  result.weeksCalculated = weeksCalculated;
  result.daysPaid = qualifyingDaysCount;
  result.remainingEntitlementWeeks = remainingEntitlementWeeks;
  
  return result;
}
