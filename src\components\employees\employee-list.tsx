"use client";

import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Employee } from "@/lib/schemas/employee";

interface EmployeeListProps {
  employees: Employee[];
  onSelectEmployee: (employee: Employee) => void;
  selectedEmployee?: Employee | null;
  isAddingNew?: boolean;
  searchQuery: string;
  taxYearFilter: string;
}

// Parse a date in DD/MM/YYYY format as a JS Date
const parseUKDate = (dateStr: string): Date => {
  if (!dateStr) return new Date(NaN);
  // Try ISO first (YYYY-MM-DD)
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return new Date(dateStr);
  }
  // Fallback to UK format (DD/MM/YYYY)
  const [day, month, year] = dateStr.split("/").map(Number);
  return new Date(year, month - 1, day);
};

const isCurrentTaxYear = (employee: Employee): boolean => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const taxYearStart = new Date(
    today.getMonth() >= 3 && today.getDate() >= 6
      ? currentYear
      : currentYear - 1,
    3,
    6,
  );
  const leaveDateRaw = employee.leaveDate;
  const leaveDate = leaveDateRaw ? parseUKDate(leaveDateRaw) : null;
  if (!leaveDate) return true;
  return leaveDate > taxYearStart;
};

const isLeaver = (employee: Employee): boolean => {
  if (!employee.leaveDate) return false;
  const today = new Date();
  const leaveDate = parseUKDate(employee.leaveDate);
  const result = leaveDate <= today;
  return result;
};

const EmployeeList: React.FC<EmployeeListProps> = ({
  employees,
  onSelectEmployee,
  selectedEmployee,
  isAddingNew = false,
  searchQuery,
  taxYearFilter,
}) => {
  // Filter employees based on search query and tax year filter
  const filteredEmployees = employees.filter((employee) => {
    // Handle multi-word search by splitting the query into terms
    const searchTerms = searchQuery
      .toLowerCase()
      .split(" ")
      .filter((term) => term.trim() !== "");
    if (searchTerms.length > 0) {
      const fullName = `${employee.firstName} ${employee.lastName}`.toLowerCase();
      const matchesSearch = searchTerms.every((term) => fullName.includes(term));
      if (!matchesSearch) return false;
    }
    if (taxYearFilter !== "all" && !isCurrentTaxYear(employee)) return false;
    return true;
  });

  // Sort employees: current employees first, then leavers, both sorted alphabetically by first name
  const sortedEmployees = [...filteredEmployees].sort((a, b) => {
    const aIsLeaver = isLeaver(a);
    const bIsLeaver = isLeaver(b);
    
    // If one is a leaver and the other isn't, the non-leaver comes first
    if (aIsLeaver && !bIsLeaver) return 1;
    if (!aIsLeaver && bIsLeaver) return -1;
    
    // If both are leavers or both are current, sort alphabetically by first name
    return a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase());
  });

  return (
    <ScrollArea className="h-full rounded-2xl border-1 p-2">
      <div className="p-2">
        {sortedEmployees.length === 0 && !isAddingNew ? (
          <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
            <p>No employees found</p>
          </div>
        ) : (
          <>
            {isAddingNew && (
              <div className="w-full px-3 py-3 text-left rounded-md bg-sky-50 border border-sky-100 mb-2">
                <div className="text-sky-600 text-sm font-medium italic">
                  Creating new employee...
                </div>
              </div>
            )}
            <ul className="space-y-1">
              {sortedEmployees.map((employee, index) => {
                const isSelected = selectedEmployee && 
                  selectedEmployee.firstName === employee.firstName && 
                  selectedEmployee.lastName === employee.lastName;
                
                const employeeIsLeaver = isLeaver(employee);
                
                return (
                  <li key={index}>
                    <button
                      onClick={() => onSelectEmployee(employee)}
                      className={cn(
                        "w-full px-3 py-2 text-left rounded-md transition-colors text-sm",
                        "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        isSelected 
                          ? "bg-slate-200 dark:bg-zinc-700 rounded-2xl" 
                          : "hover:bg-slate-200 dark:hover:bg-zinc-800 rounded-2xl hover:text-accent-foreground",
                        employeeIsLeaver && "opacity-70"
                      )}
                    >
                      <div className="flex justify-between items-center">
                        <span>
                          {employee.firstName} {employee.lastName}
                        </span>
                        {employeeIsLeaver && (
                          <span className="text-xs px-2 py-0.5 rounded bg-red-100 text-red-700 border border-red-200">
                            Leaver
                          </span>
                        )}
                      </div>
                    </button>
                  </li>
                );
              })}
            </ul>
          </>
        )}
      </div>
    </ScrollArea>
  );
};

export default EmployeeList;
