import { useMutation, useQueryClient } from "@tanstack/react-query";

// Define interfaces for the response types
export interface AddExistingEmployerResult {
  success: boolean;
  error?: string;
  employer?: {
    id: string;
    name: string;
    filePath: string;
  };
  // For multiple files
  multipleFiles?: boolean;
  results?: Array<{
    id: string;
    name: string;
    filePath: string;
  }>;
  errors?: Array<{
    path: string;
    error: string;
  }>;
  totalAdded?: number;
  totalErrors?: number;
  totalFiles?: number;
}

/**
 * Hook to add one or more existing employer DB files to the master DB.
 * - Takes a file path and adds it to the master DB, or opens a file picker if empty string is provided.
 * - Returns the employer data if successful, or summary data for multiple files.
 * - Invalidates the employers query to refresh the list.
 */
export function useAddExistingEmployer() {
  const queryClient = useQueryClient();

  return useMutation<AddExistingEmployerResult, Error, string>({
    mutationFn: async (filePath: string) => {
            return await window.api.addExistingEmployer(filePath);
    },
    onSuccess: () => {
      // Invalidate the employers query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["employers"] });
    },
  });
}

/**
 * Hook to remove an employer from the master DB.
 * - Takes an employer ID and removes it from the master DB.
 * - Does NOT delete the employer DB file, only removes the reference from the master DB.
 * - Invalidates the employers query to refresh the list.
 */
export function useRemoveEmployer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (employerId: string) => {
            return await window.api.removeEmployer(employerId);
    },
    onSuccess: () => {
      // Invalidate the employers query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["employers"] });
    },
  });
}
