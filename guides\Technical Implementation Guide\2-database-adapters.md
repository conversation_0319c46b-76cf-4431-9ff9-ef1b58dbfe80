# UK Payroll Application - Technical Guide Part 2: Database Adapters

## Master Database Adapter

The Master Database Adapter provides access to the application's master database which stores application settings and employer file references.

```typescript
// src/db/adapters/master.ts
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import Database from 'better-sqlite3';
import path from 'path';
import { mkdir } from 'fs/promises';
import * as schema from '../schema/master';

// Interface for the adapter
export interface MasterDatabaseAdapter {
  db: ReturnType<typeof drizzle>;
  initialize(): Promise<void>;
  close(): Promise<void>;
}

/**
 * Desktop implementation of the Master Database Adapter
 * Uses a local SQLite file
 */
export class MasterDatabaseAdapter implements MasterDatabaseAdapter {
  private _db: ReturnType<typeof drizzle> | null = null;
  private _sqlite: Database.Database | null = null;
  private _dbPath: string;

  constructor() {
    // In Electron, get the user data directory
    if (typeof window !== 'undefined' && window.electronAPI) {
      this._dbPath = path.join(
        window.electronAPI.getPath('userData'),
        'master.sqlite'
      );
    } else {
      // Fallback for development without Electron
      this._dbPath = path.join(process.cwd(), '.dev-data', 'master.sqlite');
    }
  }

  get db() {
    if (!this._db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this._db;
  }

  async initialize(): Promise<void> {
    try {
      // Ensure directory exists
      await mkdir(path.dirname(this._dbPath), { recursive: true });

      // Initialize SQLite database
      this._sqlite = new Database(this._dbPath, { 
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined 
      });
      
      // Optimize SQLite settings
      this._sqlite.pragma('journal_mode = WAL'); // Write-Ahead Logging for better concurrency
      this._sqlite.pragma('synchronous = NORMAL'); // Balance between durability and performance
      this._sqlite.pragma('foreign_keys = ON'); // Enable foreign key constraints

      // Initialize Drizzle
      this._db = drizzle(this._sqlite, { schema });

      // Run migrations
      migrate(this._db, { migrationsFolder: path.join(process.cwd(), 'drizzle/migrations/master') });

      console.log('Master database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize master database:', error);
      throw error;
    }
  }

  async close(): Promise<void> {
    if (this._sqlite) {
      this._sqlite.close();
      this._sqlite = null;
      this._db = null;
    }
  }
}

// Singleton instance for the application
let masterDbAdapter: MasterDatabaseAdapter | null = null;

export async function getMasterDb(): Promise<ReturnType<typeof drizzle>> {
  if (!masterDbAdapter) {
    masterDbAdapter = new MasterDatabaseAdapter();
    await masterDbAdapter.initialize();
  }
  return masterDbAdapter.db;
}
```

## Employer Database Adapter

The Employer Database Adapter manages employer-specific SQLite databases, handling creation, opening, and management of employer files.

```typescript
// src/db/adapters/employer.ts
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import Database from 'better-sqlite3';
import path from 'path';
import { mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import { v4 as uuidv4 } from 'uuid';
import * as schema from '../schema/employer';
import { getMasterDb } from './master';
import { employers } from '../schema/master';
import { eq } from 'drizzle-orm';

// Types for employer metadata
export interface EmployerIdentifier {
  id: string;
  name: string;
  filePath: string;
}

export interface NewEmployerDetails {
  name: string;
  tradingName?: string;
  taxReference?: string;
  companyNumber?: string;
  address?: any;
  contactDetails?: any;
}

// Interface for the adapter
export interface EmployerDatabaseAdapter {
  open(identifier: string): Promise<ReturnType<typeof drizzle>>;
  create(details: NewEmployerDetails): Promise<EmployerIdentifier>;
  close(id: string): Promise<void>;
  backup(id: string, destination: string): Promise<boolean>;
  restore(source: string): Promise<EmployerIdentifier>;
  getAll(): Promise<EmployerIdentifier[]>;
  getRecentlyAccessed(limit?: number): Promise<EmployerIdentifier[]>;
  updateLastAccessed(id: string): Promise<void>;
  scanDirectory(directory: string): Promise<EmployerIdentifier[]>;
  addExistingFile(filePath: string): Promise<EmployerIdentifier>;
}

/**
 * Implementation of the Employer Database Adapter
 */
export class EmployerDatabaseAdapter implements EmployerDatabaseAdapter {
  private _connections = new Map<string, { 
    db: ReturnType<typeof drizzle>, 
    sqlite: Database.Database 
  }>();
  private _dataPath: string;

  constructor() {
    // In Electron, get the user data directory
    if (typeof window !== 'undefined' && window.electronAPI) {
      this._dataPath = path.join(
        window.electronAPI.getPath('userData'),
        'employers'
      );
    } else {
      // Fallback for development without Electron
      this._dataPath = path.join(process.cwd(), '.dev-data', 'employers');
    }
  }

  async open(identifier: string): Promise<ReturnType<typeof drizzle>> {
    try {
      // Check if already open
      if (this._connections.has(identifier)) {
        const conn = this._connections.get(identifier)!;
        await this.updateLastAccessed(identifier);
        return conn.db;
      }

      // Get employer details from master database
      const masterDb = await getMasterDb();
      const result = await masterDb.select().from(employers).where(eq(employers.id, identifier)).limit(1);
      
      if (result.length === 0) {
        throw new Error(`Employer with ID ${identifier} not found`);
      }
      
      const employer = result[0];
      
      // Ensure file exists
      if (!existsSync(employer.filePath)) {
        throw new Error(`Employer database file not found at ${employer.filePath}`);
      }
      
      // Open database connection
      const sqlite = new Database(employer.filePath, { 
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined 
      });
      
      // Optimize SQLite settings
      sqlite.pragma('journal_mode = WAL');
      sqlite.pragma('synchronous = NORMAL');
      sqlite.pragma('foreign_keys = ON');
      
      // Initialize Drizzle
      const db = drizzle(sqlite, { schema });
      
      // Store connection
      this._connections.set(identifier, { db, sqlite });
      
      // Update last accessed timestamp
      await this.updateLastAccessed(identifier);
      
      return db;
    } catch (error) {
      console.error(`Failed to open employer database ${identifier}:`, error);
      throw error;
    }
  }

  async create(details: NewEmployerDetails): Promise<EmployerIdentifier> {
    try {
      // Generate new ID
      const id = uuidv4();
      
      // Ensure directory exists
      await mkdir(this._dataPath, { recursive: true });
      
      // Generate file path
      const filePath = path.join(this._dataPath, `${id}.ukpayroll`);
      
      // Create new SQLite database
      const sqlite = new Database(filePath);
      
      // Optimize SQLite settings
      sqlite.pragma('journal_mode = WAL');
      sqlite.pragma('synchronous = NORMAL');
      sqlite.pragma('foreign_keys = ON');
      
      // Initialize Drizzle
      const db = drizzle(sqlite, { schema });
      
      // Run migrations
      migrate(db, { migrationsFolder: path.join(process.cwd(), 'drizzle/migrations/employer') });
      
      // Insert employer details
      await db.insert(schema.employer).values({
        id,
        name: details.name,
        tradingName: details.tradingName,
        taxReference: details.taxReference,
        companyNumber: details.companyNumber,
        address: details.address ? JSON.stringify(details.address) : undefined,
        contactDetails: details.contactDetails ? JSON.stringify(details.contactDetails) : undefined,
      });
      
      // Store connection
      this._connections.set(id, { db, sqlite });
      
      // Register in master database
      const masterDb = await getMasterDb();
      await masterDb.insert(employers).values({
        id,
        name: details.name,
        filePath,
        lastAccessed: new Date(),
      });
      
      return { id, name: details.name, filePath };
    } catch (error) {
      console.error('Failed to create employer database:', error);
      throw error;
    }
  }

  async close(id: string): Promise<void> {
    if (this._connections.has(id)) {
      const conn = this._connections.get(id)!;
      conn.sqlite.close();
      this._connections.delete(id);
    }
  }

  async backup(id: string, destination: string): Promise<boolean> {
    try {
      // Get employer details
      const masterDb = await getMasterDb();
      const result = await masterDb.select().from(employers).where(eq(employers.id, id)).limit(1);
      
      if (result.length === 0) {
        throw new Error(`Employer with ID ${id} not found`);
      }
      
      const employer = result[0];
      
      // Close connection if open
      await this.close(id);
      
      // Create backup
      const backupSqlite = new Database(employer.filePath);
      const destSqlite = new Database(destination);
      
      backupSqlite.backup(destSqlite)
        .then(() => {
          backupSqlite.close();
          destSqlite.close();
          return true;
        })
        .catch(err => {
          backupSqlite.close();
          destSqlite.close();
          throw err;
        });
      
      return true;
    } catch (error) {
      console.error(`Backup failed for employer ${id}:`, error);
      return false;
    }
  }

  async restore(source: string): Promise<EmployerIdentifier> {
    try {
      // Open source database to verify integrity
      const sourceSqlite = new Database(source);
      const sourceDb = drizzle(sourceSqlite, { schema });
      
      // Verify it's a valid employer database
      const employerInfo = await sourceDb.select().from(schema.employer).limit(1);
      
      if (employerInfo.length === 0) {
        sourceSqlite.close();
        throw new Error('Invalid employer database file');
      }
      
      // Generate new ID (we don't reuse the ID from the file)
      const id = uuidv4();
      
      // Generate file path
      const filePath = path.join(this._dataPath, `${id}.ukpayroll`);
      
      // Create a copy of the database
      const destSqlite = new Database(filePath);
      
      await sourceSqlite.backup(destSqlite);
      
      // Close connections
      sourceSqlite.close();
      destSqlite.close();
      
      // Register in master database
      const masterDb = await getMasterDb();
      await masterDb.insert(employers).values({
        id,
        name: employerInfo[0].name as string,
        filePath,
        lastAccessed: new Date(),
      });
      
      return { 
        id, 
        name: employerInfo[0].name as string, 
        filePath 
      };
    } catch (error) {
      console.error('Failed to restore employer database:', error);
      throw error;
    }
  }

  async getAll(): Promise<EmployerIdentifier[]> {
    try {
      const masterDb = await getMasterDb();
      const results = await masterDb.select({
        id: employers.id,
        name: employers.name,
        filePath: employers.filePath,
      }).from(employers);
      
      return results;
    } catch (error) {
      console.error('Failed to get all employers:', error);
      throw error;
    }
  }

  async getRecentlyAccessed(limit: number = 10): Promise<EmployerIdentifier[]> {
    try {
      const masterDb = await getMasterDb();
      const results = await masterDb.select({
        id: employers.id,
        name: employers.name,
        filePath: employers.filePath,
      })
      .from(employers)
      .orderBy(employers.lastAccessed)
      .limit(limit);
      
      return results;
    } catch (error) {
      console.error('Failed to get recently accessed employers:', error);
      throw error;
    }
  }

  async updateLastAccessed(id: string): Promise<void> {
    try {
      const masterDb = await getMasterDb();
      await masterDb.update(employers)
        .set({ lastAccessed: new Date() })
        .where(eq(employers.id, id));
    } catch (error) {
      console.error(`Failed to update last accessed timestamp for employer ${id}:`, error);
    }
  }

  async scanDirectory(directory: string): Promise<EmployerIdentifier[]> {
    try {
      const fs = require('fs');
      const { promisify } = require('util');
      const readdir = promisify(fs.readdir);
      
      const files = await readdir(directory);
      const ukpayrollFiles = files.filter(file => file.endsWith('.ukpayroll'));
      
      const results: EmployerIdentifier[] = [];
      
      for (const file of ukpayrollFiles) {
        try {
          const filePath = path.join(directory, file);
          const result = await this.addExistingFile(filePath);
          results.push(result);
        } catch (error) {
          console.error(`Failed to add file ${file}:`, error);
          // Continue with next file
        }
      }
      
      return results;
    } catch (error) {
      console.error(`Failed to scan directory ${directory}:`, error);
      throw error;
    }
  }

  async addExistingFile(filePath: string): Promise<EmployerIdentifier> {
    try {
      // Check if file exists
      if (!existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      
      // Check if file is already registered
      const masterDb = await getMasterDb();
      const existingEntry = await masterDb.select()
        .from(employers)
        .where(eq(employers.filePath, filePath))
        .limit(1);
      
      if (existingEntry.length > 0) {
        return {
          id: existingEntry[0].id,
          name: existingEntry[0].name,
          filePath: existingEntry[0].filePath
        };
      }
      
      // Open the database to verify integrity and get employer name
      const sqlite = new Database(filePath);
      const db = drizzle(sqlite, { schema });
      
      const employerInfo = await db.select().from(schema.employer).limit(1);
      
      if (employerInfo.length === 0) {
        sqlite.close();
        throw new Error('Invalid employer database file');
      }
      
      const id = uuidv4();
      const name = employerInfo[0].name as string;
      
      // Close the connection
      sqlite.close();
      
      // Register in master database
      await masterDb.insert(employers).values({
        id,
        name,
        filePath,
        lastAccessed: new Date(),
      });
      
      return { id, name, filePath };
    } catch (error) {
      console.error(`Failed to add existing file ${filePath}:`, error);
      throw error;
    }
  }
}

// Singleton instance for the employer adapter
let employerDbAdapter: EmployerDatabaseAdapter | null = null;

export function getEmployerDbAdapter(): EmployerDatabaseAdapter {
  if (!employerDbAdapter) {
    employerDbAdapter = new EmployerDatabaseAdapter();
  }
  return employerDbAdapter;
}
```
