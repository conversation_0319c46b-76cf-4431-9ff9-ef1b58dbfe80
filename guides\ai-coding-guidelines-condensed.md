# UK Payroll Application - Essential AI Coding Rules

This condensed guide provides essential rules for AI assistants working on the UK Payroll application. For complete details, refer to the full guidelines document.

## Core Architecture Rules

- **Database Structure**: Implement one SQLite file per employer with a custom file extension (use the `EMPLOYER_DB_EXTENSION` constant from `src/constants/file.ts` for all references to the extension) and a separate master database for app metadata.
- **ORM Usage**: Use Drizzle ORM with libsql for all database operations
- **Data Fetching**: Use TanStack Query for all data fetching, caching, and state management
- **File Management**: Implement proper file creation, opening, scanning, and importing functionality
- **Incremental Database Development**: Start with minimal schema and expand incrementally as UI is developed
  - **Update on routing**: Now using state based navigation with Zustand store. No URL based routing

## SQLite Implementation

- Use WAL journal mode and appropriate pragmas for performance
- Implement proper transaction handling for multi-step operations
- Store databases initially in project root, later in appropriate user data directories
- Use drizzle-zod for schema validation
- Separate master database from employer-specific databases

## UK Payroll Specifics

- Follow HMRC regulations for tax calculations, RTI submissions, and statutory payments
- Use appropriate UK-specific data formats (NI numbers, tax codes, etc.)
- Display values in UK format (£ symbol, DD/MM/YYYY dates)
- Implement tax year configurations correctly
- Ensure all calculations are accurate to the penny

## Desktop Focus

- Optimize all code for desktop performance
- Implement proper file associations with the operating system
- Create efficient file handling for employer databases
- Build directory scanning functionality for finding employer files
- Provide native desktop experience with proper keyboard shortcuts and UX

## TanStack Query Integration

- Create custom query hooks for common data access patterns
- Use appropriate query keys for efficient caching and invalidation
- Implement optimistic updates for mutations
- Configure appropriate stale times and refetching strategies
- Handle loading, error, and success states appropriately

## Database Schema Rules

- Follow the established schema structure for both master and employer databases
- Use snake_case for database columns, camelCase for JavaScript properties
- Encrypt sensitive employee data (bank details, NI numbers)
- Implement proper relationships and indexes
- Use JSON storage for complex nested structures

## Security Essentials

- Encrypt all sensitive employee data
- Validate all user inputs with Zod schemas
- Use parameterized queries for all database operations
- Implement file integrity validation
- Never expose raw Node.js modules to renderer process

## Performance Guidelines

- Use Web Workers for intensive calculations
- Implement pagination and virtualization for large datasets
- Optimize SQLite queries with proper indexes
- Use TanStack Query for efficient data caching and fetching
- Implement background threads for document generation

## Code Organization

- Follow the established project structure
- Use TypeScript with strict typing
- Maintain separation of concerns between UI, business logic, and data access
- Implement comprehensive error handling
- Create proper interfaces and types for all data structures

## Incremental Development

- Start with minimal schema and expand as UI features are developed
- Build features incrementally, focusing on one complete feature at a time
- Begin with simple file operations and enhance over time
- Implement basic functionality first, then add advanced features
- Create proper migration paths for schema changes

## File Management

- Implement proper employer file creation and opening
- Build file scanning functionality for default directories
- Create file import capability for external files
- Make employer files self-contained and portable
- Implement file integrity validation and backup functionality

This condensed guide provides essential rules for maintaining consistency and quality. Refer to the complete guidelines document for detailed implementation guidance in specific areas.
