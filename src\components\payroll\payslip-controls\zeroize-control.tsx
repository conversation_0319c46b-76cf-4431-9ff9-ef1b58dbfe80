"use client";

import React from "react";
import { ClickableCheckbox } from "@/components/ui/clickable-checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RotateCcw } from "lucide-react";

interface ZeroizeControlProps {
  id: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const ZeroizeControl: React.FC<ZeroizeControlProps> = ({
  id,
  isChecked,
  onChange,
  disabled = false,
  className = "",
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={className}>
          <ClickableCheckbox
            id={`zeroize-${id}`}
            className="size-4 data-[state=checked]:border-amber-600 data-[state=checked]:bg-amber-400"
            checked={isChecked}
            onCheckedChange={(checked) => onChange(checked === true)}
            disabled={disabled}
            label={
              <RotateCcw
                className={`size-4 ${
                  isChecked && !disabled
                    ? "text-amber-500"
                    : "text-muted-foreground"
                }`}
              />
            }
            wrapperClassName="flex items-center"
          />
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>Zeroize Next</p>
      </TooltipContent>
    </Tooltip>
  );
};
