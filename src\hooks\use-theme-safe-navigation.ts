"use client";

import { useRouter } from "next/navigation";
import { useTheme } from "@/providers/theme-provider";

/**
 * Custom hook for theme-safe navigation
 * Ensures theme persistence when navigating between pages
 */
export function useThemeSafeNavigation() {
  const router = useRouter();
  const { theme } = useTheme();

  /**
   * Navigate to a new route while preserving theme state
   * @param href The URL to navigate to
   */
  const navigateTo = (href: string) => {
    // Use Next.js router for client-side navigation
    // This preserves React state including theme
    router.push(href);
  };

  return {
    navigateTo,
  };
}
