import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { finalisePayslips, reopenPayslips } from "@/services/employerDbService";

/**
 * Hook to finalise selected payslips and open next period
 */
export function useFinalisePayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (slipIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      return finalisePayslips(dbPath, periodId, slipIds);
    },
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        queryClient.invalidateQueries({
          queryKey: ["payslips", dbPath, periodId],
        });
        // Invalidate payslip statuses for this period (this updates the UI status indicators)
        queryClient.invalidateQueries({
          queryKey: ["payslipStatuses", dbPath, periodId],
        });
      }
    },
  });
}

/**
 * Hook to reopen selected payslips
 */
export function useReopenPayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (employeeIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      return reopenPayslips(dbPath, periodId, employeeIds);
    },
    onSuccess: () => {
      if (dbPath) {
        // More targeted cache invalidation to prevent potential memory issues
        queryClient.invalidateQueries({
          queryKey: ["payslips", dbPath, periodId],
        });
        queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        queryClient.invalidateQueries({
          queryKey: ["payslipStatuses", dbPath, periodId],
        });
      }
    },
  });
}
