"use client";

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectSeparator, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Employee } from '@/lib/schemas/employee';
import { getTaxCodeForStarterDeclaration } from "@/core/calculations/utils/tax-code-utils";

interface TaxSectionProps {
  employee: Employee;
  onChange: (section: string, field: string, value: any) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
}

// Following UK-specific payroll regulations for tax code handling and NI categories
const TaxSection: React.FC<TaxSectionProps> = ({ employee, onChange, onValidationChange }) => {
  // Function to handle input changes
  const handleChange = (field: string, value: any) => {
    onChange("tax", field, value);
  };

  // Format tax code to handle proper UK tax code formats (including K codes, S/C prefixes)
  const formatTaxCode = (code: string): string => {
    // Ensure proper formatting of tax code as per HMRC PAYErout guidance
    return code.toUpperCase().trim();
  };

  // Function to format NI number as AB 12 34 56 C
  const formatNINumber = (value: string): string => {
    // Remove all spaces and convert to uppercase
    const cleaned = value.replace(/\s/g, '').toUpperCase();
    
    // Check if the input is valid for formatting
    if (cleaned.length === 0) return '';
    
    // Apply the format: AB 12 34 56 C
    let formatted = '';
    for (let i = 0; i < cleaned.length && i < 9; i++) {
      // Add space after positions 2, 4, 6, 8 (0-indexed)
      if (i === 2 || i === 4 || i === 6 || i === 8) {
        formatted += ' ';
      }
      formatted += cleaned[i];
    }
    
    return formatted.trim();
  };

  // Function to validate NI number according to HMRC rules
  const validateNINumber = (niNumber: string, checkCompleteness: boolean = false): string[] => {
    if (!niNumber || niNumber.trim() === '') {
      return []; // Empty is allowed (not required field)
    }
    
    // Remove spaces for validation
    const cleaned = niNumber.replace(/\s/g, '').toUpperCase();
    const errors: string[] = [];
    
    // Only validate what we can based on current input length
    const currentLength = cleaned.length;
    
    // Check first letter immediately if we have at least 1 character
    if (currentLength >= 1) {
      const firstLetter = cleaned[0];
      
      // Check first letter is a valid letter
      if (!/[A-Z]/.test(firstLetter)) {
        errors.push('First character must be a letter');
      } else {
        // Check invalid first letter (D, F, I, Q, U, V)
        const invalidLetters = ['D', 'F', 'I', 'Q', 'U', 'V'];
        if (invalidLetters.includes(firstLetter)) {
          errors.push('First letter cannot be D, F, I, Q, U, or V');
        }
      }
    }
    
    // Check second letter if we have at least 2 characters
    if (currentLength >= 2) {
      const prefix = cleaned.substring(0, 2);
      const secondLetter = prefix[1];
      
      // Check second letter is a valid letter
      if (!/[A-Z]/.test(secondLetter)) {
        errors.push('Second character must be a letter');
      } else {
        // Check invalid second letter (D, F, I, Q, U, V, O)
        const invalidSecondLetters = ['D', 'F', 'I', 'Q', 'U', 'V', 'O'];
        if (invalidSecondLetters.includes(secondLetter)) {
          errors.push(`Second letter cannot be ${secondLetter}`);
        }
      }
      
      // Check invalid prefixes if both letters are valid
      if (/^[A-Z]{2}$/.test(prefix)) {
        const invalidPrefixes = ['BG', 'GB', 'KN', 'NK', 'NT', 'TN', 'ZZ'];
        if (invalidPrefixes.includes(prefix)) {
          errors.push(`Prefix ${prefix} is not valid`);
        }
      }
    }
    
    // Check the 6 numbers (positions 2-7) if we have enough characters
    for (let i = 2; i < 8 && i < currentLength; i++) {
      if (!/[0-9]/.test(cleaned[i])) {
        errors.push(`Character at position ${i+1} must be a number`);
        break; // Only show one number format error at a time
      }
    }
    
    // Check suffix if we have a complete NI number
    if (currentLength >= 9) {
      const suffix = cleaned[8];
      
      // Check suffix is a letter
      if (!/[A-Z]/.test(suffix)) {
        errors.push('Last character must be a letter');
      } else if (!['A', 'B', 'C', 'D'].includes(suffix)) {
        // Check suffix is A, B, C, or D
        errors.push('Last letter must be A, B, C, or D');
      }
      
      // Check for extra characters
      if (currentLength > 9) {
        errors.push('NI number should be exactly 9 characters (excluding spaces)');
      }
    }
    
    // Add error for incomplete NI number ONLY if we're doing a complete validation
    // and the NI number is not empty but incomplete
    if (checkCompleteness && currentLength > 0 && currentLength < 9) {
      errors.push('NI number is incomplete - must be 9 characters (excluding spaces)');
    }
    
    return errors;
  };

  // State for NI number validation
  const [niNumberErrors, setNiNumberErrors] = useState<string[]>([]);
  const [isCompleteValidation, setIsCompleteValidation] = useState<boolean>(false);

  // Check if NI number is valid for saving
  const isNINumberValid = (completeValidation: boolean = false): boolean => {
    // If NI number is empty, it's valid (not required)
    if (!employee.niNumber || employee.niNumber.trim() === '') {
      return true;
    }
    
    // Remove spaces for validation
    const cleaned = employee.niNumber.replace(/\s/g, '').toUpperCase();
    
    // If doing a complete validation, ensure the NI number is complete
    if (completeValidation && cleaned.length < 9) {
      return false;
    }
    
    // Otherwise, check for validation errors
    const errors = validateNINumber(employee.niNumber || '', completeValidation);
    return errors.length === 0;
  };

  // Update parent component about validation status when NI number changes
  useEffect(() => {
    // During regular typing, don't check for completeness
    const errors = validateNINumber(employee.niNumber || '', false);
    setNiNumberErrors(errors);
    
    // Notify parent of validation status
    if (onValidationChange) {
      // Only use completeness validation when isCompleteValidation is true
      const isValid = isNINumberValid(isCompleteValidation);
      const message = isValid ? '' : 'NI number is invalid or incomplete';
      onValidationChange(isValid, message);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [employee.niNumber, isCompleteValidation]);

  // Trigger complete validation when section is changed or form is saved
  useEffect(() => {
    // When component is about to be unmounted (section change) or submitted (save)
    return () => {
      // Set to complete validation mode to check for incomplete NI numbers
      setIsCompleteValidation(true);
      
      // Force validation check
      if (onValidationChange) {
        const isValid = isNINumberValid(true);
        const message = isValid ? '' : 'NI number is invalid or incomplete';
        onValidationChange(isValid, message);
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // State to track if component is mounted
  const [isMounted, setIsMounted] = useState(false);

  return (
    <div className="space-y-1 pt-4">

      {/* Tax Code */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="taxCode" className="text-right font-md col-span-2 justify-self-end mx-4">Tax code</Label>
        <div className="col-span-3 flex items-center gap-4">
          <Input
            id="taxCode"
            className="h-7 text-sm w-24"
            value={employee.taxCode || ""}
            onChange={(e) => {
              // Only update if value has changed to prevent unnecessary re-renders
              if (e.target.value !== employee.taxCode) {
                handleChange("taxCode", e.target.value === "" ? undefined : formatTaxCode(e.target.value));
              }
            }}
            placeholder="e.g. 1257L"
          />
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="week1Month1" 
              checked={employee.week1Month1}
              onCheckedChange={(checked) => handleChange("week1Month1", Boolean(checked))}
            />
            <Label htmlFor="week1Month1" className="text-sm font-normal">Week 1/Month 1 basis</Label>
          </div>
        </div>
      </div>

      {/* Student Loan */}
      <div className="grid grid-cols-10 gap-2 items-start pt-2">
        <Label className="text-right font-medium col-span-2 pt-1 justify-self-end mx-4">Student Loan</Label>
        <div className="col-span-8">
          <div className="flex items-center flex-wrap gap-4">
            <div className="flex items-center space-x-1">
              <Checkbox 
                id="studentLoanNone" 
                checked={!employee.studentLoan || employee.studentLoan === "Not applicable (do not deduct Student Loan repayments)"}
                onCheckedChange={(checked) => {
                  if (checked) {
                    handleChange("studentLoan", "Not applicable (do not deduct Student Loan repayments)");
                  }
                }}
              />
              <Label htmlFor="studentLoanNone" className="text-sm font-normal">No student loan</Label>
            </div>
            
            <div className="flex items-center space-x-1">
              <Checkbox 
                id="studentLoanPlan1" 
                checked={employee.studentLoan === "Plan 1"}
                onCheckedChange={(checked) => {
                  handleChange("studentLoan", checked ? "Plan 1" : "Not applicable (do not deduct Student Loan repayments)");
                }}
              />
              <Label htmlFor="studentLoanPlan1" className="text-sm font-normal">Plan 1</Label>
            </div>
            
            <div className="flex items-center space-x-1">
              <Checkbox 
                id="studentLoanPlan2" 
                checked={employee.studentLoan === "Plan 2"}
                onCheckedChange={(checked) => {
                  handleChange("studentLoan", checked ? "Plan 2" : "Not applicable (do not deduct Student Loan repayments)");
                }}
              />
              <Label htmlFor="studentLoanPlan2" className="text-sm font-normal">Plan 2</Label>
            </div>
            
            <div className="flex items-center space-x-1">
              <Checkbox 
                id="studentLoanPlan4" 
                checked={employee.studentLoan === "Plan 4"}
                onCheckedChange={(checked) => {
                  handleChange("studentLoan", checked ? "Plan 4" : "Not applicable (do not deduct Student Loan repayments)");
                }}
              />
              <Label htmlFor="studentLoanPlan4" className="text-sm font-normal">Plan 4</Label>
            </div>
            
            <div className="flex items-center space-x-1">
              <Checkbox 
                id="studentLoanPlan5" 
                checked={employee.studentLoan === "Plan 5"}
                onCheckedChange={(checked) => {
                  handleChange("studentLoan", checked ? "Plan 5" : "Not applicable (do not deduct Student Loan repayments)");
                }}
              />
              <Label htmlFor="studentLoanPlan5" className="text-sm font-normal">Plan 5</Label>
            </div>
          </div>
          
          {/* Student Loan Dates - Only show when a plan is selected */}
          {employee.studentLoan && employee.studentLoan !== "Not applicable (do not deduct Student Loan repayments)" && (
            <>
              <div className="grid grid-cols-10 gap-2 items-center pt-2">
                <Label htmlFor="studentLoanStartDate" className="text-left font-normal text-sm text-slate-600 col-span-1">SL Start</Label>
                <div className="col-span-3">
                  <Input
                    id="studentLoanStartDate"
                    type="date"
                    className="h-7 text-sm"
                    value={employee.studentLoanStartDate || ""}
                    onChange={(e) => handleChange("studentLoanStartDate", e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-2 items-center pt-2">
                <Label htmlFor="studentLoanEndDate" className="text-right font-normal text-sm text-slate-600 col-span-1">SL End</Label>
                <div className="col-span-3">
                  <Input
                    id="studentLoanEndDate"
                    type="date"
                    className="h-7 text-sm"
                    value={employee.studentLoanEndDate || ""}
                    onChange={(e) => handleChange("studentLoanEndDate", e.target.value)}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Postgraduate Loan */}
      <div className="grid grid-cols-10 gap-2 items-start pt-2">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Postgraduate Loan</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="postgraduateLoan" 
              checked={employee.postgraduateLoan === "Postgraduate Loan"}
              onCheckedChange={(checked) => {
                handleChange("postgraduateLoan", checked ? 
                  "Postgraduate Loan" : 
                  "Not applicable (do not deduct Postgraduate Loan repayments)");
              }}
            />
            <Label htmlFor="postgraduateLoan" className="text-sm font-normal">Deduct Postgraduate Loan repayments</Label>
          </div>
          
          {/* Postgraduate Loan Dates - Only show when selected */}
          {employee.postgraduateLoan === "Postgraduate Loan" && (
            <>
              <div className="grid grid-cols-10 gap-2 items-center pt-2">
                <Label htmlFor="pgLoanStartDate" className="text-right font-normal text-sm text-slate-600 col-span-1">PGL Start date</Label>
                <div className="col-span-3">
                  <Input
                    id="pgLoanStartDate"
                    type="date"
                    className="h-7 text-sm"
                    value={employee.pgLoanStartDate || ""}
                    onChange={(e) => handleChange("pgLoanStartDate", e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-2 items-center pt-2">
                <Label htmlFor="pgLoanEndDate" className="text-right font-normal text-sm text-slate-600 col-span-1">PGL End date</Label>
                <div className="col-span-3">
                  <Input
                    id="pgLoanEndDate"
                    type="date"
                    className="h-7 text-sm"
                    value={employee.pgLoanEndDate || ""}
                    onChange={(e) => handleChange("pgLoanEndDate", e.target.value)}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* National Insurance Number */}
      <div className="grid grid-cols-10 gap-2 items-center pt-6">
        <Label htmlFor="niNumber" className="text-right font-medium col-span-2 justify-self-end mx-4">NI number</Label>
        <div className="col-span-2">
          <div className="space-y-1">
            <Input 
              id="niNumber" 
              className={`h-7 text-sm ${niNumberErrors.length > 0 ? 'border-red-500' : ''}`}
              placeholder="AB 12 34 56 C"
              value={employee.niNumber || ""} 
              onChange={(e) => {
                const formatted = formatNINumber(e.target.value);
                handleChange("niNumber", formatted);
                
                // Validate the NI number but don't check for completeness during typing
                const errors = validateNINumber(formatted, false);
                setNiNumberErrors(errors);
              }}
              onBlur={() => {
                // When field loses focus, do a complete validation
                setIsCompleteValidation(true);
                
                // Only show incomplete error when user has finished editing
                const errors = validateNINumber(employee.niNumber || '', true);
                setNiNumberErrors(errors);
                
                // Notify parent of validation status
                if (onValidationChange) {
                  const isValid = isNINumberValid(true);
                  const message = isValid ? '' : 'NI number is invalid or incomplete';
                  onValidationChange(isValid, message);
                }
              }}
            />
            {niNumberErrors.length > 0 && (
              <Alert variant="destructive" className="py-2 text-xs w-180">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-disc pl-4">
                    {niNumberErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      </div>

      {/* National Insurance Table */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label htmlFor="niTable" className="text-right justify-self-end font-medium col-span-2 mx-4">NI table</Label>
        <div className="col-span-8 flex items-center gap-2">
          {/* Read-only display of the NI category letter for RTI */}
          <div className="flex items-center gap-2">
            <Input
              id="niTable"
              className="h-7 text-sm w-18 font-medium text-center"
              value={employee.niTable || "A"}
              readOnly
            />
          </div>
          
          {/* User-friendly selection dropdown */}
          <Select
            value={employee.niTable || "A"}
            onValueChange={(value) => handleChange("niTable", value)}
          >
            <SelectTrigger className="h-9 text-sm">
              <SelectValue placeholder="Select NI category" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Standard</SelectLabel>
                <SelectItem value="A">A - Standard</SelectItem>
                <SelectItem value="B">B - Married Women & Widows Reduced</SelectItem>
                <SelectItem value="C">C - Pension Age</SelectItem>
                <SelectItem value="H">H - Apprentice under 25</SelectItem>
                <SelectItem value="J">J - Deferred</SelectItem>
                <SelectItem value="M">M - Under 21</SelectItem>
                <SelectItem value="V">V - Veterans (first job)</SelectItem>
                <SelectItem value="X">X - No NI/Under 16</SelectItem>
                <SelectItem value="Z">Z - Under 21 - Deferred</SelectItem>
              </SelectGroup>
              <SelectSeparator />
              <SelectGroup>
                <SelectLabel>Freeports</SelectLabel>
                <SelectItem value="F">F - Standard</SelectItem>
                <SelectItem value="I">I - Married Women & Widows Reduced</SelectItem>
                <SelectItem value="L">L - Deferred</SelectItem>
                <SelectItem value="S">S - Pension Age</SelectItem>
              </SelectGroup>
              <SelectSeparator />
              <SelectGroup>
                <SelectLabel>Investment Zones</SelectLabel>
                <SelectItem value="N">N - Standard</SelectItem>
                <SelectItem value="E">E - Married Women & Widows Reduced</SelectItem>
                <SelectItem value="D">D - Deferred</SelectItem>
                <SelectItem value="K">K - Pension Age</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Employer's NI Contributions */}
      <div className="grid grid-cols-10 gap-2 items-center pt-2">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Employer NI</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="employerNIContributions" 
              checked={employee.employerNIContributions === "Secondary Class 1 NICs are not payable for this employee"}
              onCheckedChange={(checked) => {
                const value = checked 
                  ? "Secondary Class 1 NICs are not payable for this employee"
                  : "";
                handleChange("employerNIContributions", value);
              }}
            />
            <Label htmlFor="employerNIContributions" className="text-sm font-normal">Secondary Class 1 NICs are not payable for this employee</Label>
          </div>
        </div>
      </div>

      {/* Director Status */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Director</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="isDirector" 
              checked={employee.isDirector}
              onCheckedChange={(checked) => handleChange("isDirector", Boolean(checked))}
            />
            <Label htmlFor="isDirector" className="text-sm font-normal">This employee is a director</Label>
          </div>
        </div>
      </div>

      {/* Director-specific fields - only shown when isDirector is true */}
      {employee.isDirector && (
        <>
          {/* Start of directorship */}
          <div className="grid grid-cols-10 gap-2 items-center">
            <Label htmlFor="directorStartDate" className="text-left font-light text-sm col-span-2 justify-self-end">
              Director start date
            </Label>
            <div className="col-span-2">
              <Input
                id="directorStartDate"
                type="date"
                className="h-7 text-sm"
                value={employee.directorStartDate || ""}
                onChange={(e) => handleChange("directorStartDate", e.target.value)}
              />
            </div>
          </div>

          {/* End of directorship */}
          <div className="grid grid-cols-10 gap-2 items-center">
            <Label htmlFor="directorEndDate" className="text-left font-light text-sm col-span-2 justify-self-end">
              Director end date
            </Label>
            <div className="col-span-2">
              <Input
                id="directorEndDate"
                type="date"
                className="h-7 text-sm"
                value={employee.directorEndDate || ""}
                onChange={(e) => handleChange("directorEndDate", e.target.value)}
              />
            </div>
          </div>

          {/* Alternate NI calculation method */}
          <div className="grid grid-cols-10 gap-2 items-center pt-2">
            <Label htmlFor="directorNICalcMethod" className="text-right font-medium col-span-2 justify-self-end mx-4">
              Directors&apos; NI
            </Label>
            <div className="col-span-8">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="directorNICalcMethod" 
                  checked={employee.directorNICalcMethod === "alternate"}
                  onCheckedChange={(checked) => {
                    const value = checked ? "alternate" : "standard";
                    handleChange("directorNICalcMethod", value);
                  }}
                />
                <Label htmlFor="directorNICalcMethod" className="text-sm font-normal">
                  Use alternate method for calculating directors&apos; NICs
                </Label>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Off-Payroll Worker */}
      <div className="grid grid-cols-10 gap-2 items-center">
        <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Off-payroll</Label>
        <div className="col-span-8">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="isOffPayrollWorker" 
              checked={employee.isOffPayrollWorker}
              onCheckedChange={(checked) => handleChange("isOffPayrollWorker", Boolean(checked))}
            />
            <Label htmlFor="isOffPayrollWorker" className="text-sm font-normal">This is an off-payroll worker subject to Chapter 10 Part 2 ITEPA 2003</Label>
          </div>
        </div>
      </div>



      {/* RTI-specific fields */}
      <div className="pt-4 justify-center align-items-center">
        {/* Payroll ID */}
        <div className="grid grid-cols-10 gap-2 items-center">
          <Label htmlFor="payrollId" className="text-left font-medium col-span-2 justify-self-end mx-4">Payroll ID</Label>
          <div className="col-span-4">
            <Input 
              id="payrollId" 
              className="h-7 text-md"
              value={employee.payrollId || ""} 
              onChange={(e) => handleChange("payrollId", e.target.value)}
            />
          </div>
        </div>

        {/* Change of Payroll ID */}
        <div className="grid grid-cols-10 gap-2 items-center pt-2">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Change of Payroll ID</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <RadioGroup 
                  value={employee.changeOfPayrollId || "detect"} 
                  onValueChange={(value) => handleChange("changeOfPayrollId", value)}
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-1">
                    <RadioGroupItem value="detect" id="changeOfPayrollIdDetect" />
                    <Label htmlFor="changeOfPayrollIdDetect" className="text-sm font-normal">Auto Detect</Label>
                  </div>
                  <div className="flex items-center space-x-1">
                    <RadioGroupItem value="force" id="changeOfPayrollIdForce" />
                    <Label htmlFor="changeOfPayrollIdForce" className="text-sm font-normal">Force include change indicator on FPS </Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </div>
        </div>

        {/* Zero Pay Handling */}
        <div className="grid grid-cols-10 gap-2 items-center pt-2">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Zero pay handling</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="zeroPayHandling" 
                checked={employee.zeroPayHandling === "exclude"}
                onCheckedChange={(checked) => {
                  handleChange("zeroPayHandling", checked ? "exclude" : "");
                }}
              />
              <Label htmlFor="zeroPayHandling" className="text-sm font-normal">Do not include employee on FPS if there is no payment</Label>
            </div>
          </div>
        </div>

        {/* Contracted Hours */}
        <div className="grid grid-cols-10 gap-2 items-center pt-4">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Contracted hours</Label>
          <div className="col-span-8">
            <Select 
              value={employee.contractedHours || ""} 
              onValueChange={(value) => handleChange("contractedHours", value)}
            >
              <SelectTrigger className="h-7 text-sm">
                <SelectValue placeholder="Select hours" />
              </SelectTrigger>
              <SelectContent className="text-xs">
                <SelectItem value="not_specified">Not specified</SelectItem>
                <SelectItem value="A">A - less than 16 hours</SelectItem>
                <SelectItem value="B">B - 16 hours or more, but less than 24 hours</SelectItem>
                <SelectItem value="C">C - 24 hours or more, but less than 30 hours</SelectItem>
                <SelectItem value="D">D - 30 hours or more</SelectItem>
                <SelectItem value="E">E - Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Irregular Payment Pattern */}
        <div className="grid grid-cols-10 gap-2 items-center pt-4">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Irregular payment</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="irregularPaymentPattern" 
                checked={employee.irregularPaymentPattern}
                onCheckedChange={(checked) => handleChange("irregularPaymentPattern", Boolean(checked))}
              />
              <Label htmlFor="irregularPaymentPattern" className="text-sm font-normal">Employee is currently on an irregular payment pattern</Label>
            </div>
          </div>
        </div>

        {/* Non-Individual */}
        <div className="grid grid-cols-10 gap-2 items-center pt-2">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Non-individual</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="nonIndividual" 
                checked={employee.nonIndividual}
                onCheckedChange={(checked) => handleChange("nonIndividual", Boolean(checked))}
              />
              <Label htmlFor="nonIndividual" className="text-sm font-normal">Employee&apos;s payments are being made to a body (e.g. personal representative, trustee or corporate organisation)</Label>
            </div>
          </div>
        </div>

        {/* Trivial Commutation Payment */}
        <div className="grid grid-cols-10 gap-2 items-center pt-2">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Trivial commutation</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="trivialCommutation" 
                checked={employee.trivialCommutation}
                onCheckedChange={(checked) => handleChange("trivialCommutation", Boolean(checked))}
              />
              <Label htmlFor="trivialCommutation" className="text-sm font-normal">Include trivial commutation payment declaration</Label>
            </div>
          </div>
        </div>

        {/* Flexible Drawdown */}
        <div className="grid grid-cols-10 gap-2 items-center pt-2">
          <Label className="text-right font-medium col-span-2 justify-self-end mx-4">Flexible drawdown</Label>
          <div className="col-span-8">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="flexibleDrawdown" 
                checked={employee.flexibleDrawdown}
                onCheckedChange={(checked) => handleChange("flexibleDrawdown", Boolean(checked))}
              />
              <Label htmlFor="flexibleDrawdown" className="text-sm font-normal">Include flexible drawdown declaration</Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaxSection;
