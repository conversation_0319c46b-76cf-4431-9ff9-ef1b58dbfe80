# UK Payroll Application - Development Plan & Architecture (Desktop Focus)

## Project Overview

This document outlines the development plan and architecture for a comprehensive UK payroll application designed primarily for bureaus and accountants. The application focuses on a desktop-first approach with optimized performance for managing multiple employers.

### Core Business Requirements

- **Target Users**: Payroll bureaus and accountants managing multiple employers
- **Business Scale**: Support from single-employee businesses to large employers (250+ employees)
- **Deployment Model**: Desktop application with optimized performance
- **Database Architecture**:
  - One SQLite file per employer with custom file extension (`EMPLOYER_DB_EXTENSION` (see src/constants/file.ts))
  - Master database for user preferences, employer list, and global settings management
- **Time to Market**: Initial release focused on complete desktop functionality within 4 months

## System Architecture (Desktop Focus)

### Technology Stack

- **Frontend**: React with Next.js
- **Desktop Wrapper**: Electron
- **UI Framework**: Shadcn UI components with Tailwind CSS
- **Database**:
  - Primary: SQLite (one file per employer) using better-sqlite3
  - ORM: Drizzle ORM with drizzle-zod for schema validation
  - Master Database: SQLite for app settings and employer management
- **State Management and Data Fetching**: 
  - TanStack Query for data fetching, caching, and state management
  - React context with custom hooks for global state
- **Form Handling**: React Hook Form with Zod validation
- **Data Visualization**: TanStack Table for data-intensive views
- **Document Generation**: 
  - PDF: React-PDF
  - Spreadsheets: Excel.js
- **Performance Optimization**: Web Workers for intensive calculations
- **API Integration**: Direct HMRC API integration

### Architectural Principles

1. **Performance-First Approach**:
   - Optimize for desktop performance across all features
   - Use Web Workers for CPU-intensive calculations
   - Implement TanStack Table with virtualization for large datasets
   - Implement TanStack Query for efficient data fetching and caching
   - Optimize database operations for local-first performance
   - Create responsive UI under all conditions

2. **File-Per-Employer Database Architecture**:
   - Isolated SQLite database for each employer
   - Custom file extension (`EMPLOYER_DB_EXTENSION` (see src/constants/file.ts)) and system integration
   - Consistent schema across all employer files
   - Simple file sharing and transfer between users
   - Default storage location with user configuration

3. **Master Database for Application Management**:
   - Track and manage employer file references
   - Store application preferences and settings
   - Maintain recent employer list
   - Store default templates and configurations
   - Simple single-user design

4. **Employer File Management**:
   - Add employers through direct creation, file opening, or scanning
   - Support for transferring employer files between users
   - File integrity validation and backup capabilities
   - Custom file extension integration with operating system
   - User-configurable default file locations

5. **Incremental Database Development**:
   - Start with minimal schema requirements
   - Expand database tables and fields as UI is developed
   - Maintain schema migration capability for updates
   - Focus on flexibility for evolving requirements

## Core Modules

### 1. Master Database Module

A management database to handle application-level concerns:

- **Database Implementation**:
  - SQLite file in user data directory
  - Initial development in project root directory
  - Simple structure focused on application needs

- **Core Functionality**:
  - Employer file tracking and management
  - Recently accessed employer list
  - Application preferences and defaults
  - Report templates and global settings
  - Default file paths configuration

- **Schema Philosophy**:
  - Start with minimal tables for core functionality
  - Add tables and fields incrementally as needed
  - Focus on employer tracking first, expand later

### 2. Employer Database Module

The core data storage system with one SQLite file per employer:

- **Database Implementation**:
  - SQLite file per employer with better-sqlite3
  - Drizzle ORM for type-safe database operations
  - drizzle-zod integration for schema validation
  - Custom `EMPLOYER_DB_EXTENSION` (see src/constants/file.ts) file extension with system integration
  - User-configurable storage location

- **Schema Philosophy**:
  - Start with basic employer and employee tables
  - Add payroll, tax, and calculation tables as UI develops
  - Implement migrations for schema changes
  - Keep design flexible for evolving requirements

- **File Management**:
  - Creation and initialization
  - Opening existing files
  - Import and export functionality
  - Backup and restore operations
  - File integrity validation

### 3. Calculation Engine with Web Workers

A high-performance calculation module handling all UK payroll calculations:

- **Tax Year Configuration**:
  - Separate configuration files per tax year
  - Constants for rates, thresholds, and calculation rules
  - Comprehensive validation and type safety
  - Easy updates for new tax years

- **Web Worker Implementation**:
  - Intensive calculations offloaded to separate thread
  - Progress reporting for long operations
  - Batched processing for multi-employee calculations

- **Calculation Components**:
  - PAYE tax calculation
  - National Insurance contributions
  - Student loan deductions
  - Pension calculations (including auto-enrollment)
  - Statutory payments (SSP, SMP, SPP, etc.)
  - Attachment of earnings orders
  - CIS deductions

### 4. Document Generation

Comprehensive document generation for payroll outputs:

- **React-PDF Implementation**:
  - High-quality payslip templates
  - P60 and P45 generation
  - Custom letter templates
  - Styling and layout controls
  - Batch generation capabilities

- **Excel.js Integration**:
  - Formatted reports with styling and formulas
  - Data export functionality
  - Customizable templates
  - Multi-sheet reports
  - Data visualization with charts

- **Document Storage**:
  - Local file storage
  - User-configurable save locations
  - Document versioning
  - Organized file structure

### 5. HMRC API Integration

Direct integration with HMRC APIs for RTI and other submissions:

- **RTI Submission**:
  - FPS (Full Payment Submission)
  - EPS (Employer Payment Summary)
  - XML generation according to HMRC schemas
  - Response handling and error management
  - Submission queuing for offline operation

- **OAuth Integration**:
  - Secure authentication with HMRC
  - Token management and refresh
  - Scope handling
  - Error recovery
  - Audit logging for all submissions

- **Validation & Compliance**:
  - Pre-submission validation
  - HMRC schema compliance
  - Business rule validation
  - Error reporting and resolution
  - Submission records and status tracking

### 6. User Interface Components

Comprehensive UI components optimized for desktop:

- **Data Tables with TanStack Table**:
  - Virtualized high-performance tables
  - Sorting, filtering, and pagination
  - Responsive design for all screen sizes
  - Custom cell renderers
  - Export functionality

- **Form Components**:
  - React Hook Form integration
  - Zod validation schemas
  - UK-specific field validations
  - Responsive layout
  - Accessibility compliance

- **Dashboard Components**:
  - Employer listing and management
  - Key performance indicators
  - Data visualization
  - Activity summaries
  - Action items and alerts

- **Desktop UI Optimization**:
  - Desktop window controls
  - Native desktop menus
  - Keyboard shortcuts
  - Context menus
  - Responsive layouts for various screen sizes

### 7. TanStack Query Integration

Optimized data fetching and caching for improved performance:

- **Core Integration**:
  - Client-side caching of database queries
  - Automatic background refreshing of stale data
  - Deduplication of redundant queries
  - Optimistic UI updates for write operations

- **Key Features**:
  - Persistent caching for frequently accessed data
  - Automatic invalidation on data changes
  - Loading and error state management
  - Pagination and infinite scrolling support

- **Performance Benefits**:
  - Reduced database load in high-concurrency situations
  - Faster UI transitions between screens
  - Improved responsiveness for data-intensive views
  - Efficient query sharing across components

### 8. Employer and Employee Management

Comprehensive management of employer and employee data:

- **Employer Management**:
  - Company setup and configuration
  - HMRC registration and settings
  - Department and cost center management
  - Pay element configuration
  - Multi-employer operations

- **Employee Management**:
  - Personnel record management
  - Employment history
  - Tax and NI configuration
  - Bank details with encryption
  - Document storage and history

- **Bulk Operations**:
  - Mass updates of common fields
  - Importing employees from CSV/Excel
  - Template-based employee creation
  - Batch processing
  - Data validation and error handling

### 9. Payroll Processing

Core payroll processing functionality:

- **Pay Period Management**:
  - Pay period creation and configuration
  - Pay calendar and schedule management
  - Flexible pay frequencies
  - Period locking and control
  - Historical period management

- **Payroll Run**:
  - Individual and batch processing
  - Pre-processing validation
  - Override capabilities
  - Calculation audit trail
  - Payslip preview and adjustment

- **Payments**:
  - BACS file generation
  - Payment reconciliation
  - Payment status tracking
  - Bank account validation
  - Remittance advice generation

### 10. Reporting and Analytics

Comprehensive reporting capabilities:

- **Standard Reports**:
  - Payroll summaries
  - Departmental analysis
  - Year-to-date totals
  - Employer costs
  - Liability reports

- **Custom Reports**:
  - Report designer/customization
  - Saved report templates
  - Export to multiple formats
  - Scheduled report generation
  - Parameter-driven reports

- **Analytics Dashboard**:
  - Cost analysis
  - Trend visualization
  - Payroll metrics
  - Comparative analysis
  - Forecasting tools

## Database Architecture

### Master Database Design

- **Implementation**:
  - SQLite file in user data directory (initially in project root)
  - Simple schema focused on current needs
  - Expandable for future requirements

- **Initial Tables**:
  - Employers: Track employer files and metadata
  - Settings: Application settings and preferences
  - RecentActivity: Recently accessed employers
  - Templates: Default templates for new employers/employees

- **Schema Philosophy**:
  - Start with minimal required tables
  - Expand incrementally as UI is developed
  - Focus on core functionality first

### Employer Database Design

- **Single SQLite File Design**:
  - SQLite WAL journal mode for better concurrency
  - Designed for efficient querying patterns
  - Minimal initial schema, expanded during development
  - Scalable to support large employee counts

- **Initial Tables**:
  - Employer: Company details and configuration
  - Employees: Personnel records
  - Departments: Organizational structure

- **Schema Philosophy**:
  - Build tables as UI and features are developed
  - Focus on flexibility and extensibility
  - Implement migrations for schema changes

### File Management

- **Employer File Handling**:
  - Create new employer files
  - Open existing employer files
  - Scan for employer files in default location
  - Add external employer files to application
  - Share and transfer between users

- **File System Integration**:
  - Custom file extension (`EMPLOYER_DB_EXTENSION` (see src/constants/file.ts))
  - File association with application
  - Default save locations
  - File backup and restore
  - Integrity validation

### TanStack Query Implementation

- **Query Caching**:
  - Client-side caching to reduce database load
  - Cache invalidation strategy
  - Optimistic updates for write operations
  - Proper query key organization

- **Key Benefits**:
  - Reduced database access
  - Improved UI responsiveness
  - Consistent loading states
  - Automatic data synchronization
  - Efficient handling of stale data

## Desktop Application Features

- **Performance Optimization**:
  - SQLite configuration for optimal performance
  - Efficient data loading and caching with TanStack Query
  - UI virtualization for large datasets
  - Background processing for intensive tasks
  - Memory management for large operations

- **Native Integration**:
  - Custom file association for ``EMPLOYER_DB_EXTENSION` (see src/constants/file.ts)` files
  - Customized window chrome
  - Native file pickers and dialogs
  - System tray integration
  - Native notifications

- **File System Integration**:
  - Document export and import
  - Local file management
  - Backup and restore functionality
  - External software integration
  - Secure file handling

- **Employer File Management**:
  - Create new employer files
  - Open existing files via dialog
  - Direct open via file association
  - Scan default location for new files
  - Manual addition of external files
  - Easy transfer between users

## Implementation Strategy

### Phase 1: Core Infrastructure (Weeks 1-4)

- Set up Next.js with Electron integration
- Implement better-sqlite3 with Drizzle ORM
- Create master database with basic schema
- Implement employer database structure
- Set up custom file extension and associations
- Create file system utilities for desktop

### Phase 2: Employer Management & Dashboard (Weeks 5-8)

- Implement employer creation workflow
- Create employer dashboard and selection UI
- Develop employer list functionality with file scanning
- Build employer file opening and management
- Implement employee data schemas
- Create basic employer settings
- Develop import/export functionality for employer files
- Implement TanStack Query for employer and employee data

### Phase 3: Core Calculation Engine (Weeks 9-12)

- Implement tax year configurations
- Develop PAYE calculation module with Web Workers
- Create National Insurance calculations
- Implement pension calculations
- Develop statutory payments calculations
- Build student loan deduction functionality
- Create comprehensive validation rules

### Phase 4: HMRC Integration & Documents (Weeks 13-16)

- Implement HMRC API integration
- Develop RTI submission capability
- Create document generation with React-PDF
- Implement Excel.js for reporting
- Build TanStack Table for data views
- Develop payslip generation
- Implement P60 and P45 generation

### Phase 5: Complete Desktop Application (Weeks 17-20)

- Finalize payroll processing workflow
- Implement end-to-end payroll runs
- Create comprehensive reporting
- Build dashboard views
- Comprehensive testing and optimization
- Prepare for distribution and deployment

### Future Phases (Post-Initial Release)

- Implement licensing and activation system
- Develop user authentication
- Create cloud data synchronization
- Build employer and employee portals
- Implement multi-device support

## Testing Strategy

- **Unit Testing**:
  - Comprehensive tests for calculation engine
  - Validation of tax rules across tax years
  - Testing of utility functions
  - Component testing with React Testing Library
  - Drizzle schema validation tests

- **Integration Testing**:
  - Database operation validation
  - File handling validation
  - Employer file management testing
  - UI workflow testing
  - Desktop integration testing

- **End-to-End Testing**:
  - Complete workflow testing
  - UI automation testing
  - Performance benchmarking
  - File system integration testing
  - Calculation accuracy validation

## Conclusion

This desktop-focused approach allows the UK Payroll Application to deliver optimal performance and user experience for accountants and payroll bureaus. By prioritizing desktop functionality first with a clear path for future extensions, the application will provide a robust, compliant solution for payroll processing built on modern technologies and efficient architecture.

The file-per-employer SQLite architecture provides excellent isolation, performance, and portability while making it easy for users to share and transfer data. The integration of TanStack Query for data fetching and caching enhances performance and user experience, creating a responsive application even with large datasets.

This development plan provides a clear roadmap for creating a sophisticated UK payroll solution with a pragmatic approach to incremental development.
