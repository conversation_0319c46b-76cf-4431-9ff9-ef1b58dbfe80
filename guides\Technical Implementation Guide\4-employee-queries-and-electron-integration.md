# UK Payroll Application - Technical Guide Part 4: Employee Queries and Electron Integration

## TanStack Query Hooks for Employees

These hooks provide a clean interface for querying and mutating employee data with TanStack Query.

```typescript
// src/hooks/use-query/employees.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEmployerDatabase } from '@/hooks/use-employer-database';
import { employees } from '@/db/schema/employer';
import { eq } from 'drizzle-orm';

// Get all employees for an employer
export function useEmployees(employerId: string | null) {
  const { db } = useEmployerDatabase();
  
  return useQuery({
    queryKey: ['employees', employerId],
    queryFn: async () => {
      if (!employerId || !db) throw new Error('Employer ID or database not available');
      
      return await db.select().from(employees);
    },
    enabled: !!employerId && !!db,
  });
}

// Get a single employee
export function useEmployee(employerId: string | null, employeeId: string | null) {
  const { db } = useEmployerDatabase();
  
  return useQuery({
    queryKey: ['employee', employerId, employeeId],
    queryFn: async () => {
      if (!employerId || !employeeId || !db) 
        throw new Error('Employer ID, Employee ID, or database not available');
      
      const result = await db.select()
        .from(employees)
        .where(eq(employees.id, employeeId))
        .limit(1);
      
      if (result.length === 0) throw new Error('Employee not found');
      return result[0];
    },
    enabled: !!employerId && !!employeeId && !!db,
  });
}

// Create an employee
export function useCreateEmployee(employerId: string | null) {
  const queryClient = useQueryClient();
  const { db } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: async (data: any) => {
      if (!employerId || !db) throw new Error('Employer ID or database not available');
      
      const result = await db.insert(employees)
        .values({
          id: crypto.randomUUID(),
          ...data,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();
      
      return result[0];
    },
    onSuccess: (data) => {
      // Invalidate the employees list query
      queryClient.invalidateQueries({ queryKey: ['employees', employerId] });
      
      // Add the new employee to the cache
      queryClient.setQueryData(['employee', employerId, data.id], data);
    },
  });
}

// Update an employee
export function useUpdateEmployee(employerId: string | null) {
  const queryClient = useQueryClient();
  const { db } = useEmployerDatabase();
  
  return useMutation({
    mutationFn: async ({ id, ...data }: { id: string, [key: string]: any }) => {
      if (!employerId || !db) throw new Error('Employer ID or database not available');
      
      const result = await db.update(employees)
        .set({
          ...data,
          updatedAt: new Date(),
        })
        .where(eq(employees.id, id))
        .returning();
      
      return result[0];
    },
    onSuccess: (data) => {
      // Invalidate the specific employee query
      queryClient.invalidateQueries({ queryKey: ['employee', employerId, data.id] });
      
      // Update the employees list in the cache
      queryClient.setQueryData(['employees', employerId], (old: any[]) => {
        return old?.map(employee => employee.id === data.id ? data : employee) || [];
      });
    },
  });
}
```

## Electron Integration for File Handling

### File Association Setup

Configure Electron to handle `.ukpayroll` files properly.

```typescript
// electron-builder.json
{
  "appId": "com.yourcompany.ukpayroll",
  "productName": "UK Payroll",
  "directories": {
    "output": "dist"
  },
  "files": [
    "dist/**/*",
    "package.json"
  ],
  "fileAssociations": [
    {
      "ext": "ukpayroll",
      "name": "UK Payroll Employer Database",
      "description": "UK Payroll Employer Database",
      "icon": "icons/employer"
    }
  ],
  "win": {
    "target": [
      "nsis"
    ],
    "icon": "icons/icon.ico"
  },
  "mac": {
    "target": [
      "dmg"
    ],
    "icon": "icons/icon.icns",
    "category": "public.app-category.finance"
  },
  "linux": {
    "target": [
      "AppImage"
    ],
    "icon": "icons/icon.png",
    "category": "Office;Finance"
  }
}
```

### File Handling Implementation

```typescript
// src/electron/file-handlers/index.ts
import { app, BrowserWindow } from 'electron';
import path from 'path';
import { existsSync } from 'fs';

export function registerFileHandlers(mainWindow: BrowserWindow) {
  // Handle file open events (macOS)
  app.on('open-file', (event, filePath) => {
    event.preventDefault();
    handleFileOpen(mainWindow, filePath);
  });
  
  // Handle file open from command line args (Windows/Linux)
  const gotTheLock = app.requestSingleInstanceLock();
  
  if (!gotTheLock) {
    app.quit();
  } else {
    app.on('second-instance', (event, commandLine) => {
      // Someone tried to run a second instance, focus our window instead
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.focus();
        
        // Check if there's a file path in the command line args
        const filePath = commandLine.find(arg => arg.endsWith('.ukpayroll'));
        if (filePath) {
          handleFileOpen(mainWindow, filePath);
        }
      }
    });
    
    // Check if we were launched with a file
    if (process.platform === 'win32' || process.platform === 'linux') {
      const filePath = process.argv.find(arg => arg.endsWith('.ukpayroll'));
      if (filePath) {
        handleFileOpen(mainWindow, filePath);
      }
    }
  }
}

function handleFileOpen(mainWindow: BrowserWindow, filePath: string) {
  if (!existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);
    mainWindow.webContents.send('file-open-error', {
      error: 'File not found',
      path: filePath
    });
    return;
  }
  
  // Check if it's a valid employer file (just a basic check)
  if (!filePath.endsWith('.ukpayroll')) {
    console.error(`Invalid file type: ${filePath}`);
    mainWindow.webContents.send('file-open-error', {
      error: 'Invalid file type',
      path: filePath
    });
    return;
  }
  
  // Send to renderer
  mainWindow.webContents.send('file-open-request', {
    path: filePath
  });
  
  // Ensure window is visible
  if (mainWindow.isMinimized()) {
    mainWindow.restore();
  }
  mainWindow.focus();
}
```

### Preload Script for File Handling

```typescript
// src/electron/preload/index.ts
import { contextBridge, ipcRenderer } from 'electron';

// Expose safe APIs to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: (options: any) => ipcRenderer.invoke('file:open', options),
  saveFile: (data: any, options: any) => ipcRenderer.invoke('file:save', data, options),
  
  // Get system paths
  getPath: (name: string) => ipcRenderer.invoke('app:getPath', name),
  
  // System information
  getPlatform: () => process.platform,
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Events
  onFileOpenRequest: (callback: (event: any, fileInfo: any) => void) => {
    ipcRenderer.on('file-open-request', (event, fileInfo) => callback(event, fileInfo));
    return () => ipcRenderer.removeListener('file-open-request', callback);
  },
  onFileOpenError: (callback: (event: any, error: any) => void) => {
    ipcRenderer.on('file-open-error', (event, error) => callback(event, error));
    return () => ipcRenderer.removeListener('file-open-error', callback);
  }
});
```

## Main Entry Points

### Dashboard Page Implementation with TanStack Query

```tsx
// src/app/dashboard/page.tsx
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { PlusCircle, FolderOpen, Import, Scan } from 'lucide-react';
import { useRecentEmployers } from '@/hooks/use-query/employers';
import { useOpenEmployer, useScanForEmployers } from '@/hooks/use-query/employers';
import { EmployerCard } from '@/components/dashboard/employer-card';
import { getEmployerDbAdapter } from '@/db/adapters/employer';

export default function DashboardPage() {
  const router = useRouter();
  
  // Use TanStack Query for data fetching
  const { 
    data: recentEmployers = [], 
    isLoading, 
    error 
  } = useRecentEmployers(10);
  
  const openEmployerMutation = useOpenEmployer();
  const scanForEmployersMutation = useScanForEmployers();
  
  // Listen for file open requests (from file association)
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      const removeListener = window.electronAPI.onFileOpenRequest(async (event, fileInfo) => {
        try {
          // Add the file to our system and open it
          const adapter = getEmployerDbAdapter();
          const result = await adapter.addExistingFile(fileInfo.path);
          
          await openEmployerMutation.mutateAsync(result.id);
          router.push(`/employers/${result.id}`);
        } catch (error) {
          console.error('Failed to open file:', error);
        }
      });
      
      return removeListener;
    }
  }, [router, openEmployerMutation]);
  
  const handleEmployerClick = async (employerId: string) => {
    try {
      await openEmployerMutation.mutateAsync(employerId);
      router.push(`/employers/${employerId}`);
    } catch (error) {
      console.error('Failed to open employer:', error);
    }
  };
  
  const handleCreateEmployer = () => {
    router.push('/employers/create');
  };
  
  const handleOpenEmployer = async () => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      try {
        const result = await window.electronAPI.openFile({
          title: 'Open Employer Database',
          filters: [
            { name: 'UK Payroll Files', extensions: ['ukpayroll'] },
            { name: 'All Files', extensions: ['*'] }
          ]
        });
        
        if (result && !result.canceled && result.filePaths.length > 0) {
          // Add the selected file to our system
          const adapter = getEmployerDbAdapter();
          const employer = await adapter.addExistingFile(result.filePaths[0]);
          
          // Open the employer
          await openEmployerMutation.mutateAsync(employer.id);
          router.push(`/employers/${employer.id}`);
        }
      } catch (error) {
        console.error('Failed to open employer:', error);
      }
    }
  };
  
  const handleScanForEmployers = async () => {
    try {
      const result = await scanForEmployersMutation.mutateAsync();
      console.log('Found employers:', result);
      // Optionally show a notification with the count of found employers
    } catch (error) {
      console.error('Failed to scan for employers:', error);
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <div className="flex gap-4">
          <Button 
            variant="default" 
            size="sm" 
            onClick={handleCreateEmployer}
            className="flex items-center gap-2"
          >
            <PlusCircle size={16} />
            <span>Create New</span>
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleOpenEmployer}
            className="flex items-center gap-2"
          >
            <FolderOpen size={16} />
            <span>Open File</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleScanForEmployers}
            disabled={scanForEmployersMutation.isPending}
            className="flex items-center gap-2"
          >
            <Scan size={16} />
            <span>{scanForEmployersMutation.isPending ? 'Scanning...' : 'Scan for Files'}</span>
          </Button>
        </div>
      </div>
      
      <div className="bg-slate-100 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Recent Employers</h2>
        {isLoading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-md text-red-800">
            Failed to load recent employers
          </div>
        ) : recentEmployers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentEmployers.map((employer) => (
              <EmployerCard
                key={employer.id}
                employer={employer}
                onClick={() => handleEmployerClick(employer.id)}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded p-6 text-center">
            <p className="text-slate-500 mb-4">You haven't opened any employers yet.</p>
            <Button onClick={handleCreateEmployer}>Create Your First Employer</Button>
          </div>
        )}
      </div>
    </div>
  );
}
```
