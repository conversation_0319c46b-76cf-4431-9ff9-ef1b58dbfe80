"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { EmployersTab } from "@/components/dashboard/employers-tab";
import { PreferencesTab } from "@/components/dashboard/preferences-tab";
import { DefaultsTab } from "@/components/dashboard/defaults-tab";
import { UsersTab } from "@/components/dashboard/users-tab";
import { AnalyticsTab } from "@/components/dashboard/analytics-tab";
import { AutomationTab } from "@/components/dashboard/automation-tab";
import { PortalsTab } from "@/components/dashboard/portals-tab";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Bell } from "lucide-react";

const navigationSections = [
  { id: "employers", label: "Employers" },
  { id: "preferences", label: "Preferences" },
  { id: "defaults", label: "Defaults" },
  { id: "users", label: "User Management" },
  { id: "analytics", label: "Analytics" },
  { id: "automation", label: "Automation" },
  { id: "portals", label: "Portals" },
];

export function Dashboard() {
  const [activeSection, setActiveSection] = useState<string>("employers");
  const [isVisible, setIsVisible] = useState(false);

  // Effect for initial fade-in on page load
  useEffect(() => {
    // Short delay to ensure component is mounted before animation
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    return () => clearTimeout(timer);
  }, []);

  const handleSectionChange = (sectionId: string) => {
    // First hide the current content
    setIsVisible(false);

    // After a short delay, change the section and make it visible
    setTimeout(() => {
      setActiveSection(sectionId);
      setIsVisible(true);
    }, 100); // Short delay for the fade-out to complete
  };

  // Function to render the active section
  const renderActiveSection = () => {
    switch (activeSection) {
      case "employers":
        return <EmployersTab />;
      case "preferences":
        return <PreferencesTab />;
      case "defaults":
        return <DefaultsTab />;
      case "users":
        return <UsersTab />;
      case "analytics":
        return <AnalyticsTab />;
      case "automation":
        return <AutomationTab />;
      case "portals":
        return <PortalsTab />;
      default:
        return <EmployersTab />;
    }
  };

  return (
    <div className="flex flex-col">
      <div className="flex flex-col">
        {/* Navigation and user controls in grid layout */}
        <div className="grid grid-cols-12 items-center border-b-2 pt-4 pb-3">
          {/* Navigation buttons - takes up most of the space */}
          <div className="col-span-10 flex gap-2">
            {navigationSections.map((section) => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "ghost"}
                size="sm"
                onClick={() => handleSectionChange(section.id)}
                className={
                  activeSection === section.id
                    ? "font-normal"
                    : "font-normal hover:bg-slate-200"
                }
              >
                {section.label}
              </Button>
            ))}
          </div>

          {/* User controls - at the end of the line */}
          <div className="col-span-2 flex items-center justify-end gap-2">
            <Button variant="outline" size="sm">
              <Bell className="mr-2 h-4 w-4" />
              Notifications
            </Button>
          </div>
        </div>

        {/* Content area - shows active section with overflow at page level */}
        <div className="h-[calc(100vh-125px)] overflow-auto pt-1">
          <div
            className={`transition-opacity duration-200 ${isVisible ? "opacity-100" : "opacity-0"}`}
          >
            {renderActiveSection()}
          </div>
        </div>
      </div>
    </div>
  );
}
