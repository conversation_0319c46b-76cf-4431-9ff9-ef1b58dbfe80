import { dialog } from "electron";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import { getMasterDb } from "../../drizzle/adapters/master";
import { getEmployerDbAdapter } from "../../drizzle/adapters/employerDbAdapter";
import { employers } from "../../drizzle/schema/master";
import { eq } from "drizzle-orm";
import { EMPLOYER_DB_EXTENSION } from "../../constants/file";

/**
 * Add an existing employer DB file to the master DB.
 * @param filePath Path to the employer DB file.
 * @returns Result of the operation.
 */
export async function addExistingEmployer(filePath: string) {
  try {
    // Validate file exists
    if (!fs.existsSync(filePath)) {
      return {
        success: false,
        error: "File does not exist.",
      };
    }

    // Validate file extension
    if (!filePath.endsWith(EMPLOYER_DB_EXTENSION)) {
      return {
        success: false,
        error: `Invalid file extension. Expected ${EMPLOYER_DB_EXTENSION}.`,
      };
    }

    // Always resolve to absolute path (user can pick any location)
    const absolutePath = path.isAbsolute(filePath)
      ? filePath
      : path.resolve(process.cwd(), filePath);
    console.log(`[addExistingEmployer] Using employer DB path:`, absolutePath);

    // Check if file is already in the master DB
    const db = getMasterDb();
    const existingEmployers = await db
      .select()
      .from(employers)
      .where(eq(employers.file_path, absolutePath))
      .all();

    if (existingEmployers.length > 0) {
      return {
        success: false,
        error: "This employer file is already in the database.",
      };
    }

    // Open the employer DB to get the name
    const { db: employerDb, client } = getEmployerDbAdapter(absolutePath);

    try {
      // Query the employer table to get the name
      const result = await client.execute({
        sql: "SELECT id, name FROM employer LIMIT 1",
      });

      if (result.rows.length === 0) {
        return {
          success: false,
          error: "Invalid employer database file. No employer data found.",
        };
      }

      const employerId = (result.rows[0].id as string) || uuidv4();
      const employerName = result.rows[0].name as string;

      // Extract file name from path (without extension) to use as display name
      const fileName = path.basename(filePath, EMPLOYER_DB_EXTENSION);

      // Add to master DB
      await db.insert(employers).values({
        id: employerId,
        display_name: fileName, // Use file name instead of employer name
        file_path: absolutePath,
        status: "open",
        created_at: new Date(),
        updated_at: new Date(),
        last_opened_at: new Date(),
      });

      return {
        success: true,
        employer: {
          id: employerId,
          name: fileName, // Use file name as the display name
          filePath,
        },
      };
    } finally {
      client.close();
    }
  } catch (error) {
    console.error("Error adding existing employer:", error);
    return {
      success: false,
      error: `Failed to add employer: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Remove an employer from the master DB.
 * @param employerId ID of the employer to remove.
 * @returns Result of the operation.
 */
export async function removeEmployer(employerId: string) {
  try {
    const db = getMasterDb();

    // Check if employer exists
    const existingEmployers = await db
      .select()
      .from(employers)
      .where(eq(employers.id, employerId))
      .all();

    if (existingEmployers.length === 0) {
      return {
        success: false,
        error: "Employer not found.",
      };
    }

    // Remove from master DB
    await db.delete(employers).where(eq(employers.id, employerId));

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error removing employer:", error);
    return {
      success: false,
      error: `Failed to remove employer: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Open a file picker dialog to select one or more employer DB files.
 * @returns Array of selected file paths or null if cancelled.
 */
export async function openEmployerFilePicker() {
  const result = await dialog.showOpenDialog({
    properties: ["openFile", "multiSelections"],
    filters: [
      {
        name: "Employer Database",
        extensions: [EMPLOYER_DB_EXTENSION.substring(1)],
      },
      { name: "All Files", extensions: ["*"] },
    ],
    title: "Select Employer Database File(s)",
  });

  if (result.canceled || result.filePaths.length === 0) {
    return null;
  }

  return result.filePaths;
}
