/**
 * UK Tax Year Configuration 2023-2024
 * Based on HMRC official figures from PAYErout-v23-0
 */

import { TaxYearConfig } from './types';

/**
 * Tax year configuration for 2024-2025
 */
export const TaxYear2024_2025: TaxYearConfig = {
  id: '2024-2025',
  name: '2024/25',
  startDate: '2024-04-06',
  endDate: '2025-04-05',
  
  paye: {
    personalAllowance: 12570,
    personalAllowanceReductionThreshold: 100000,
    maxRegulatoryLimit: 0.5, // 50%
    
    // G pointers for tax rate identification (as per PAYErout Appendices A/B/C)
    G: 1,  // Basic rate (index for basic-rate in standardRates)
    G1: 2, // Higher rate (index for higher-rate in standardRates)
    G2: 3, // Additional rate (index for additional-rate in standardRates)
    
    // Standard UK rates
    standardRates: [
      {
        id: 'starting-rate',
        name: 'Starting Rate for Savings',
        rate: 0.0,
        threshold: 0
      },
      {
        id: 'basic-rate',
        name: 'Basic Rate',
        rate: 0.2,
        threshold: 0
      },
      {
        id: 'higher-rate',
        name: 'Higher Rate',
        rate: 0.4,
        threshold: 37700
      },
      {
        id: 'additional-rate',
        name: 'Additional Rate',
        rate: 0.45,
        threshold: 125140
      }
    ],
    
    // Scottish rates
    scottishRates: [
      {
        id: 'scottish-starter-rate',
        name: 'Scottish Starter Rate',
        rate: 0.19,
        threshold: 0
      },
      {
        id: 'scottish-basic-rate',
        name: 'Scottish Basic Rate',
        rate: 0.2,
        threshold: 2162
      },
      {
        id: 'scottish-intermediate-rate',
        name: 'Scottish Intermediate Rate',
        rate: 0.21,
        threshold: 13118
      },
      {
        id: 'scottish-higher-rate',
        name: 'Scottish Higher Rate',
        rate: 0.42,
        threshold: 31092
      },
      {
        id: 'scottish-top-rate',
        name: 'Scottish Top Rate',
        rate: 0.47,
        threshold: 125140
      }
    ],
    
    // Welsh rates (currently the same as UK rates but with different names)
    welshRates: [
      {
        id: 'WR1',
        name: 'Welsh Basic Rate (WR1)',
        rate: 0.2,
        threshold: 0
      },
      {
        id: 'WR2',
        name: 'Welsh Higher Rate (WR2)',
        rate: 0.4,
        threshold: 37700
      },
      {
        id: 'WR3',
        name: 'Welsh Additional Rate (WR3)',
        rate: 0.45,
        threshold: 125140
      }
    ]
  },
  
  nationalInsurance: {
    // Common thresholds across all categories
    lowerEarningsLimit: 6396,
    freeportsUpperSecondaryThreshold: 25000,
    investmentZonesUpperSecondaryThreshold: 25000,
    terminationAwardThreshold: 30000,
    sportingTestimonialThreshold: 100000,
    class1ARate: 0.138,
    
    categories: [
      {
        category: 'A',
        description: 'Standard rate for employees',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0.138
      },
      {
        category: 'B',
        description: 'Married women\'s reduced rate',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.0585,
        upperRate: 0.02,
        employerRate: 0.138
      },
      {
        category: 'C',
        description: 'Employer only',
        primaryThreshold: 0,
        secondaryThreshold: 9100,
        upperEarningsLimit: 0,
        primaryRate: 0,
        upperRate: 0,
        employerRate: 0.138
      },
      {
        category: 'F',
        description: 'Standard rate for employees in Freeports',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate up to FUST
      },
      {
        category: 'I',
        description: 'Married women\'s reduced rate in Freeports',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.0185,
        upperRate: 0.02,
        employerRate: 0 // Zero rate up to FUST
      },
      {
        category: 'S',
        description: 'Standard rate for employees in Investment Zones',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate up to IZUST
      },
      {
        category: 'L',
        description: 'Married women\'s reduced rate in Investment Zones',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.0185,
        upperRate: 0.02,
        employerRate: 0 // Zero rate up to IZUST
      },
      {
        category: 'H',
        description: 'Apprentice under 25',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate for employer
      },
      {
        category: 'J',
        description: 'Under 21',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate for employer
      },
      {
        category: 'M',
        description: 'Under 21 deferment',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate for employer
      },
      {
        category: 'V',
        description: 'Veterans',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.08,
        upperRate: 0.02,
        employerRate: 0 // Zero rate for veterans
      },
      {
        category: 'Z',
        description: 'Under 21 married women',
        primaryThreshold: 12570,
        secondaryThreshold: 9100,
        upperEarningsLimit: 50270,
        primaryRate: 0.0185,
        upperRate: 0.02,
        employerRate: 0 // Zero rate for employer
      }
    ]
  },
  
  studentLoan: {
    plan1Threshold: 22015,
    plan2Threshold: 27295,
    plan4Threshold: 27660,
    plan5Threshold: 25000,
    postgraduateThreshold: 21000,
    repaymentRate: 0.09,
    postgraduateRepaymentRate: 0.06
  },
  
  statutory: {
    ssp: {
      weeklyRate: 116.75,
      lowerEarningsLimit: 123.00,
      waitingDays: 3,
      maxWeeksPayable: 28
    },
    smp: {
      weeklyRate: 184.03,
      lowerEarningsLimit: 123.00,
      higherRatePercentage: 0.9,
      higherRateWeeks: 6,
      maxWeeksPayable: 39
    },
    spp: {
      weeklyRate: 184.03,
      lowerEarningsLimit: 123.00,
      maxWeeksPayable: 2
    },
    sap: {
      weeklyRate: 184.03,
      lowerEarningsLimit: 123.00,
      higherRatePercentage: 0.9,
      higherRateWeeks: 6,
      maxWeeksPayable: 39
    },
    spbp: {
      weeklyRate: 184.03,
      lowerEarningsLimit: 123.00,
      maxWeeksPayable: 2
    },
    shpp: {
      weeklyRate: 184.03,
      lowerEarningsLimit: 123.00,
      maxWeeksPayable: 37
    }
  },
  
  pension: {
    // Values for 2024-2025 tax year based on UK The Pensions Regulator guidelines
    lowerEarningsThreshold: 6396,   // Lower level for qualifying earnings (increased from 2023-2024)
    upperEarningsThreshold: 50270,  // Upper level for qualifying earnings
    earningsTrigger: 10000,         // Earnings trigger for auto-enrollment
    minimumEmployerContribution: 0.03, // 3% minimum employer contribution
    minimumTotalContribution: 0.08  // 8% minimum total contribution
  }
};

/**
 * Get tax year configuration by ID
 * @param taxYearId Tax year ID (e.g., "2023-2024")
 * @returns The tax year configuration or undefined if not found
 */
export function getTaxYearConfig(taxYearId: string): TaxYearConfig | undefined {
  // In a real implementation, this would look up the config from a database
  // or config file based on the tax year ID
  if (taxYearId === '2024-2025') {
    return TaxYear2024_2025;
  }
  
  return undefined;
}

/**
 * Get the current tax year configuration
 * @returns The current tax year configuration
 */
export function getCurrentTaxYearConfig(): TaxYearConfig {
  // In a production environment, this would determine the current tax year
  // based on the current date and return the appropriate config
  return TaxYear2024_2025;
}
