// Define employee type for type safety
export type BatchEmployee = {
  id: string;
  name: string;
  status: string;
  salary: number;
  hourlyRate: number;
  hours: number;
  bonus: number;
  commission: number;
  deduction: number;
  pensionEe: number;
  pensionEr: number;
  onEmployeeSelect?: (employeeId: string) => void; // Optional function for employee selection
  [key: string]: string | number | undefined | ((employeeId: string) => void); // Index signature for dynamic access
};

// Available columns for batch editing
export const availableColumns = [
  { id: "salary", name: "Salary" },
  { id: "hourlyRate", name: "Hourly Rate" },
  { id: "hours", name: "Hours" },
  { id: "bonus", name: "Bonus" },
  { id: "commission", name: "Commission" },
  { id: "deduction", name: "Deduction" },
  { id: "pensionEe", name: "Employee Pension" },
  { id: "pensionEr", name: "Employer Pension" },
];

export interface BatchEditorProps {
  periodId: string;
  activeView?: string;
  onSwitchToOverview?: () => void;
  onSwitchToBatch?: () => void;
  onEmployeeSelect?: (employeeId: string) => void;
  // Props for persisting state
  initialStatusFilter?: "all" | "open" | "closed";
  onStatusFilterChange?: (filter: "all" | "open" | "closed") => void;
  initialSelectedColumns?: string[];
  onSelectedColumnsChange?: (columns: string[]) => void;
  initialSorting?: { id: string; desc: boolean }[];
  onSortingChange?: (sorting: { id: string; desc: boolean }[]) => void;
}

// Define a type for the cell change handler to ensure consistency
export type CellChangeHandler = (employeeId: string, columnId: string, value: number | undefined) => void;
