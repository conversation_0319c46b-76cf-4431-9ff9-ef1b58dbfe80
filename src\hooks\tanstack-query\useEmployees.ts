import { useQuery } from '@tanstack/react-query';
import { useEmployerDBContext } from '@/providers/employer-db-provider';
import { employee } from '@/drizzle/schema/employer/employee.schema';

// Infer Employee type from Drizzle schema
type Employee = typeof employee.$inferSelect;

export function useEmployeesQuery() {
  const ctx = useEmployerDBContext();
  const db = ctx?.openEmployers.find(e => e.id === ctx.activeEmployerId)?.dbInstance as any;

  return useQuery<Employee[]>({
    queryKey: ['employees', ctx?.activeEmployerId],
    queryFn: async () => {
      if (!db) return [];
      return db.select().from(employee);
    },
    enabled: !!db,
    staleTime: 5 * 60 * 1000,
  });
}
