"use client";

import React, { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { CalendarRange, Calendar } from "lucide-react";

interface PayslipCalculations {
  taxablePay: number;
  tax: number;
  studentLoan: number;
  postgraduateLoan: number;
  employeeNIC: number;
  employerNIC: number;
  netPay: number;
  additionsToNetPay: number;
  deductionsFromNetPay: number;
  employeePension: number;
  employerPension: number;
  takeHomePay: number;
  employerCost: number;
}

interface YearToDateTotals {
  gross: number;
  taxablePay: number;
  tax: number;
  nationalInsurance: number;
  employeePension: number;
  employerPension: number;
  netPay: number;
  studentLoan?: number;
  postgraduateLoan?: number;
  employerNIC?: number;
  additionsToNetPay?: number;
  deductionsFromNetPay?: number;
  takeHomePay?: number;
  employerCost?: number;
}

interface PayslipSummaryProps {
  calculations: PayslipCalculations;
  ytd: YearToDateTotals;
}

// Format currency for display
const formatCurrency = (value: number): string => {
  return `£${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
};

const PayslipSummary: React.FC<PayslipSummaryProps> = ({
  calculations,
  ytd,
}) => {
  const [showYTD, setShowYTD] = useState(false);

  return (
    <div className="space-y-2">
      <div className="bg-card rounded-lg p-2">
        <div className="text-[13px]">
          {/* Header row */}
          <div className="mb-4 flex items-center justify-center">
            <div className="flex space-x-2">
              <Button
                variant={showYTD ? "outline" : "default"}
                size="sm"
                className="h-7 text-xs"
                onClick={() => setShowYTD(false)}
              >
                <Calendar className="mr-1 h-3 w-3" />
                This Period
              </Button>
              <Button
                variant={showYTD ? "default" : "outline"}
                size="sm"
                className="h-7 text-xs"
                onClick={() => setShowYTD(true)}
              >
                <CalendarRange className="mr-1 h-3 w-3" />
                Year to Date
              </Button>
            </div>
          </div>

          {/* First section: Taxable Pay and deductions */}
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Taxable Pay:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.taxablePay : calculations.taxablePay,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Tax:</span>
            <span className="text-right">
              {formatCurrency(showYTD ? ytd.tax : calculations.tax)}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Student Loan:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.studentLoan || 0 : calculations.studentLoan,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Postgraduate Loan:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD
                  ? ytd.postgraduateLoan || 0
                  : calculations.postgraduateLoan,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Employee NIC:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.nationalInsurance : calculations.employeeNIC,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Employer NIC:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.employerNIC || 0 : calculations.employerNIC,
              )}
            </span>
          </div>

          <Separator className="my-1" />

          {/* Net Pay */}
          <div className="grid grid-cols-2 text-emerald-700">
            <span>Net Pay:</span>
            <span className="text-right">
              {formatCurrency(showYTD ? ytd.netPay : calculations.netPay)}
            </span>
          </div>

          <Separator className="my-1" />

          {/* Additions and Deductions */}
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Net Pay Additions:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD
                  ? ytd.additionsToNetPay || 0
                  : calculations.additionsToNetPay,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Net Pay Deductions:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD
                  ? ytd.deductionsFromNetPay || 0
                  : calculations.deductionsFromNetPay,
              )}
            </span>
          </div>

          <Separator className="my-1" />

          {/* Pension */}
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Employee Pension:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.employeePension : calculations.employeePension,
              )}
            </span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Employer Pension:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.employerPension : calculations.employerPension,
              )}
            </span>
          </div>

          <Separator className="my-1" />

          {/* Take Home Pay */}
          <div className="grid grid-cols-2 text-sky-600">
            <span>Take Home Pay:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD
                  ? ytd.takeHomePay || ytd.netPay
                  : calculations.takeHomePay,
              )}
            </span>
          </div>

          <Separator className="my-1" />

          {/* Employer Cost */}
          <div className="grid grid-cols-2">
            <span className="text-muted-foreground">Employer Cost:</span>
            <span className="text-right">
              {formatCurrency(
                showYTD ? ytd.employerCost || 0 : calculations.employerCost,
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayslipSummary;
